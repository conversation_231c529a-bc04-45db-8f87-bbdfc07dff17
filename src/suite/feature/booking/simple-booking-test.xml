<!--Test suite for Simple Booking Test -->
<suite name="Simple Booking Test" verbose="1">
  <parameter name="browserToUse" value="chrome"/>
  <parameter name="headless" value="true"/>
  <parameter name="environment" value="http://localhost:58000"/>
  <parameter name="testSuiteName" value="booking/simple-booking-test"/>
  <test name="Simple Booking Test">
    <groups>
      <run>
        <include name="bookingEngine"/>
      </run>
    </groups>
    <classes>
      <class name="SimpleBookingTest"/>
    </classes>
  </test>
</suite>
