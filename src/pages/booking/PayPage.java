package pages.booking;

import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.testng.Assert;
import toolbox.Report;

import java.io.FileReader;
import java.io.IOException;

/**
 * Page object for the Payment page in the booking engine
 */
public class PayPage extends BasePage {
    // Page elements
    public String pageUrl = "/booking/pay";
    
    // Selectors
    private By depositPaymentMessage = By.cssSelector(".deposit-payment-message");
    private By paymentIframe = By.id("card-capture-for-cardUuid");
    private By barclaysIframe = By.id("barclays-iframe");
    private By cardholderNameField = By.id("cardholderName");
    private By cardNumberField = By.id("cardNumber");
    private By expiryMonthField = By.id("expiryMonth");
    private By expiryYearField = By.id("expiryYear");
    private By cscField = By.id("csc");
    private By submitButton = By.cssSelector(".submit-button");
    private By spinner = By.cssSelector(".spinner");
    
    /**
     * Constructor
     */
    public PayPage(WebDriver driver, Report report, String environment) {
        super(driver, report, environment);
    }
    
    /**
     * Assert that the deposit payment message contains the expected amount
     */
    public PayPage assertDepositPaymentMessage(String expectedAmount) {
        report.appendExport("INFO: Asserting deposit payment message contains amount: " + expectedAmount);
        WebElement message = wait.until(ExpectedConditions.visibilityOfElementLocated(depositPaymentMessage));
        String messageText = message.getText();
        Assert.assertTrue(messageText.contains(expectedAmount), 
                         "Expected deposit payment message to contain amount: " + expectedAmount + 
                         ", but got: " + messageText);
        return this;
    }
    
    /**
     * Fill in payment details from a JSON file
     */
    public PayPage fillPaymentDetails(String filePath) {
        report.appendExport("INFO: Filling in payment details from file: " + filePath);
        
        try {
            JSONParser parser = new JSONParser();
            JSONObject cardDetails = (JSONObject) parser.parse(new FileReader(filePath));
            
            String cardholderName = (String) cardDetails.get("cardholderName");
            String cardNumber = (String) cardDetails.get("cardNumber");
            String expiryMonth = (String) cardDetails.get("expiryMonth");
            String expiryYear = (String) cardDetails.get("expiryYear");
            String csc = (String) cardDetails.get("csc");
            
            // Switch to the payment iframe
            WebElement paymentFrame = wait.until(ExpectedConditions.visibilityOfElementLocated(paymentIframe));
            driver.switchTo().frame(paymentFrame);
            
            // Switch to the Barclays iframe
            WebElement barclaysFrame = wait.until(ExpectedConditions.visibilityOfElementLocated(barclaysIframe));
            driver.switchTo().frame(barclaysFrame);
            
            // Fill in card details
            wait.until(ExpectedConditions.visibilityOfElementLocated(cardholderNameField)).sendKeys(cardholderName);
            wait.until(ExpectedConditions.visibilityOfElementLocated(cardNumberField)).sendKeys(cardNumber);
            wait.until(ExpectedConditions.visibilityOfElementLocated(expiryMonthField)).sendKeys(expiryMonth);
            wait.until(ExpectedConditions.visibilityOfElementLocated(expiryYearField)).sendKeys(expiryYear);
            wait.until(ExpectedConditions.visibilityOfElementLocated(cscField)).sendKeys(csc);
            
            // Click the submit button
            WebElement button = wait.until(ExpectedConditions.elementToBeClickable(submitButton));
            button.click();
            
            // Switch back to the default content
            driver.switchTo().defaultContent();
            
            report.appendExport("INFO: Filled in payment details and submitted");
            
            // Wait for the payment to be processed
            wait.until(ExpectedConditions.invisibilityOfElementLocated(spinner));
        } catch (IOException | ParseException e) {
            report.appendExport("ERROR: Failed to read card details from file: " + e.getMessage());
        }
        
        return this;
    }
}
