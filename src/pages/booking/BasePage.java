package pages.booking;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import toolbox.Report;

import java.time.Duration;

/**
 * Base class for all page objects
 */
public class BasePage {
    protected WebDriver driver;
    protected WebDriverWait wait;
    protected Report report;
    protected String environment;
    
    /**
     * Constructor
     */
    public BasePage(WebDriver driver, Report report, String environment) {
        this.driver = driver;
        this.report = report;
        this.environment = environment;
        this.wait = new WebDriverWait(driver, Duration.ofSeconds(30));
    }
    
    /**
     * Validate that the page is loaded
     */
    public void validatePage() {
        report.appendExport("INFO: Validating page: " + this.getClass().getSimpleName());
    }
}
