package pages.booking;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import toolbox.Report;

/**
 * Page object for the Extras page in the booking engine
 */
public class ExtrasPage extends BasePage {
    // Page elements
    public String pageUrl = "/booking/extras";
    
    // Selectors
    private By continueButton = By.cssSelector(".continue-button");
    private By spinner = By.cssSelector(".spinner");
    
    /**
     * Constructor
     */
    public ExtrasPage(WebDriver driver, Report report, String environment) {
        super(driver, report, environment);
    }
    
    /**
     * Click the Continue button
     */
    public ExtrasPage clickContinueButton() {
        report.appendExport("INFO: Clicking Continue button on Extras page");
        wait.until(ExpectedConditions.invisibilityOfElementLocated(spinner));
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(continueButton));
        button.click();
        return this;
    }
}
