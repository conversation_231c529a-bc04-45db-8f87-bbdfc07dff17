package pages.booking;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import toolbox.Report;

/**
 * Page object for the Bookings page
 */
public class BookingsPage extends BasePage {
    // Page elements
    public String pageUrl = "/hotels/{hotelSlug}/bookings";
    
    /**
     * Constructor
     */
    public BookingsPage(WebDriver driver, Report report, String environment) {
        super(driver, report, environment);
    }
    
    /**
     * Open the Bookings page for a specific hotel
     */
    public BookingsPage open(String hotelSlug) {
        String url = environment + pageUrl.replace("{hotelSlug}", hotelSlug);
        driver.get(url);
        report.appendExport("INFO: Opened Bookings page for hotel: " + hotelSlug);
        return this;
    }
    
    /**
     * Click on a booking with the specified reference
     */
    public BookingsPage clickBooking(String bookingReference) {
        report.appendExport("INFO: Clicking on booking with reference: " + bookingReference);
        WebElement booking = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("//*[contains(text(),'" + bookingReference + "')]")));
        booking.click();
        return this;
    }
}
