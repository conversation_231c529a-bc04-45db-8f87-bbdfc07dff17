package pages.booking;

import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import toolbox.Report;

import java.io.FileReader;
import java.io.IOException;

/**
 * Page object for the Guest Details page in the booking engine
 */
public class GuestDetailsPage extends BasePage {
    // Page elements
    public String pageUrl = "/booking/guest-details";
    
    // Selectors
    private By firstNameField = By.id("firstName");
    private By lastNameField = By.id("lastName");
    private By emailField = By.id("email");
    private By phoneField = By.id("phone");
    private By postcodeLookupButton = By.cssSelector(".postcode-lookup-button");
    private By addressDropdown = By.cssSelector(".address-dropdown");
    private By firstAddressOption = By.cssSelector(".address-option:first-child");
    private By continueButton = By.cssSelector(".continue-button");
    private By spinner = By.cssSelector(".spinner");
    
    /**
     * Constructor
     */
    public GuestDetailsPage(WebDriver driver, Report report, String environment) {
        super(driver, report, environment);
    }
    
    /**
     * Fill in guest details from a JSON file
     */
    public GuestDetailsPage fillGuestDetails(String filePath) {
        report.appendExport("INFO: Filling in guest details from file: " + filePath);
        
        try {
            JSONParser parser = new JSONParser();
            JSONObject guestDetails = (JSONObject) parser.parse(new FileReader(filePath));
            
            String firstName = (String) guestDetails.get("firstName");
            String lastName = (String) guestDetails.get("lastName");
            String email = (String) guestDetails.get("email");
            String phone = (String) guestDetails.get("phone");
            
            wait.until(ExpectedConditions.visibilityOfElementLocated(firstNameField)).sendKeys(firstName);
            wait.until(ExpectedConditions.visibilityOfElementLocated(lastNameField)).sendKeys(lastName);
            wait.until(ExpectedConditions.visibilityOfElementLocated(emailField)).sendKeys(email);
            wait.until(ExpectedConditions.visibilityOfElementLocated(phoneField)).sendKeys(phone);
            
            report.appendExport("INFO: Filled in guest details - Name: " + firstName + " " + lastName + ", Email: " + email);
        } catch (IOException | ParseException e) {
            report.appendExport("ERROR: Failed to read guest details from file: " + e.getMessage());
        }
        
        return this;
    }
    
    /**
     * Click the Postcode Lookup button
     */
    public GuestDetailsPage selectPostcodeLookupButton() {
        report.appendExport("INFO: Clicking Postcode Lookup button");
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(postcodeLookupButton));
        button.click();
        return this;
    }
    
    /**
     * Select the first address from the dropdown
     */
    public GuestDetailsPage selectFirstAddressFromDropdown() {
        report.appendExport("INFO: Selecting first address from dropdown");
        wait.until(ExpectedConditions.visibilityOfElementLocated(addressDropdown));
        WebElement option = wait.until(ExpectedConditions.elementToBeClickable(firstAddressOption));
        option.click();
        return this;
    }
    
    /**
     * Click the Continue button
     */
    public GuestDetailsPage clickContinueButton() {
        report.appendExport("INFO: Clicking Continue button on Guest Details page");
        wait.until(ExpectedConditions.invisibilityOfElementLocated(spinner));
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(continueButton));
        button.click();
        return this;
    }
}
