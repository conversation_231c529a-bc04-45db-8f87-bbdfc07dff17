package pages.booking;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.testng.Assert;
import toolbox.Report;

/**
 * Page object for the Calendar page
 */
public class CalendarPage extends BasePage {
    // Page elements
    public String pageUrl = "/hotels/{hotelSlug}/calendar?date={date}";
    
    /**
     * Constructor
     */
    public CalendarPage(WebDriver driver, Report report, String environment) {
        super(driver, report, environment);
    }
    
    /**
     * Open the Calendar page for a specific hotel and date
     */
    public CalendarPage open(String hotelSlug, String date) {
        String url = environment + pageUrl.replace("{hotelSlug}", hotelSlug).replace("{date}", date);
        driver.get(url);
        report.appendExport("INFO: Opened Calendar page for hotel: " + hotelSlug + " and date: " + date);
        return this;
    }
    
    /**
     * Assert that a booking with the specified reference exists on the calendar
     */
    public CalendarPage assertBookingExists(String bookingReference) {
        report.appendExport("INFO: Asserting booking with reference: " + bookingReference + " exists on calendar");
        WebElement booking = wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath("//*[contains(text(),'" + bookingReference + "')]")));
        Assert.assertTrue(booking.isDisplayed(), "Expected booking with reference: " + bookingReference + " to be displayed on calendar");
        return this;
    }
}
