package pages.booking;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import toolbox.Report;

/**
 * Page object for the Your Stay page in the booking engine
 */
public class YourStayPage extends BasePage {
    // Page elements
    public String pageUrl = "/booking";
    
    // Selectors
    private By selectDatesButton = By.cssSelector(".select-dates-button");
    private By currentDateCell = By.cssSelector(".flatpickr-day.today");
    private By nextDayDateCell = By.cssSelector(".flatpickr-day.today + .flatpickr-day");
    private By searchButton = By.cssSelector(".search-button");
    private By occupancySearchButton = By.cssSelector(".occupancy-search-button");
    private By twinRoomBox = By.cssSelector(".twin-room-box");
    private By addRatePlanButton = By.cssSelector(".add-rate-plan-button");
    private By continueButton = By.cssSelector(".continue-button");
    private By spinner = By.cssSelector(".spinner");
    
    /**
     * Constructor
     */
    public YourStayPage(WebDriver driver, Report report, String environment) {
        super(driver, report, environment);
    }
    
    /**
     * Open the Your Stay page for a specific hotel
     */
    public YourStayPage open(String hotelSlug) {
        driver.get(environment + "/booking/" + hotelSlug);
        report.appendExport("INFO: Opened Your Stay page for hotel: " + hotelSlug);
        return this;
    }
    
    /**
     * Click the Select Dates button
     */
    public YourStayPage clickSelectDates() {
        report.appendExport("INFO: Clicking Select Dates button");
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(selectDatesButton));
        button.click();
        return this;
    }
    
    /**
     * Select the current date in the calendar
     */
    public YourStayPage selectCurrentDate() {
        report.appendExport("INFO: Selecting current date");
        WebElement dateCell = wait.until(ExpectedConditions.elementToBeClickable(currentDateCell));
        dateCell.click();
        return this;
    }
    
    /**
     * Select the next day date in the calendar
     */
    public YourStayPage selectNextDayDate() {
        report.appendExport("INFO: Selecting next day date");
        WebElement dateCell = wait.until(ExpectedConditions.elementToBeClickable(nextDayDateCell));
        dateCell.click();
        return this;
    }
    
    /**
     * Click the Search button
     */
    public YourStayPage clickSearchButton() {
        report.appendExport("INFO: Clicking Search button");
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(searchButton));
        button.click();
        return this;
    }
    
    /**
     * Click the Occupancy Search button
     */
    public YourStayPage clickOccupancySearchButton() {
        report.appendExport("INFO: Clicking Occupancy Search button");
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(occupancySearchButton));
        button.click();
        return this;
    }
    
    /**
     * Select the Twin Room
     */
    public YourStayPage selectTwinRoom() {
        report.appendExport("INFO: Selecting Twin Room");
        wait.until(ExpectedConditions.invisibilityOfElementLocated(spinner));
        WebElement roomBox = wait.until(ExpectedConditions.elementToBeClickable(twinRoomBox));
        WebElement addButton = roomBox.findElement(addRatePlanButton);
        addButton.click();
        return this;
    }
    
    /**
     * Click the Continue button
     */
    public YourStayPage clickContinueButton() {
        report.appendExport("INFO: Clicking Continue button");
        wait.until(ExpectedConditions.invisibilityOfElementLocated(spinner));
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(continueButton));
        button.click();
        return this;
    }
}
