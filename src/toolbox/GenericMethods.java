package toolbox;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;

/**
 * Generic methods for interacting with the application
 */
public class GenericMethods {
    private Report report;
    private WebDriver driver;
    private WebDriverWait wait;
    
    /**
     * Constructor
     */
    public GenericMethods(Report report) {
        this.report = report;
    }
    
    /**
     * Set the WebDriver
     */
    public void setDriver(WebDriver driver) {
        this.driver = driver;
        this.wait = new WebDriverWait(driver, Duration.ofSeconds(30));
    }
    
    /**
     * Wait for an element containing the specified text to be present
     */
    public void waitForElementWithText(String text) {
        report.appendExport("INFO: Waiting for element with text: " + text);
        wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//*[contains(text(),'" + text + "')]")));
    }
    
    /**
     * Wait for an element with the specified ID to be present
     */
    public void waitForElementWithId(String id) {
        report.appendExport("INFO: Waiting for element with ID: " + id);
        wait.until(ExpectedConditions.presenceOfElementLocated(By.id(id)));
    }
    
    /**
     * Wait for an element with the specified CSS selector to be present
     */
    public void waitForElementWithCss(String css) {
        report.appendExport("INFO: Waiting for element with CSS selector: " + css);
        wait.until(ExpectedConditions.presenceOfElementLocated(By.cssSelector(css)));
    }
    
    /**
     * Wait for an element with the specified XPath to be present
     */
    public void waitForElementWithXpath(String xpath) {
        report.appendExport("INFO: Waiting for element with XPath: " + xpath);
        wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath(xpath)));
    }
    
    /**
     * Wait for an element with the specified ID to be clickable
     */
    public void waitForElementToBeClickable(String id) {
        report.appendExport("INFO: Waiting for element with ID to be clickable: " + id);
        wait.until(ExpectedConditions.elementToBeClickable(By.id(id)));
    }
    
    /**
     * Wait for an element with the specified CSS selector to be clickable
     */
    public void waitForElementToBeClickableByCss(String css) {
        report.appendExport("INFO: Waiting for element with CSS selector to be clickable: " + css);
        wait.until(ExpectedConditions.elementToBeClickable(By.cssSelector(css)));
    }
    
    /**
     * Wait for an element with the specified XPath to be clickable
     */
    public void waitForElementToBeClickableByXpath(String xpath) {
        report.appendExport("INFO: Waiting for element with XPath to be clickable: " + xpath);
        wait.until(ExpectedConditions.elementToBeClickable(By.xpath(xpath)));
    }
}
