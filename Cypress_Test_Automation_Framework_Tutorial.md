# Cypress Test Automation Framework Tutorial

## Introduction

This tutorial provides a comprehensive guide to the Cypress test automation framework used for testing the hotel booking system. The framework follows the Page Object Model (POM) design pattern and includes specialized components for handling complex scenarios like payment processing with iframes and 3D Secure authentication. This tutorial will help you understand the architecture, key components, and how to create and run tests.

## Table of Contents

1. [Framework Architecture](#framework-architecture)
2. [Page Object Model Implementation](#page-object-model-implementation)
3. [Test Data Management](#test-data-management)
4. [Test Organization](#test-organization)
5. [Custom Commands and Utilities](#custom-commands-and-utilities)
6. [Handling Iframes and Payment Forms](#handling-iframes-and-payment-forms)
7. [3D Secure Challenge Testing](#3d-secure-challenge-testing)
8. [Running Tests](#running-tests)
9. [Best Practices](#best-practices)

## Framework Architecture

The Cypress test automation framework is structured as follows:

```
cypress/
├── e2e/                  # Test files organized by feature
│   ├── 001 Default/      # Basic tests like login
│   ├── booking-engine/   # Booking flow tests
│   ├── features/         # Feature-specific tests
│   └── ...
├── fixtures/             # Test data
│   ├── accounts.js       # User account data
│   ├── cards/            # Payment card test data
│   └── ...
├── pages/                # Simple page objects
├── support/              # Support files
│   ├── commands/         # Custom Cypress commands
│   ├── forms/            # Form interaction helpers
│   ├── functions/        # Utility functions
│   ├── pageObjectModel/  # Advanced page objects
│   │   ├── Components/   # Reusable page components
│   │   └── Pages/        # Page objects
│   └── e2e.js            # Main support file
└── cypress.config.js     # Cypress configuration
```

## Page Object Model Implementation

The framework implements the Page Object Model in two ways:

### 1. Simple Page Objects

Basic page objects are defined in the `cypress/pages/` directory:

```javascript
class guestLogin {
  elements = {
    username: cy.get(''),
    password: cy.get(''),
    login: cy.get('')
  }

  enterUsername(username) {
    this.elements.username().clear();
    this.elements.username().type(username);
  }

  enterPassword(password) {
    this.elements.password().clear();
    this.elements.password().type(password);
  }

  clickLogin() {
    this.elements.login().click()
  }
}

export default guestLogin;
```

### 2. Advanced Page Objects

More complex page objects are defined in the `cypress/support/pageObjectModel/` directory:

```javascript
export const bookingHubPage = {
  selectors: {
    addPaymentButton: '.add-payment-button',
    paymentStatusLabel: '.payment-status',
    creditCardInfo: '.credit-card-info'
  },

  clickAddPaymentButton() {
    cy.get(this.selectors.addPaymentButton).click();
    return this;
  },

  assertCreditCardNotAttached() {
    cy.get(this.selectors.creditCardInfo).should('not.exist');
    return this;
  },

  assertPaymentStatus(status) {
    cy.get(this.selectors.paymentStatusLabel).should('contain', status);
    return this;
  }
};
```

## Test Data Management

Test data is managed using JavaScript modules in the `cypress/fixtures/` directory:

```javascript
// cypress/fixtures/accounts.js
export default {
  cypress_a: {
    hotelier: {
      manager: {
        email: '<EMAIL>',
        password: 'password123'
      }
    },
    slug: 'cardiff',
    title: 'Cardiff Plaza'
  }
};

// cypress/fixtures/cards/paymentDetailsBarclays.js
export const validBarclaysCards = {
  visaCredit: {
    cardHolderName: 'Test User',
    cardNumber: '****************',
    expirationMonth: '12',
    expirationYear: '2025',
    ccv: '123',
    challengePassword: 'password'
  }
};
```

## Test Organization

Tests are organized by feature and functionality:

```javascript
// cypress/e2e/001 Default/001_login.cy.js
import accounts from '../../fixtures/accounts';
import TestFilters from '../../support/filterTests';

TestFilters(['P2Sanity'], () => {
  describe('PMS Staff Login', () => {
    it('can log in as Staff', () => {
      cy.fn_login('staff',
        Cypress.env('staff_email'),
        Cypress.env('staff_password')
      )
      cy.get('.message.success').should('contain', 'Logged in')
      cy.location('pathname').should('eq', '/hotels')
    })

    // More test cases...
  })
})
```

## Custom Commands and Utilities

The framework includes many custom commands and utilities:

```javascript
// cypress/support/functions/login.js
Cypress.Commands.add('fn_login', (type, email, password, slug = null, options = {}) => {
  const defaultOptions = { failOnStatusCode: true };
  const mergedOptions = { ...defaultOptions, ...options };

  if (type === 'staff') {
    cy.visit('/staff/login', mergedOptions);
    cy.get('#email').type(email);
    cy.get('#password').type(password);
    cy.get('button[type="submit"]').click();
  } else if (type === 'hotelier') {
    cy.visit('/login/', mergedOptions);
    cy.get('#email').type(email);
    cy.get('#password').type(password);
    cy.get('button[type="submit"]').click();
  }
});
```

## Handling Iframes and Payment Forms

One of the most complex parts of the framework is handling payment forms in iframes:

```javascript
// Simplified example from cypress/support/pageObjectModel/Components/paymentIFrame.js
export const paymentIframe = {
  selectors: {
    iframe: '#card-capture-for-cardUuid',
    barclaysIFrame: "#barclays-iframe",
    barclaysIframeCardholderName:'#cardholderName',
    barclaysIframeCardNumber:'#cardNumber',
    barclaysExpiryMonth: '#expiryMonth',
    barclaysExpiryYear: '#expiryYear',
    barclaysSecurityCode: '#csc'
  },

  fillBarclaysDetails: (card) => {
    // Wait for the iframe to be fully loaded
    cy.wait(3000);

    // Handle nested iframe scenario
    cy.get('iframe#card-capture-for-cardUuid').then($outerIframe => {
      cy.iframe('#card-capture-for-cardUuid').then($outerIframeBody => {
        // Check if there's a nested iframe with ID 'barclays-iframe'
        const nestedIframe = $outerIframeBody.find('#barclays-iframe');

        if (nestedIframe.length > 0) {
          // Use jQuery to access and fill the nested iframe
          cy.window().then(win => {
            const $nestedIframe = win.jQuery('#barclays-iframe');

            // Fill in the form fields
            const $nameField = win.jQuery($nestedIframe[0]).contents().find('#cardholderName');
            const $cardField = win.jQuery($nestedIframe[0]).contents().find('#cardNumber');
            const $monthSelect = win.jQuery($nestedIframe[0]).contents().find('#expiryMonth');
            const $yearSelect = win.jQuery($nestedIframe[0]).contents().find('#expiryYear');
            const $securityField = win.jQuery($nestedIframe[0]).contents().find('#csc');

            $nameField.val(card.cardHolderName).trigger('input');
            $cardField.val(card.cardNumber).trigger('input');
            $monthSelect.val(card.expirationMonth).trigger('change');
            $yearSelect.val(card.expirationYear).trigger('change');
            $securityField.val(card.ccv).trigger('input');

            // Submit the form
            const $submitButton = win.jQuery($nestedIframe[0]).contents().find('button[type="submit"]');
            $submitButton.click();
          });
        }
      });
    });
  }
};
```

## 3D Secure Challenge Testing

The framework includes specialized handling for 3D Secure authentication challenges:

```javascript
// Simplified example from cypress/support/pageObjectModel/Components/paymentIFrame.js
handle3DSChallenge: (card) => {
  // Wait for the 3DS iframe to load
  cy.wait(3000);

  // Define possible iframe selectors for different payment providers
  const possibleIframeSelectors = [
    '#challengeFrame',        // Standard 3DS iframe
    '#Cardinal-CCA-IFrame',   // Cardinal Commerce iframe
    'iframe[name*="3ds"]',    // Any iframe with '3ds' in the name
    'iframe[id*="challenge"]' // Any iframe with 'challenge' in the ID
  ];

  // Try each possible iframe selector
  for (const iframeSelector of possibleIframeSelectors) {
    cy.get('body').then($body => {
      if ($body.find(iframeSelector).length > 0) {
        // Found a matching iframe, try to interact with it
        cy.iframe(iframeSelector).then($iframe => {
          // Try different selectors for password field
          const passwordField = $iframe.find('#password, input[type="password"]');
          if (passwordField.length > 0) {
            cy.iframe(iframeSelector).find('#password, input[type="password"]')
              .type(card.challengePassword);

            // Try different selectors for submit button
            cy.iframe(iframeSelector).find('#txtButton, button[type="submit"], input[type="submit"]')
              .click();

            // Break the loop after successful interaction
            return false;
          }
        });
      }
    });
  }

  // Wait for the challenge to complete
  cy.wait(5000);
}
```

## Running Tests

Tests can be run using the Cypress Test Runner or from the command line:

```bash
# Open Cypress Test Runner
npx cypress open

# Run tests in headless mode
npx cypress run

# Run specific test file
npx cypress run --spec "cypress/e2e/001 Default/001_login.cy.js"

# Run tests with specific tags
npx cypress run --env grepTags=P2Sanity
```

## Best Practices

1. **Use Page Objects**: Encapsulate page elements and actions in page objects.
2. **Organize Test Data**: Keep test data in fixtures and separate from test logic.
3. **Handle Iframes Properly**: Use the `cypress-iframe` plugin and custom helpers for iframe interactions.
4. **Implement Robust Selectors**: Use multiple selector strategies for better resilience.
5. **Add Proper Waits**: Use explicit waits instead of fixed timeouts when possible.
6. **Handle Errors Gracefully**: Add error handling for complex operations like payment processing.
7. **Use Test Filters**: Organize tests with tags for better test suite management.
8. **Keep Tests Independent**: Each test should be independent and not rely on other tests.
9. **Document Complex Logic**: Add comments to explain complex test logic, especially for iframe handling.
10. **Implement Chainable Methods**: Use method chaining for more readable test code.

## Payment Testing Strategies

The framework includes specialized strategies for testing payment flows:

### 1. Direct Payment Form Testing

For testing payment forms directly:

```javascript
// Example from a payment test
it('Verify user can complete payment with Barclays gateway', () => {
  // Navigate to payment page
  bookingHubPage.clickAddPaymentButton();

  // Fill payment amount
  cy.get('input[name*="amount"]').clear().type('10');

  // Submit the form
  payByLinkForm.submitAndOpenPBL(paymentAmount);

  // Fill card details in the iframe
  paymentIframe.fillBarclaysDetails(card);

  // Verify successful payment
  payByLinkPage.assertSuccessfulPayment(paymentAmountFormatted);
});
```

### 2. 3D Secure Challenge Testing

For testing cards that require 3D Secure authentication:

```javascript
// Example using a card that requires 3DS challenge
it('Verify user can complete 3DS challenge payment', () => {
  // Navigate to payment page and fill amount
  bookingHubPage.clickAddPaymentButton();
  cy.get('input[name*="amount"]').clear().type('10');
  payByLinkForm.submitAndOpenPBL(paymentAmount);

  // Fill card details and handle 3DS challenge
  paymentIframe.fillBarclaysDetailsWithChallenge(card);

  // Verify successful payment
  payByLinkPage.assertSuccessfulPayment(paymentAmountFormatted);
});
```

### 3. Payment Gateway Selection

The framework supports testing multiple payment gateways:

```javascript
// Example of gateway-specific test
it('Verify payment with JudoPay gateway', () => {
  // Select JudoPay gateway if multiple options are available
  cy.get('input[value="judopay"]').check();

  // Continue with payment flow
  // ...
});
```

### 4. Payment Error Handling

Tests for payment error scenarios:

```javascript
// Example of testing payment failure
it('Verify appropriate error for declined card', () => {
  // Use a card that will be declined
  const declinedCard = invalidCards.declined;

  // Fill payment form
  paymentIframe.fillJudoPayDetails(declinedCard);

  // Verify error message
  cy.get('.payment-error').should('contain', 'Your card was declined');
});
```

## Conclusion

The Cypress test automation framework provides a robust solution for testing the hotel booking system, with specialized handling for complex scenarios like payment processing and 3D Secure authentication. By following the patterns and practices outlined in this tutorial, you can create maintainable and reliable automated tests for the application.
