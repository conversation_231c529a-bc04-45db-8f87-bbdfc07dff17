# HLS PMS Docker Environment Troubleshooting Guide

This guide documents the resolution of complex configuration issues encountered when setting up the HLS PMS application in a Docker environment.

## Overview

The HLS PMS application is a microservices-based hotel management system that uses:
- 27 Docker containers orchestrated with docker-compose
- MySQL database for data persistence
- Redis for caching and session storage
- LocalStack for AWS service emulation (SSM Parameter Store)
- confd for dynamic configuration management
- Apache web server with PHP 7.4

## Common Issues and Solutions

### 1. Cypress Installation Error

**Problem**: `npm run cypress:install` script doesn't exist
```bash
npm ERR! Missing script: "cypress:install"
```

**Solution**: Use the correct command to install Cypress binary
```bash
npx cypress install
```

### 2. VS Code Settings JSON Syntax Error

**Problem**: Missing comma in settings.json causing parse errors

**Solution**: Add missing comma between JSON properties
```json
{
    "workbench.colorTheme": "Tomorrow Night Blue",
    "remote.autoForwardPorts": "hybrid"  // <- Add comma here if more properties follow
}
```

### 3. Docker Volume Mount Issues (WSL2)

**Problem**: Read-only filesystem errors when containers try to write to mounted volumes
```
mkdir: cannot create directory '/var/www/hotel/app/storage/cache': Read-only file system
```

**Root Cause**: WSL2 filesystem limitations with Docker volume mounts

**Solution**: Create temporary directories and update docker-compose.yml
```bash
# Create temporary directories
mkdir -p /tmp/hotel-storage/{cache,logs,meta,sessions,views}
mkdir -p /tmp/hotel-xdebug

# Update docker-compose.yml volume mounts
volumes:
  - ./:/var/www/hotel
  - /tmp/hotel-xdebug:/var/log/xdebug
  - /tmp/hotel-storage:/var/www/hotel/app/storage
```

### 4. MongoDB Port Conflict

**Problem**: Port 27017 already in use
```
Error starting userland proxy: listen tcp4 0.0.0.0:27017: bind: address already in use
```

**Solution**: Stop conflicting containers
```bash
docker ps | grep 27017
docker stop <conflicting-container-id>
```

### 5. Database Connection Errors

**Problem**: Application can't connect to MySQL database
```
SQLSTATE[HY000] [1049] Unknown database 'pms'
SQLSTATE[HY000] [1045] Access denied for user 'local_pms'@'%'
```

**Solution**: Create required database and user
```bash
# Connect to MySQL container
docker exec -it hotel-database-master-1 mysql -u root -psecret

# Create database and user
CREATE DATABASE IF NOT EXISTS local_pms;
CREATE USER IF NOT EXISTS 'local_pms'@'%' IDENTIFIED BY 'secret';
GRANT ALL PRIVILEGES ON local_pms.* TO 'local_pms'@'%';
FLUSH PRIVILEGES;
```

### 6. Composer Installation Issues

**Problem**: Multiple composer-related errors including filesystem permissions and GitHub authentication

**Solutions**:

**Filesystem Permissions**:
```bash
sudo chown -R danj:danj /var/www/hotel
```

**GitHub Authentication**:
```bash
# Configure Git credentials
git config --global user.email "<EMAIL>"
git config --global user.name "Your Name"

# Set up GitHub token for private repositories
composer config --global github-oauth.github.com YOUR_GITHUB_TOKEN
```

**Package Installation**:
```bash
cd /var/www/hotel
composer install --no-dev --optimize-autoloader
```

### 7. Redis Connection Error (Primary Issue)

**Problem**: Application returns 500 error with Redis connection failures
```
Connection refused [tcp://redis:6379]
```

**Root Cause**: Missing environment variables and SSM parameters for Redis configuration

**Solution**: Configure Redis environment variables
```bash
# Add to devops/docker/common.env
HLS_PMS_REDIS_HOST=redis
HLS_PMS_REDIS_PORT=6379
HLS_PMS_REDIS_DATABASE=0
```

### 8. SSM Parameter Configuration Issues (Critical)

**Problem**: confd fails to generate configuration files
```
error calling getv: key does not exist: /databases/pms/name
```

**Root Cause**: Missing AWS SSM parameters in LocalStack and incorrect parameter prefixes

**Complete Solution**:

**Step 1**: Identify the parameter prefix used by the application
```bash
# Check the launch script
docker exec hotel-application-1 cat /usr/bin/launch-application
# Shows: confd -onetime -backend ssm -prefix ${tier}
# Where tier=${Tier:-local}, so prefix is "/local"
```

**Step 2**: Create all required SSM parameters with correct prefix
```bash
# Database parameters
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/name" --value "local_pms" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/users/pms/username" --value "root" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/users/pms/password" --value "secret" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/users/pms_reports/username" --value "root" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/users/pms_reports/password" --value "secret" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566

# Database host parameters
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/databases/legacy/master/host" --value "database-master" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/databases/legacy/master/port" --value "3306" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/databases/legacy/slave/host" --value "database-master" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/databases/legacy/slave/port" --value "3306" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite

# Redis parameters
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/cache/redis/host" --value "redis" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/cache/redis/port" --value "6379" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
```

**Step 3**: Configure confd to use LocalStack endpoint
```bash
# Backup original launch script
docker exec hotel-application-1 cp /usr/bin/launch-application /usr/bin/launch-application.backup

# Create updated launch script
docker exec hotel-application-1 bash -c 'cat > /tmp/launch-application << "EOF"
#!/bin/bash

# Get the tier
tier=${Tier:-local}

# Set AWS endpoint for LocalStack
export AWS_ENDPOINT_URL=${SSM_ENDPOINT_URL:-http://localstack:4566}

# Setup the configs from SSM
confd -onetime -backend ssm -prefix ${tier}

# Apache listen
exec apache2-foreground
EOF'

# Replace the launch script
docker exec hotel-application-1 chmod +x /tmp/launch-application
docker exec hotel-application-1 cp /tmp/launch-application /usr/bin/launch-application
docker exec hotel-application-1 chmod +x /usr/bin/launch-application
```

**Step 4**: Restart application container
```bash
docker-compose restart application
```

## Verification Steps

### 1. Check Container Status
```bash
docker-compose ps
# All 27 containers should be running
```

### 2. Verify Database Connection
```bash
docker exec hotel-database-master-1 mysql -u root -psecret -e "SHOW DATABASES;"
# Should show local_pms database
```

### 3. Test Application
```bash
curl -s -o /dev/null -w "%{http_code}" http://localhost:58000/staff/login
# Should return 200
```

### 4. Check Configuration Files
```bash
# Verify database configuration was generated
docker exec hotel-application-1 cat app/config/environment/database.php | grep -A 5 "'database'"
# Should show 'database' => 'local_pms'
```

### 5. Verify SSM Parameters
```bash
# List all parameters
docker exec hotel-localstack-1 aws ssm describe-parameters --region eu-west-1 --endpoint-url http://localhost:4566

# Get specific parameter
docker exec hotel-localstack-1 aws ssm get-parameter --name "/local/databases/pms/name" --region eu-west-1 --endpoint-url http://localhost:4566
```

## Key Technical Concepts

### confd Configuration Management
- **Purpose**: Dynamically generates configuration files from backend data stores
- **Backend**: AWS SSM Parameter Store (via LocalStack)
- **Templates**: Located in `/etc/confd/templates/`
- **Configuration**: Located in `/etc/confd/conf.d/`
- **Prefix**: Uses tier-based prefixes (e.g., `/local/` for local development)

### LocalStack SSM Integration
- **Endpoint**: http://localstack:4566
- **Region**: eu-west-1 (important for parameter retrieval)
- **Authentication**: Uses container's AWS credentials/environment

### Docker Networking
- **Service Discovery**: Containers communicate using service names as hostnames
- **Redis**: Accessible at `redis:6379`
- **Database**: Accessible at `database-master:3306`
- **LocalStack**: Accessible at `localstack:4566`

## Environment Files

### devops/docker/common.env
```bash
# Database Configuration
HLS_PMS_DB_HOST=database-master
HLS_PMS_DB_PORT=3306
HLS_PMS_DB_DATABASE=local_pms
HLS_PMS_DB_USERNAME=root
HLS_PMS_DB_PASSWORD=secret

# Redis Configuration
HLS_PMS_REDIS_HOST=redis
HLS_PMS_REDIS_PORT=6379
HLS_PMS_REDIS_DATABASE=0
```

## Troubleshooting Commands

### Container Logs
```bash
# Application logs
docker logs hotel-application-1 --tail 20

# Database logs
docker logs hotel-database-master-1 --tail 20

# Redis logs
docker logs hotel-redis-1 --tail 20

# LocalStack logs
docker logs hotel-localstack-1 --tail 20
```

### Interactive Debugging
```bash
# Access application container
docker exec -it hotel-application-1 bash

# Access database container
docker exec -it hotel-database-master-1 bash

# Test Redis connection
docker exec -it hotel-redis-1 redis-cli ping
```

### Configuration Verification
```bash
# Check confd templates
docker exec hotel-application-1 find /etc/confd -name "*.tmpl" | head -5

# Check confd configuration
docker exec hotel-application-1 find /etc/confd -name "*.toml" | head -5

# Manual confd run with debugging
docker exec -e AWS_REGION=eu-west-1 -e AWS_ENDPOINT_URL=http://localstack:4566 hotel-application-1 confd -onetime -backend ssm -prefix local
```

## Success Indicators

When everything is working correctly:

1. **HTTP Status**: `curl http://localhost:58000/staff/login` returns 200
2. **Container Count**: All 27 containers running
3. **Database**: `local_pms` database exists with proper user permissions
4. **Configuration**: Database config shows correct connection details
5. **Logs**: No confd errors in application startup logs
6. **Browser**: Staff login page loads without errors

## Common Pitfalls

1. **Parameter Prefix**: Always use `/local/` prefix for local development
2. **Region Consistency**: Use `eu-west-1` for all LocalStack operations
3. **Database Names**: Use `local_pms` not `pms` for the database name
4. **Container Restart**: Always restart application container after SSM parameter changes
5. **File Permissions**: Ensure proper ownership of mounted volumes in WSL2
6. **GitHub Authentication**: Required for private composer packages

## Additional Resources

- **Application URL**: http://localhost:58000
- **Database**: MySQL on port 3306 (internal), accessible via `database-master` hostname
- **Redis**: Available on port 6379 (internal), accessible via `redis` hostname
- **LocalStack**: AWS services on port 4566, accessible via `localstack` hostname

## Quick Setup Script

For future setups, you can use this script to configure all SSM parameters at once:

```bash
#!/bin/bash
# HLS PMS SSM Parameter Setup Script

echo "Setting up SSM parameters for HLS PMS..."

# Database parameters
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/name" --value "local_pms" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/users/pms/username" --value "root" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/users/pms/password" --value "secret" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/users/pms_reports/username" --value "root" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/databases/pms/users/pms_reports/password" --value "secret" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite

# Database host parameters
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/databases/legacy/master/host" --value "database-master" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/databases/legacy/master/port" --value "3306" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/databases/legacy/slave/host" --value "database-master" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/databases/legacy/slave/port" --value "3306" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite

# Redis parameters
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/cache/redis/host" --value "redis" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite
docker exec hotel-localstack-1 aws ssm put-parameter --name "/local/resources/cache/redis/port" --value "6379" --type "String" --region eu-west-1 --endpoint-url http://localhost:4566 --overwrite

echo "SSM parameters configured successfully!"
echo "Restart the application container: docker-compose restart application"
```

This troubleshooting guide should help resolve the most common issues encountered when setting up the HLS PMS Docker environment.
