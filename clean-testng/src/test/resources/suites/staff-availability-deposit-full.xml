<!--Test suite for Staff Availability Deposit Full Test -->
<suite name="Staff Availability Deposit Full Test" verbose="1">
  <parameter name="browserToUse" value="chrome"/>
  <parameter name="headless" value="true"/>
  <parameter name="environment" value="http://localhost:58000"/>
  <parameter name="testSuiteName" value="booking/staff-availability-deposit-full"/>
  <test name="Staff Availability Deposit Full Test">
    <groups>
      <run>
        <include name="bookingEngine"/>
      </run>
    </groups>
    <classes>
      <class name="booking.StaffAvailabilityDepositFullTest"/>
    </classes>
  </test>
</suite>
