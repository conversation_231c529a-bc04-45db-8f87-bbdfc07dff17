# Guide to Creating Effective Prompts for Framework Documentation

## Introduction

This guide provides strategies for crafting effective prompts to generate comprehensive documentation for test automation frameworks. It focuses on combining multiple analysis approaches, including PocketFlow tutorial generation and custom repository scanning, to create more complete and insightful documentation.

> **Key Insight**: Our experiments show that removing the `--max-abstractions` limit in PocketFlow produces significantly more comprehensive tutorials. In our test case, we went from 3 abstractions to 7 abstractions, with much more granular breakdowns of concepts and deeper technical explanations. For the most comprehensive documentation, we recommend running PocketFlow without abstraction limits.

## Table of Contents

1. [Understanding Documentation Needs](#understanding-documentation-needs)
2. [Multi-Tool Approach](#multi-tool-approach)
3. [PocketFlow Tutorial Generation](#pocketflow-tutorial-generation)
4. [Custom Repository Scanning](#custom-repository-scanning)
5. [Combining Insights](#combining-insights)
6. [Effective Prompt Templates](#effective-prompt-templates)
7. [Iterative Refinement](#iterative-refinement)
8. [Best Practices](#best-practices)

## Understanding Documentation Needs

Before crafting prompts, identify the specific documentation needs:

- **Framework Overview**: High-level architecture and components
- **User Guide**: How to use the framework (for testers)
- **Developer Guide**: How to extend the framework (for developers)
- **Specialized Guides**: For complex areas like payment testing
- **Best Practices**: Guidelines for effective framework usage

Different audiences need different documentation types, so tailor your prompts accordingly.

## Multi-Tool Approach

Using multiple analysis tools provides complementary insights:

1. **PocketFlow Tutorial Generator**: Provides structured, beginner-friendly explanations of key concepts
2. **Custom Repository Scanning**: Offers quantitative insights into framework components
3. **Manual Code Analysis**: Provides detailed understanding of specific components
4. **Existing Documentation**: Incorporates current knowledge

This multi-tool approach creates more comprehensive documentation than any single method.

## PocketFlow Tutorial Generation

### When to Use PocketFlow

PocketFlow is most effective for:
- Creating beginner-friendly tutorials
- Explaining key concepts with analogies
- Providing a structured learning path
- Generating code examples with explanations

### Effective PocketFlow Prompts

When running PocketFlow, consider these strategies:

1. **Focus on Specific Components**: Generate tutorials for one component at a time
   ```bash
   python main.py --dir /path/to/framework/component --include "*.js" --max-size 20000 --max-abstractions 3
   ```

2. **Increase Cache Size**: For more comprehensive analysis
   ```bash
   python main.py --dir /path/to/framework --cache-size 1000 --context-window 100000
   ```

3. **Use Appropriate Filters**: Include relevant files and exclude irrelevant ones
   ```bash
   python main.py --dir /path/to/framework --include "*.js" "*.json" --exclude "node_modules/*" "*.test.js"
   ```

4. **Generate Multiple Chapters**: Create a complete tutorial with multiple chapters
   ```bash
   python main.py --dir /path/to/framework --max-abstractions 5
   ```

5. **Remove Abstraction Limits for Comprehensive Documentation**: For the most detailed analysis
   ```bash
   python main.py --dir /path/to/framework --include "*.js" "*.json" --exclude "node_modules/*" --max-size 100000
   ```

6. **Target Specific Features**: For specialized documentation on particular features
   ```bash
   # For payment processing
   python main.py --repo https://github.com/username/framework \
     --include "**/*payment*.js" "**/*card*.js" "**/*iframe*.js" "**/*gateway*.js" "**/*3ds*.js" \
     --exclude "node_modules/*" "**/*.test.js" \
     --max-size 100000
   ```

### Analyzing PocketFlow Output

After generating tutorials with PocketFlow:

1. Review the `index.md` file to understand the key concepts identified
2. Examine each chapter to see how concepts are explained
3. Note the code examples and explanations provided
4. Identify any gaps or areas that need more detail

## Custom Repository Scanning

### When to Use Custom Scanning

Custom repository scanning is most effective for:
- Identifying the most frequently used patterns
- Finding all files related to a specific feature
- Quantifying the importance of different components
- Discovering relationships between components

### Creating a Custom Scanner

A basic repository scanner should:
1. Search for key terms related to framework components
2. Count occurrences of each term
3. Identify files containing each term
4. Generate a summary of findings

Example scanner features:
- Category-based keyword searching
- File filtering by pattern
- Context extraction around matches
- Quantitative analysis of results

### Analyzing Scanner Output

After scanning the repository:

1. Review the most frequent categories to identify key framework components
2. Examine the files associated with each category
3. Look for patterns in how components are used together
4. Identify specialized components (like payment processing)

## Combining Insights

The most effective documentation comes from combining insights from multiple sources:

1. **Use PocketFlow for Structure**: The PocketFlow tutorial provides a logical structure and beginner-friendly explanations

2. **Use Scanner for Completeness**: The repository scanner ensures all important components are covered

3. **Use Manual Analysis for Depth**: Manual code review provides detailed understanding of complex components

4. **Create a Unified Narrative**: Combine all insights into a cohesive documentation set

## Effective Prompt Templates

### Framework Overview Prompt

```
Please create a comprehensive overview of the [Framework Name] based on the following inputs:

1. PocketFlow tutorial output, which identified these key concepts:
   - [List key concepts from PocketFlow]
   - Note: This tutorial was generated without abstraction limits for maximum comprehensiveness

2. Repository scan results, which found these key components:
   - [List key components from scanner with occurrence counts]

3. Specific areas that need detailed coverage:
   - [List areas requiring special attention]

The overview should include:
- Framework architecture and directory structure
- Key components and their relationships
- Specialized functionality (like payment processing)
- Code examples for important patterns
- Detailed explanation of iframe handling for payment forms
- Explanation of how 3D Secure challenges are handled
```

### User Guide Prompt

```
Please create a user guide for the [Framework Name] based on the framework overview. The guide should:

1. Provide step-by-step instructions for common tasks:
   - Setting up the framework
   - Creating new tests
   - Using page objects
   - Managing test data
   - Running tests

2. Include specific guidance for these complex areas:
   - [List complex areas like payment testing]

3. Provide troubleshooting advice for common issues

The guide should be practical, with concrete code examples that users can adapt for their own tests.
```

### Specialized Guide Prompt

```
Please create a specialized guide for [Specific Feature] in the [Framework Name]. Based on our analysis:

1. This feature appears in [X] files with [Y] occurrences
2. The key components involved are:
   - [List key components]
3. The most complex aspects are:
   - [List complex aspects]

The guide should provide:
- Detailed explanation of how the feature works
- Step-by-step instructions for testing this feature
- Code examples for common scenarios
- Best practices specific to this feature
- Troubleshooting advice
```

## Iterative Refinement

Documentation generation is an iterative process:

1. **Generate Initial Documentation**: Use the multi-tool approach to create initial documentation

2. **Identify Gaps**: Review the documentation to identify missing or unclear areas

3. **Create Targeted Prompts**: Craft specific prompts to address the gaps

4. **Refine and Expand**: Continuously improve the documentation based on feedback

5. **Keep Documentation Updated**: Update documentation as the framework evolves

## Best Practices

1. **Be Specific in Prompts**: Clearly specify what documentation you need and what sources to use

2. **Provide Context**: Include relevant information about the framework and its purpose

3. **Request Multiple Formats**: Ask for different documentation types for different audiences

4. **Include Code Examples**: Always request concrete code examples to illustrate concepts

5. **Focus on Complex Areas**: Pay special attention to complex areas like payment processing

6. **Request Best Practices**: Always include best practices in the documentation

7. **Consider Documentation Structure**: Request logical organization with clear sections

8. **Validate with Experts**: Have framework experts review the generated documentation

9. **Remove Abstraction Limits for Comprehensive Documentation**: Our experiments show that removing the `--max-abstractions` parameter produces significantly more comprehensive and detailed tutorials

10. **Target Specific Features**: Create specialized documentation for complex features like payment processing, 3D Secure authentication, and iframe handling

11. **Compare Limited vs. Unlimited Abstractions**: Generate both quick overviews (with limited abstractions) and comprehensive documentation (without abstraction limits) to get the best of both approaches

## Conclusion

Creating effective prompts for framework documentation requires a multi-tool approach that combines the strengths of different analysis methods. By using PocketFlow for structured tutorials, custom scanning for comprehensive coverage, and targeted prompts for specific needs, you can generate documentation that is both comprehensive and useful for different audiences.

Remember that documentation generation is an iterative process. Start with broad analysis, identify gaps, and then use targeted prompts to fill those gaps. The result will be a comprehensive documentation set that helps users understand and effectively use the framework.
