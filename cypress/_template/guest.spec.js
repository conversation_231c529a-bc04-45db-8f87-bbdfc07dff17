import accounts from '../../../fixtures/accounts';

describe('Booking Management: Create a Booking directly via Availability', () => {

    //beforeEach(() => {
    //    Cypress.Cookies.preserveOnce('high-level_session', 'session_id', 'remember_token')
    //})

    it('can log in', () => {
        cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
        cy.get('header h1').should('contain.text', 'Check availability')
    })


})
