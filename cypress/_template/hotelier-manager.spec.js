import accounts from '../../../fixtures/accounts';

describe('Booking Management: Create a Booking directly via Availability', () => {

    //beforeEach(() => {
    //    Cypress.Cookies.preserveOnce('high-level_session', 'session_id', 'remember_token')
    //})

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('has one hotel', () => {
        cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
    })




})
