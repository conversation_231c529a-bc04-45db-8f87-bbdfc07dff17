import accounts from '../../../fixtures/accounts';

describe('Booking Management: Create a Booking directly via Availability', () => {

    //beforeEach(() => {
    //    Cypress.Cookies.preserveOnce('high-level_session', 'session_id', 'remember_token')
    //})

    it('can log in', () => {
        cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
        cy.get('header h1').should('contain.text', 'Check availability')
    })

})
