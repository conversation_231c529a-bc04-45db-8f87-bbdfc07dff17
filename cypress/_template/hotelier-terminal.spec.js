import accounts from '../../../fixtures/accounts';

describe('Booking Management: Create a Booking directly via Availability', () => {

    //beforeEach(() => {
    //    Cypress.Cookies.preserveOnce('high-level_session', 'session_id', 'remember_token')
    //})

    it('can alogin', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
    })

})
