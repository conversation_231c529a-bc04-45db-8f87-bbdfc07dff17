import accounts from '../../../fixtures/accounts';

describe('Booking Management: Create a Booking directly via Availability', () => {

    //beforeEach(() => {
    //    Cypress.Cookies.preserveOnce('high-level_session', 'session_id', 'remember_token')
    //})

    it('can log in', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can select a hotel', () => {
        cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
        cy.get('.module__header').should('contain', accounts.cypress_a.title)
        cy.contains('a', accounts.cypress_a.title).click()
        cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
    })







})
