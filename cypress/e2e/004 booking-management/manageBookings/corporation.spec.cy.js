import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Booking Management: Corporation cannot edit a Booking', () => {

        it('can log in', () => {
            cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
            cy.get('div.title h1').should('contain.text', 'Your Stay')
        })

        it('has no access to the calendar', () => {
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/calendar`);
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        it('has no access to bookings', () => {
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/bookings`);
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })
        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
