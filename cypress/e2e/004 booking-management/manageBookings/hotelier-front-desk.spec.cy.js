import accounts from '../../../fixtures/accounts';
import EditSeededBookings from '../../../fixtures/cypress_a/edit_seeded_bookings'
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Booking Management: Hotelier Front Desk can edit a Booking', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can edit reservations selected from the calendar', () => {
            // Listeners
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/calendar?*'}).as('fetchCalendar');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/resources?*'}).as('fetchResources');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/events?*'}).as('fetchEvents');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/rates?*'}).as('fetchRates');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/inventory?*'}).as('fetchInventory');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/credit-cards?*'}).as('fetchCards');
            cy.intercept({method: 'POST', url: '/hotels/*/calendar/book'}).as('createReservation');

            EditSeededBookings.forEach(booking => {

                // Load Calendar
                cy.contains('a', /Calendar/).click().wait(['@fetchResources', '@fetchEvents', '@fetchRates'])

                // Select Booking
                cy.get('a.fc-event').contains(booking.guest_name).click()

                // Edit Booking
                cy.get('h2.module__header a.edit').click()

                // Confirm Booking
                cy.url().should('contain', 'bookings').and('contain', 'edit')
                cy.get('h2.module__header').should('contain', 'Update booking')
                cy.get('.form__contents input#guest_name').should('contain.value', booking.guest_name)
                cy.get('.form__contents input#referrer').should('contain.value', booking.referrer)

                // Update Booking
                cy.get('form').first().fillUpdateBooking(booking).submit()

                // Confirm
                cy.url().should('contain', 'bookings').and('not.contain', 'edit')
                cy.get('h2.module__header').should('contain', booking.referrer)
            })

            // Load Calendar
            cy.contains('a', /Calendar/).click().wait(['@fetchResources', '@fetchEvents', '@fetchRates'])
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})