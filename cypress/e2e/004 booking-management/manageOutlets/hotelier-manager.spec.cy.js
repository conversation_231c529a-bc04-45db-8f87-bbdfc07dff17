import outlets from '../../../fixtures/outlets'
import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Booking Management: Hotelier Manager can manage Outlets', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('has one hotel', () => {
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create outlets', () => {
            cy.contains('a', /Outlets/).click({ force: true })
            outlets.forEach(outlet => {
                cy.contains('a', /Create Outlet/).click()
                cy.get('form').fillCreateOutletForm(outlet).submit()
                cy.get('.message.success').should('contain', `${outlet.type} created`)
                cy.url().should('contain', '/outlets')
            })
        })

        it ('can search outlets', () => {
            cy.contains('a', /Outlets/).click({ force: true })
            cy.get('.col1 form').fillSearchForm({
                code: outlets[0].code
            }).submit()
            cy.get('.table__content')
                .should('contain', outlets[0].code)
                .should('not.contain', outlets[1].code)
        })

        it('can delete an outlet', () => {
            let outlet = outlets[0];
            cy.contains('a', /Outlets/).click({ force: true })
            cy.contains('td', outlet.code).selectTrashCan().click()
            cy.get('.message.success').should('contain', outlet.type + ' deleted')
        })
        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
