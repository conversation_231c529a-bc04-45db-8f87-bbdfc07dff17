import accounts from '../../../fixtures/accounts';
import venues from '../../../fixtures/venues';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Booking Management: Hotelier Front Desk can manage Venues', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create venues', () => {
            cy.contains('a', /Venues/).click({ force: true })
            venues.forEach(venue => {
                cy.contains('a', /Create Venue/).click()
                cy.get('form').fillCreateVenueForm(venue).submit()
                cy.get('.message.success').should('contain', `${venue.type} created`)
                cy.url().should('contain', '/venues')
            })
        })

        it ('can search venues', () => {
            cy.contains('a', /Venues/).click({ force: true })
            cy.get('.col1 form').fillSearchForm({
                code: venues[0].code
            }).submit()
            cy.get('.table__content')
                .should('contain', venues[0].code)
                .should('not.contain', venues[1].code)
        })

        it('can delete a venue', () => {
            let venue = venues[0]
            cy.contains('a', /Venues/).click({ force: true })
            cy.contains('td', venue.code).selectTrashCan().click()
            cy.get('.message.success').should('contain', venue.type + ' deleted')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})