import accounts from '../../../fixtures/accounts';
import rateRestrictions from '../../../fixtures/cypress_a/rate_restrictions';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Plan Management: Hotelier Front Desk cannot create rate restrictions', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('cannot create rate restrictions', () => {
            // Intercepts..
            cy.intercept({method: 'GET', url: '/permissions/'}).as('fetchPermissions')
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels')
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes')
            cy.intercept({method: 'POST', url: '/ratesgrid/update'}).as('updateRates')
            cy.intercept({method: 'POST', url: '/ratesgrid/'}).as('fetchRates')
            cy.contains('a', 'Grid').click()
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRates'])

            cy.get('.app-content').should('not.be.empty')
            cy.get('.rate-grid').should('not.be.empty')

            // Filter Editable rates only
            cy.get('.search-and-display button').click()
            cy.get('.search-and-display .dropdown-menu').within(() => {
                cy.contains('a', 'Editable Rates').click()
            })

            // Rate Restrictions
            rateRestrictions.forEach(rateRestriction => {
                // Select restriction type
                cy.get('button#rest-drop-down').click()
                cy.get('.restrition-dropdown').within(() => {   // bad spellings by @Daryll
                    cy.contains('a.dropdown-item', rateRestriction.type).click()
                })
                cy.get('button#rest-drop-down').should('contain.text', rateRestriction.type)
                // Select Rate row
                cy.contains('.rate-triage-row.rate', rateRestriction.rate).within(() => {
                    // Unsure input is 'readonly'
                    cy.get('.rate-grid-row-data .rate-grid-cell.isCurrentDay')
                        .find('input')
                        .should('have.attr', 'readonly')
                })
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})