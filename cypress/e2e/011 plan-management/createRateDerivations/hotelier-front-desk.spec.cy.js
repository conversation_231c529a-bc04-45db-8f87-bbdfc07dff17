import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    const derivedRate = {
        'rate_name': 'Basic',
    }

    describe('Plan Management: Hotelier Front Desk cannot create a derived rate', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('cannot create a derived rate', () => {
            // Set up listeners
            cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
            cy.intercept({method: 'POST', url: '/derivations/create/*'}).as('createDerivation');
            cy.contains('a', /Rate Plans/).click({ force: true })
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes'])
            cy.contains('p', derivedRate.rate_name).click()
            cy.contains('a', /Derivations/).should('not.exist')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})