import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    const rateHurdle = {
        'name': 'Test Rate Hurdle (Staff Admin)',
        'days_lead_out_from': 6,
        'days_lead_out_to': 8,
        'days_of_week': ['mon', 'tue', 'wed', 'thu', 'fri'],
        'min_threshold': 20,
        'max_threshold': 80,
        'rates': [
            'Basic',
        ],
        'roomTypes': [
            {
                'name': 'Single',
                'strategy': 'Amount',       // 'Percent|Amount'
                'value': 20
            }
        ]
    }

    describe('Plan Management: Staff Admin can create a rate hurdle', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create a rate hurdle', () => {
            // Set up listeners
            cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
            cy.intercept({method: 'POST', url: '/hurdles/'}).as('fetchRateHurdles');
            cy.intercept({method: 'POST', url: '/hurdles/create'}).as('createRateHurdle');
            cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
            // Navigate to rate Hurdles
            cy.contains('a', 'Rate Hurdles').click({ force: true })
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRateHurdles'])
                .wait(1000)
            // Create Rate Hurdle
            cy.contains('a', 'Create New Hurdle').click()
            cy.contains('.form-set-title-wrapper', 'Rate Hurdles')
            cy.get('form')
                .fillCreateRateHurdleForm(rateHurdle).submit()
                .wait(['@createRateHurdle', '@fetchRateHurdles'])
            cy.get('.table-react').should('contain.text', rateHurdle.name)
            cy.url().should('contain', '/v2/rates/hurdle')
        })

        it('can delete a rate hurdle', () => {
            // Set up listeners
            cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
            cy.intercept({method: 'POST', url: '/hurdles/'}).as('fetchRateHurdles');
            cy.intercept({method: 'POST', url: '/hurdles/create'}).as('createRateHurdle');
            cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
            // Navigate to rate Hurdles
            cy.contains('a', 'Rate Hurdles').click({ force: true })
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRateHurdles'])
                .wait(1000)
            // Delete Rate Hurdle
            cy.get('.link.name').contains(rateHurdle.name).parent().invoke('attr', 'id').then(id => {
                let HurdelId = id.split('-')[1];
                cy.get('#Delete-' + HurdelId ).click()
            })
            // Confirm
            cy.get('.modal-content')
                .should('be.visible')
                .should('contain.text', `Are you sure you want to delete ${rateHurdle.name}`)
                .within(() => {
                    cy.get('button').contains('Delete').click()
                })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
