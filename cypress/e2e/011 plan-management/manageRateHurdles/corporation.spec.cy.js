import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Plan Management: Corporations cannot create a rate hurdle', () => {

        it('cannot access rates', () => {
            cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
            cy.get('header h1').should('contain.text', 'Check availability')
        })

        it('cannot create a rate', () => {
            cy.fn_safeVisit('hotels/cypress-a/v2/rates/hurdle')
            cy.get('.module__header').should('contain.text','Page not found')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
