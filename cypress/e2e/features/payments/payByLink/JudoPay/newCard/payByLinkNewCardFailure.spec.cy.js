import TestFilters from '../../../../../../support/filterTests';
import accounts from '../../../../../../fixtures/accounts';
import { payByLinkForm } from '../../../../../../support/pageObjectModel/Pages/paymentRequestForm';
import { payByLinkPage } from '../../../../../../support/pageObjectModel/Pages/payByLinkPage';
import { bookingsPage } from '../../../../../../support/pageObjectModel/Pages/bookings';
import { bookingHubPage } from '../../../../../../support/pageObjectModel/Pages/bookingHub';
import { guestProfilePage } from '../../../../../../support/pageObjectModel/Pages/guestProfile';

import { invalidJudoPayCards } from '../../../../../../fixtures/cards/paymentDetailsJudopay';
import { pmsGeneral } from '../../../../../../support/pageObjectModel/Components/pmsGeneral';
import { paymentIframe } from '../../../../../../support/pageObjectModel/Components/paymentIFrame';

const card = invalidJudoPayCards.invalidFrictionlessSuccessictionlessSuccess
const hotelSlug = accounts.cypressHotel6.slug
const paymentAmount = 10.00
const paymentAmountFormatted = paymentAmount.toLocaleString('en-US', {
    style: 'currency',
    currency: 'GBP'
  });
const bookingAmount = 200.00


const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {

describe('Payment Processing: Request a Payment By Link as staff', () => {
 
    before('can log in', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Logged in');
    })
 
    it('Verify user cannot successfuly complete Payment By Link with imcorrect CCV code', () => {
        bookingsPage.open(hotelSlug)
        bookingsPage.clickFirstExistingBooking()
        bookingHubPage.assertCreditCardNotAttached()
                      .assertPaymentStatus('pending')
        bookingHubPage.clickAddPaymentButton()
 
        bookingHubPage.selectPaymentByLinkOption()
        cy.get(payByLinkForm.selectors.headerTitle).should('contain', 'Request a payment from')
        payByLinkForm.submitAndOpenPBL(paymentAmount)
        
        paymentIframe.fillJudoPayDetails(card)
        payByLinkPage.assertUnsuccessfulPayment(paymentAmountFormatted)
        
        cy.get(payByLinkForm.selectors.headerTitle).should('have.text', 'Payment failure!')
        cy.get(payByLinkPage.selectors.paymentResultMessage).should('have.text', `We failed to take a payment of ${paymentAmountFormatted}.`)

        //Verify payment is reflected on booking hub 
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        bookingsPage.open(hotelSlug)
        bookingsPage.clickFirstExistingBooking()
        bookingHubPage
            .assertPaymentStatus('pending')
            .assertPaymentAmount(0.00)
            .assertBalanceAmount(bookingAmount)
            .assertCreditCardNotAttached()
            .clickGuestProfileButton()
        
        //Verify card is not displayed on guest profile
        cy.get(guestProfilePage.selectors.cardBox).should('not.exist')
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})
})
 