import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {


//To cover AC7 and AC9
describe('Staff Admin: verify the format of the staff matrix', () => {

    it('can verify permissions layout and naming', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.get('.message.success').should('contain', 'Logged in')

        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug)
        cy.url('contains', '/hotels/cypress_a/dashboard')

        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/hoteliers')
        cy.contains('a', /Staff Permissions/).click()
        cy.url('contains', '/hotels/cypress_a/permissions')

        //click reset permissions
        cy.contains('a', /Reset Permissions/).click()

        //table
        cy.get('.table > .module__header').should('contain', 'Permissions Matrix')

        //headings
        cy.get('thead > tr > :nth-child(1)').should('be.empty')
        cy.get('thead > tr > :nth-child(2)').should('contain', 'Role')
        cy.get('thead > tr > :nth-child(3)').should('contain', 'View')
        cy.get('thead > tr > :nth-child(4)').should('contain', 'Edit')
        cy.get('thead > tr > :nth-child(5)').should('contain', 'Delete')

        // body

        // Bookings
        cy.get('[data-model=Bookings][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Bookings-viewBookings').should('not.exist')
            cy.get('#permissions-front-desk-Bookings-updateBooking').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Booking-deleteBooking').should('not.exist')
        })
        cy.get('[data-model=Bookings][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Bookings-viewBookings').should('not.exist')
            cy.get('#permissions-manager-Bookings-updateBooking').should('exist').and('be.checked')
            cy.get('#permissions-manager-Booking-deleteBooking').should('not.exist')
        })

        // Categories
        cy.get('[data-model=Categories][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Categories-viewCategory').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Categories-updateCategory').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Categories-deleteCategory').should('exist').and('be.checked')
        })
        cy.get('[data-model=Categories][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Categories-viewCategory').should('exist').and('be.checked')
            cy.get('#permissions-manager-Categories-updateCategory').should('exist').and('be.checked')
            cy.get('#permissions-manager-Categories-deleteCategory').should('exist').and('be.checked')
        })

        // Conferences
        cy.get('[data-model=Conferences][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Conferences-viewConference').should('not.exist')
            cy.get('#permissions-front-desk-Conferences-updateConference').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Conferences-deleteConference').should('exist').and('be.checked')
        })
        cy.get('[data-model=Conferences][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Conferences-viewConference').should('not.exist')
            cy.get('#permissions-manager-Conferences-updateConference').should('exist').and('be.checked')
            cy.get('#permissions-manager-Conferences-deleteConference').should('exist').and('be.checked')
        })

        // Corporations
        cy.get('[data-model=Corporations][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Corporations-viewCorporations').should('not.exist')
            cy.get('#permissions-front-desk-Corporations-updateCorporation').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Corporations-deleteCorporation').should('exist').and('be.checked')
        })
        cy.get('[data-model=Corporations][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Corporations-viewCorporation').should('not.exist')
            cy.get('#permissions-manager-Corporations-updateCorporation').should('exist').and('be.checked')
            cy.get('#permissions-manager-Corporations-deleteCorporation').should('exist').and('be.checked')
        })

        // Credit cards
        cy.get('[data-model=Credit_Cards][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Credit_Cards-viewCard').should('not.exist')
            cy.get('#permissions-front-desk-Credit_Cards-updateCard').should('not.exist')
            cy.get('#permissions-front-desk-Credit_Cards-deleteCard').should('exist').and('not.be.checked')
        })
        cy.get('[data-model=Credit_Cards][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Credit_Cards-viewCard').should('not.exist')
            cy.get('#permissions-manager-Credit_Cards-updateCard').should('not.exist')
            cy.get('#permissions-manager-Credit_Cards-deleteCard').should('exist').and('be.checked')
        })

        // Deposits
        cy.get('[data-model=Deposits][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Deposits-viewDeposit').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Deposits-updateDeposit').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Deposits-deleteDeposit').should('not.exist')
        })
        cy.get('[data-model=Deposits][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Deposits-viewDeposit').should('exist').and('be.checked')
            cy.get('#permissions-manager-Deposits-updateDeposit').should('exist').and('be.checked')
            cy.get('#permissions-manager-Deposits-deleteDeposit').should('not.exist')
        })

        // Gifts
        cy.get('[data-model=Gifts][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Gifts-viewGift').should('not.exist')
            cy.get('#permissions-front-desk-Gifts-updateGift').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Gifts-deleteGift').should('exist').and('be.checked')
        })
        cy.get('[data-model=Gifts][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Gifts-viewGift').should('not.exist')
            cy.get('#permissions-manager-Gifts-updateGift').should('exist').and('be.checked')
            cy.get('#permissions-manager-Gifts-deleteGift').should('exist').and('be.checked')
        })

        // Guests
        cy.get('[data-model=Guests][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Guests-viewGuest').should('not.exist')
            cy.get('#permissions-front-desk-Guests-updateGuest').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Guests-deleteGuest').should('exist').and('be.checked')
        })
        cy.get('[data-model=Guests][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Guests-viewGuest').should('not.exist')
            cy.get('#permissions-manager-Guests-updateGuest').should('exist').and('be.checked')
            cy.get('#permissions-manager-Guests-deleteGuest').should('exist').and('be.checked')
        })

        // Hurdles
        cy.get('[data-model=Hurdles][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Hurdles-viewHurdle').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Hurdles-updateHurdle').should('exist').and('not.be.checked')
            cy.get('#permissions-front-desk-Hurdles-deleteHurdle').should('exist').and('not.be.checked')
        })
        cy.get('[data-model=Hurdles][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Hurdles-viewHurdle').should('exist').and('be.checked')
            cy.get('#permissions-manager-Hurdles-updateHurdle').should('exist').and('be.checked')
            cy.get('#permissions-manager-Hurdles-deleteHurdle').should('exist').and('be.checked')
        })

        // Images
        cy.get('[data-model=Images][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Images-viewImage').should('not.exist')
            cy.get('#permissions-front-desk-Images-updateImage').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Images-deleteImage').should('exist').and('be.checked')
        })
        cy.get('[data-model=Images][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Images-viewImage').should('not.exist')
            cy.get('#permissions-manager-Images-updateImage').should('exist').and('be.checked')
            cy.get('#permissions-manager-Images-deleteImage').should('exist').and('be.checked')
        })

        // Outlets
        cy.get('[data-model=Outlets][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Outlets-viewOutlet').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Outlets-updateOutlet').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Outlets-deleteOutlet').should('exist').and('be.checked')
        })
        cy.get('[data-model=Outlets][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Outlets-viewOutlet').should('exist').and('be.checked')
            cy.get('#permissions-manager-Outlets-updateOutlet').should('exist').and('be.checked')
            cy.get('#permissions-manager-Outlets-deleteOutlet').should('exist').and('be.checked')
        })

        // Packages
        cy.get('[data-model=Packages][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Packages-viewPackage').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Packages-updatePackage').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Packages-deletePackage').should('exist').and('be.checked')
        })
        cy.get('[data-model=Packages][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Packages-viewPackage').should('exist').and('be.checked')
            cy.get('#permissions-manager-Packages-updatePackage').should('exist').and('be.checked')
            cy.get('#permissions-manager-Packages-deletePackage').should('exist').and('be.checked')
        })

        // Payments
        cy.get('[data-model=Payments][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Payments-viewPayment').should('not.exist')
            cy.get('#permissions-front-desk-Payments-updatePayment').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Payments-deletePayment').should('not.exist')
        })
        cy.get('[data-model=Payments][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Payments-viewPayment').should('not.exist')
            cy.get('#permissions-manager-Payments-updatePayment').should('exist').and('be.checked')
            cy.get('#permissions-manager-Payments-deletePayment').should('not.exist')
        })

        // Policies
        cy.get('[data-model=Policies][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Policies-viewPolicy').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Policies-updatePolicy').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Policies-deletePolicy').should('exist').and('be.checked')
        })
        cy.get('[data-model=Policies][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Policies-viewPolicy').should('exist').and('be.checked')
            cy.get('#permissions-manager-Policies-updatePolicy').should('exist').and('be.checked')
            cy.get('#permissions-manager-Policies-deletePolicy').should('exist').and('be.checked')
        })

        // Products
        cy.get('[data-model=Products][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Products-viewProduct').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Products-updateProduct').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Products-deleteProduct').should('exist').and('be.checked')
        })
        cy.get('[data-model=Products][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Products-viewProduct').should('exist').and('be.checked')
            cy.get('#permissions-manager-Products-updateProduct').should('exist').and('be.checked')
            cy.get('#permissions-manager-Products-deleteProduct').should('exist').and('be.checked')
        })

        // Product Rates
        cy.get('[data-model=Product_Rates][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Product_Rates-viewProductRate').should('exist').and('not.be.checked')  // ***
            cy.get('#permissions-front-desk-Product_Rates-updateProductRate').should('exist').and('not.be.checked') // ***
            cy.get('#permissions-front-desk-Product_Rates-deleteProductRate').should('not.exist')
        })
        cy.get('[data-model=Product_Rates][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Product_Rates-viewProductRate').should('exist').and('be.checked')
            cy.get('#permissions-manager-Product_Rates-updateProductRate').should('exist').and('be.checked')
            cy.get('#permissions-manager-Product_Rates-deleteProductRate').should('not.exist')
        })

        // Purchases
        cy.get('[data-model=Purchases][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Purchases-viewPurchase').should('not.exist')
            cy.get('#permissions-front-desk-Purchases-updatePurchase').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Purchases-deletePurchase').should('exist').and('be.checked')
        })
        cy.get('[data-model=Purchases][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Purchases-viewPurchase').should('not.exist')
            cy.get('#permissions-manager-Purchases-updatePurchase').should('exist').and('be.checked')
            cy.get('#permissions-manager-Purchases-deletePurchase').should('exist').and('be.checked')
        })

        // Reservations
        cy.get('[data-model=Reservations][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Reservations-viewReservation').should('not.exist')
            cy.get('#permissions-front-desk-Reservations-updateReservation').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Reservations-deleteReservation').should('not.exist')
        })
        cy.get('[data-model=Reservations][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Reservations-viewReservation').should('not.exist')
            cy.get('#permissions-manager-Reservations-updateReservation').should('exist').and('be.checked')
            cy.get('#permissions-manager-Reservations-deleteReservation').should('not.exist')
        })

        // Rewards
        cy.get('[data-model=Rewards][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Rewards-viewReward').should('not.exist')
            cy.get('#permissions-front-desk-Rewards-updateReward').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Rewards-deleteReward').should('exist').and('be.checked')
        })
        cy.get('[data-model=Rewards][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Rewards-viewReward').should('not.exist')
            cy.get('#permissions-manager-Rewards-updateReward').should('exist').and('be.checked')
            cy.get('#permissions-manager-Rewards-deleteReward').should('exist').and('be.checked')
        })

        // Rooms
        cy.get('[data-model=Rooms][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Rooms-viewRoom').should('not.exist')
            cy.get('#permissions-front-desk-Rooms-updateRoom').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Rooms-deleteRoom').should('exist').and('be.checked')
        })
        cy.get('[data-model=Rooms][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Rooms-viewRoom').should('not.exist')
            cy.get('#permissions-manager-Rooms-updateRoom').should('exist').and('be.checked')
            cy.get('#permissions-manager-Rooms-deleteRoom').should('exist').and('be.checked')
        })

        // Room Types
        cy.get('[data-model=Room_Types][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Room_Types-viewRoomType').should('not.exist')
            cy.get('#permissions-front-desk-Room_Types-updateRoomType').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Room_Types-deleteRoomType').should('exist').and('be.checked')
        })
        cy.get('[data-model=Room_Types][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Room_Types-viewRoomType').should('not.exist')
            cy.get('#permissions-manager-Room_Types-updateRoomType').should('exist').and('be.checked')
            cy.get('#permissions-manager-Room_Types-deleteRoomType').should('exist').and('be.checked')
        })

        // Stays
        cy.get('[data-model=Stays][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Stays-viewStay').should('not.exist')
            cy.get('#permissions-front-desk-Stays-updateStay').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Stays-deleteStay').should('not.exist')
        })
        cy.get('[data-model=Stays][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Stays-viewStay').should('not.exist')
            cy.get('#permissions-manager-Stays-updateStay').should('exist').and('be.checked')
            cy.get('#permissions-manager-Stays-deleteStay').should('not.exist')
        })

        // Venues
        cy.get('[data-model=Venues][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Venues-viewVenue').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Venues-updateVenue').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Venues-deleteVenue').should('exist').and('be.checked')
        })
        cy.get('[data-model=Venues][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Venues-viewVenue').should('exist').and('be.checked')
            cy.get('#permissions-manager-Venues-updateVenue').should('exist').and('be.checked')
            cy.get('#permissions-manager-Venues-deleteVenue').should('exist').and('be.checked')
        })

        // Vouchers
        cy.get('[data-model=Vouchers][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Vouchers-viewVoucher').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Vouchers-updateVoucher').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Vouchers-deleteVoucher').should('exist').and('be.checked')
        })
        cy.get('[data-model=Vouchers][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Vouchers-viewVoucher').should('exist').and('be.checked')
            cy.get('#permissions-manager-Vouchers-updateVoucher').should('exist').and('be.checked')
            cy.get('#permissions-manager-Vouchers-deleteVoucher').should('exist').and('be.checked')
        })

        // Rates Grid
        cy.get('[data-model=Rates_Grid][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Rates_Grid-viewRatesGrid').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Rates_Grid-updateRatesGrid').should('exist').and('not.be.checked')
            cy.get('#permissions-front-desk-Rates_Grid-deleteRatesGrid').should('not.exist')
        })
        cy.get('[data-model=Rates_Grid][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Rates_Grid-viewRatesGrid').should('exist').and('be.checked')
            cy.get('#permissions-manager-Rates_Grid-updateRatesGrid').should('exist').and('be.checked')
            cy.get('#permissions-manager-Rates_Grid-deleteRatesGrid').should('not.exist')
        })

        // Rates
        cy.get('[data-model=Rates][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Rates-viewRate').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Rates-updateRate').should('exist').and('not.be.checked')
            cy.get('#permissions-front-desk-Rates-deleteRate').should('exist').and('not.be.checked')
        })
        cy.get('[data-model=Rates][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Rates-viewRate').should('exist').and('be.checked')
            cy.get('#permissions-manager-Rates-updateRate').should('exist').and('be.checked')
            cy.get('#permissions-manager-Rates-deleteRate').should('exist').and('be.checked')
        })

        // Promotional Codes
        cy.get('[data-model=Promotional_Codes][data-role=front-desk]').within(() => {
            cy.get('#permissions-front-desk-Promotional_Codes-viewPromoCode').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Promotional_Codes-updatePromoCode').should('exist').and('be.checked')
            cy.get('#permissions-front-desk-Promotional_Codes-deletePromoCode').should('exist').and('be.checked')
        })
        cy.get('[data-model=Promotional_Codes][data-role=manager]').within(() => {
            cy.get('#permissions-manager-Promotional_Codes-viewPromoCode').should('exist').and('be.checked')
            cy.get('#permissions-manager-Promotional_Codes-updatePromoCode').should('exist').and('be.checked')
            cy.get('#permissions-manager-Promotional_Codes-deletePromoCode').should('exist').and('be.checked')
        })

        //should not contain
        cy.get('tbody').should('not.contain', 'Extra')
        cy.get('tbody').should('not.contain', 'Consumable')
        cy.get('tbody').should('not.contain', 'Inclusion')
        cy.get('tbody').should('not.contain', 'Sundry')
        cy.get('tbody').should('not.contain', 'Reduction')
        cy.get('tbody').should('not.contain', 'Charge')
        cy.get('tbody').should('not.contain', 'Advanced Rates')
        cy.get('tbody').should('not.contain', 'Dynamic Rates')

    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	

})