import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Hotelier Manager: Permissions user selection', ()=> {

    it('can interact with dropdown', () => {

        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager2.email, accounts.cypress_a.hotelier.manager2.password)
        cy.get('.message.success').should('contain', 'Logged in')

        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug)
        cy.url('contains', '/hotels/cypress-a/dashboard')

        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/edit')
        cy.get('#disabled_permissions_user').should('not.exist')
        cy.get('#permissions_user').should('be.enabled')
        cy.get('#permissions_user').should('contain', accounts.cypress_a.hotelier.manager2.email)
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})