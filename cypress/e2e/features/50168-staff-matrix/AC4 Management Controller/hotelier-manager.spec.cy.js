import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Hotelier Manager: Permissions user selection', ()=> {

    it('cannot interact with dropdown', () => {

        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')

        cy.visit('/hotels/'+ accounts.cypress_a.slug)
        cy.url('contains', '/hotels/' + accounts.cypress_a.slug + '/dashboard')

        cy.visit('/hotels/' + accounts.cypress_a.slug + '/edit')
        cy.get('#disabled_permissions_user').should('be.disabled')

        cy.get('#permissions_user').should('not.exist')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})