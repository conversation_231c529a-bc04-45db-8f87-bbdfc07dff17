import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Hotelier Front Desk: ensure that the restrict. settings are no longer available in pms', () => {

    it('log in and check restrict.delete does not exist', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/options/help', {failOnStatusCode: false})
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('log in and check restrict.edit does not exist', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/options/help', {failOnStatusCode: false})
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})