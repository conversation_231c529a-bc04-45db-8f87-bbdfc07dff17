import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('hotelier group admin: ensure that the restrict. settings are no longer available', () => {

    it('log in and check restrict.delete no longer exists', () => {
        cy.fn_login('hotelier', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug + '/options/help')
        cy.contains('.box', 'restrict.delete').should('not.exist')
    })
    it('log in and check restrict.edit no longer exists', () => {
        cy.fn_login('hotelier', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/options/help')
        cy.contains('.box', 'restrict.edit').should('not.exist')

    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})