import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Hotelier Manager: ensure that the restrict. settings are no longer available in pms', () => {

    it('log in and check restrict.delete does not exist', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/options/help')
        cy.contains('.box', 'restrict.delete').should('not.exist')
    })

    it('log in and check restrict.edit does not exist', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/options/help')
        cy.contains('.box', 'restrict.edit').should('not.exist')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})