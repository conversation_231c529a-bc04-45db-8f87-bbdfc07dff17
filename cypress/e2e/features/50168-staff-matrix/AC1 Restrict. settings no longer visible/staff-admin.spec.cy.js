import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests'

TestFilters(['permissionsMatrix'], () => {

describe('Staff Admin: ensure that the restrict. settings are no longer available in pms', () => {

    it('log in and check restrict.delete does not exist', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/options/help')
        cy.contains('.box', 'restrict.delete').should('not.exist')
    })

    it('log in and check restrict.edit does not exist', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/options/help')
        cy.contains('.box', 'restrict.edit').should('not.exist')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})