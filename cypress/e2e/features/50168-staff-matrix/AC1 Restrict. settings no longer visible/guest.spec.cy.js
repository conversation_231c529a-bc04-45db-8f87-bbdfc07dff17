import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Guest: ensure that the restrict. settings are no longer available in pms', () => {

    it('can login', () => {
        cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
        cy.get('header h1').should('contain.text', 'Check availability')
    })

    it('cannot access hotel options', () => {
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/options/help')
        cy.get('.module__header').should('contain.text','Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})