import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Terminal: ensure that the restrict. settings are no longer available in pms', () => {

    it('can login', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
        cy.get('div.title h2').should('contain.text','Hotelier Log in')
    })

    it('cannot access hotel options', () => {
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/options/help')
        cy.get('.module__header').should('contain.text','Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})