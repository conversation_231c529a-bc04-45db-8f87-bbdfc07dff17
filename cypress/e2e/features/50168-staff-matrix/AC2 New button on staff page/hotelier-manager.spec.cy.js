import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Hotelier Manager: ensure user can see but not interact with Staff permission button', () => {

    it('can see and click on new button and successfully redirect', () => {

        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')

        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug)
        cy.url('contains', '/hotels/cypress_a/dashboard')

        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/hoteliers')
        cy.contains('a', 'Staff Permissions').should('have.class', 'disabled')

        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/permissions')
        cy.get('.module__header').should('contain.text', 'Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})