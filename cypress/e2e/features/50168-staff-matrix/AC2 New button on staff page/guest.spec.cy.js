import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Guest: ensure user cant view hotelier page', () => {

    it('can login', () => {
        cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
        cy.get('header h1').should('contain.text', 'Check availability')
    })

    it('cannot access hotelier', () => {
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/hoteliers')
        cy.get('div.title h2').should('contain.text','Hotelier Log in')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})