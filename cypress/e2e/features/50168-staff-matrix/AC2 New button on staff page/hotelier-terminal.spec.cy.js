import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('terminal: ensure user cant view hotelier page', () => {

    it('can login', ()=>{
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
        cy.get('.module__header').should('contain','Hotelier Log In')
    })

    it('cannot access hotelier', () => {
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/hoteliers')
        cy.get('div.title h2').should('contain.text','Hotelier Log in')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})