import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Group Admin: ensure user can see and successfully interact with staff permissions button', ()=>{

    it('can see and click on the new button and successfully see permissions', ()=> {

        cy.fn_login('group', accounts.cypress_a.group_admin.email, accounts.cypress_a.group_admin.password)

        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug)
        cy.url('contains', '/hotels/cypress_a/dashboard')

        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/hoteliers')
        cy.contains('a', /Staff Permissions/).click()

        cy.url('contains', 'hotels/cypress_a/hotelier/permissions')
        cy.get('.col1 .module__header').should('contain.text', 'Permissions')
        cy.get('.col2 .module__header').should('contain.text', 'Permissions Matrix')

    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})