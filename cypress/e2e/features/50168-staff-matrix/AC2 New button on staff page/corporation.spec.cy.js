import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Corporation: ensure user cant view hotelier page', () => {

    it('can login', () => {
        cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
        cy.get('header h1').should('contain.text', 'Check availability')
    })

    it('cannot access hotelier', () => {
        cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/hoteliers')
        cy.get('div.title h2').should('contain.text','Hotelier Log in')
    })
    afterEach(function () {
        cy.fn_afterEach<PERSON>ira(this.currentTest)
    })
})	
})