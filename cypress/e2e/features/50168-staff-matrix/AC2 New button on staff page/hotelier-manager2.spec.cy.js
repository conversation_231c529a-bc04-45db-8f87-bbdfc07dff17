import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Hotelier Manager 2: ensure user can see and successfully interact with Staff permission button', () => {

    it('can see and click on new button and manage permissions', () => {

        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager2.email, accounts.cypress_a.hotelier.manager2.password)
        cy.get('.message.success').should('contain', 'Logged in')

        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug)
        cy.url('contains', '/hotels/cypress-a/dashboard')

        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/hoteliers')
        cy.contains('a', 'Staff Permissions').click()

        cy.url('contains', 'hotels/cypress_a/hotelier/permissions')
        cy.get('.col1 .module__header').should('contain.text', 'Permissions')
        cy.get('.col2 .module__header').should('contain.text', 'Permissions Matrix')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})