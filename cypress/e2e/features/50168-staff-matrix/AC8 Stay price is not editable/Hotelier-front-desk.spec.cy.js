import accounts from '../../../../fixtures/accounts'
import TestFilters from '../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {


describe('As a front desk stay I cant edit the price of a stay', () => {

    it('test to show that I can edit a stay price', () => {

        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()

        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.contains('Edit Booking').click()

        cy.get('.currency-switch').clear()
        cy.get('.currency-switch').type('80.00')
        cy.contains('Save all changes').click()
        cy.contains('Back to Booking').click()

        //expand the booking to verify stay price
        cy.get('.box.breakdown > .module__header > .push-right > .fa').click()
        cy.get('.stay > .push-right > .price').should('contain', '£80.00')
        cy.get('.logout').click()
    })

    it('sets edit permission for hotelier to edit stay', () => {

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug +'/permissions')
        cy.contains('Reset Permissions').click()

        cy.get('#permissions-front-desk-Stays-updateStay').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('test to show that I cant edit a price of stay once permission removed', () => {

        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()

        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.contains('Edit Booking').click()

        cy.get('.currency-switch').should('be.disabled')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})