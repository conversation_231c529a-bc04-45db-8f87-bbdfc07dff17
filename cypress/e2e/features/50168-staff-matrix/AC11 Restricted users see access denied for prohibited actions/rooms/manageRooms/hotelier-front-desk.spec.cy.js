import accounts from '../../../../../../fixtures/accounts'
import rooms from '../../../../../../fixtures/rooms'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Room Management: Hotelier Front Desk can create a room', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can create a room', () => {
        cy.visit('/hotels/'+ accounts.cypress_a.slug)
        cy.contains('a', 'Rooms').click({ force: true })
        cy.get('.module__header').should('contain', 'Rooms')
        rooms.forEach(room => {
            cy.contains('a', /Create Room Number\/Name/).click()
            cy.get('form').fillCreateRoomForm(room).submit()
            cy.get('.message.success').should('contain', `Room ${room.name} created`)
            cy.url().should('contain', 'room-types')
        })
    })

    it('can delete rooms', () => {
        cy.visit('/hotels/'+ accounts.cypress_a.slug)
        cy.contains('a', /Rooms/).click({ force: true })
        rooms.forEach(room => {
            cy.contains('.room-index-table td', room.name).selectTrashCan().click()
            cy.get('.message.success').should('contain', `Room ${room.name} deleted`)
        })
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})
