import accounts from '../../../../../../fixtures/accounts'
import rooms from '../../../../../../fixtures/rooms'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Room Management: Hotelier Manager can create a room', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can create a room', () => {
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Rooms').click({force: true})
        cy.get('.module__header').should('contain', 'Rooms')
        rooms.forEach(room => {
            cy.contains('a', /Create Room Number\/Name/).click()
            cy.get('form').fillCreateRoomForm(room).submit()
            cy.get('.message.success').should('contain', `Room ${room.name} created`)
            cy.url().should('contain', 'room-types')
        })
    })

    it('can delete rooms', () => {
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Rooms/).click({force: true})
        rooms.forEach(room => {
            cy.contains('.room-index-table td', room.name).selectTrashCan().click()
            cy.get('.message.success').should('contain', `Room ${room.name} deleted`)
        })
    })

    it('Set manage rooms permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('Reset Permissions').click()
        cy.get('#permissions-manager-Rooms-updateRoom').click()
        cy.get('#permissions-manager-Room_Types-updateRoomType').click()
        cy.get('#permissions-manager-Rooms-deleteRoom').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant create a room', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Rooms').click({force: true})
        cy.get('.module__header').should('contain', 'Rooms')
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/rooms/create', {failOnStatusCode: false})
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('cant create a room Types', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Rooms').click({force: true})
        cy.get('.module__header').should('contain', 'Rooms')
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/room-types/create', {failOnStatusCode: false})
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('cant delete rooms', () => {
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Rooms/).click({force: true})
        cy.get('td.actions span').first().within(() => {
            // cy.get('i.fa-trash').should('not.be.clickable')
            cy.get('i.fa-trash').should('have.class', 'disabled')
        })
    })

    it('cant edit rooms', () => {
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Rooms/).click({force: true})
        cy.get('table#DataTables_Table_1 td.actions').first().within(()=> {
            cy.get('i.fa-pencil')
                .invoke('attr', 'data-cy').then(($url) => {
                    cy.log($url)
                    cy.fn_safeVisit($url)
                })
        })
        cy.get('.module__header').should('contain','Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})
