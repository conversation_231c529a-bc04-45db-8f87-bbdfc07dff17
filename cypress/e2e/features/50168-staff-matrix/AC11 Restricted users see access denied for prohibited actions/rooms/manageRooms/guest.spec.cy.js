import accounts from '../../../../../../fixtures/accounts'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Room Management: Guests can not create a room', () => {

    it('can log in', () => {
        cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
        cy.get('header h1').should('contain.text', 'Check availability')
    })

    it('can not create a room', () => {
        cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/room-types`);
        cy.get('div.title h2').should('contain.text','Hotelier Log in')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})
