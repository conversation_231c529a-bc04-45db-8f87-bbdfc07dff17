import accounts from '../../../../../../fixtures/accounts'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {


describe('Hotelier manager cant visit edit booking', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('Hotelier can edit a booking', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug)

        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.get('.module__header > :nth-child(1) > .edit').click()
        cy.get('#guest_name').clear().type('update name test')
        cy.contains('Submit').click()
        cy.get('.message.success').should('contain', 'updated')
        cy.get('.logout').click()
    })

    it('sets the permission to not be able to edit a booking', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Bookings-updateBooking').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Hotelier should no longer be able to access the edit booking', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.fn_safeVisit('/hotels/'+ accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.get('h2.module__header span span').invoke('attr', 'data-cy').then(($url) => {
            cy.fn_safeVisit($url)
        })
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})