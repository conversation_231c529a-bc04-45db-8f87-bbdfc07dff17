import accounts from '../../../../../../fixtures/accounts'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Hotelier Front Desk - Manage Credit Cards', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('Can add a card to a booking', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.contains('Card').click()
        cy.get('.modal-body > #card-wizard > #create-or-request-step > .card-type-wrapper > #create-card').click()
        cy.get('#name').type('James')
        cy.get('#number').type('4111 1111 1111 1111', {delay:100})
        cy.get('#expiry').type('1225')
        cy.get('#ccv').type('123')
        cy.contains('Submit').click()
    })
    it('Cant delete a card from a booking', () => {
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.get('h2.module__header:contains("Card Ending") i.fa-trash').should('not.exist')
    })

    it('Cant delete a card from a guest profile', () => {
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.get('.guest > .module__header > .fa').click()
        cy.get('h2.module__header:contains("Card Ending") i.fa-trash').should('not.exist')
    })

    it('sets credit card permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Credit_Cards-deleteCard').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Can add a card to a booking', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.contains('Card').click()
        cy.get('.modal-body > #card-wizard > #create-or-request-step > .card-type-wrapper > #create-card').click()
        cy.get('#name').type('James')
        cy.get('#number').type('4111 1111 1111 1111', {delay:100})
        cy.get('#expiry').type('1225')
        cy.get('#ccv').type('123')
        cy.contains('Submit').click()
    })

    it('Can Delete a card from a guest profile', () => {
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.get('.guest > .module__header > .fa').click()
        cy.contains('h2.module__header', 'Card Ending')
            .find('i.fa.fa-trash').click()
        cy.get('#card-delete-password').type(accounts.cypress_a.hotelier.front_desk.password, {delay:0})
        cy.get('[name="jqi_confirm_buttonConfirm"]').click()
    })

    it('Can Delete a card from a booking', () => {
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.contains('h2.module__header', 'Card Ending')
            .find('i.fa.fa-trash').click()
        cy.get('.jqimessage')
            .should('contain', 'Are you sure you want to delete \'Credit Card Ending in 1111\'?')
            .should('contain', 'This will only remove it from this booking. The card will still exist on the guests profile.');
        cy.get('[name="jqi_confirm_buttonYes"]').click();
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})