import accounts from '../../../../../../fixtures/accounts';
import packages from '../../../../../../fixtures/cypress_a/packages';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Onboarding: Hotelier Manager can manage Packages', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can create packages', () => {
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Packages/).click({ force: true })
        packages.forEach(packageObject => {
            cy.contains('a', /Create package/).click()
            cy.contains('.module__header', /Create package/)
            cy.get('form').fillCreatePackageForm(packageObject).submit()
            cy.get('.message.success').should('contain', 'package created')
        })
    })

    it ('can search for a package', () => {
        cy.contains('a', /Packages/).click({ force: true })
        cy.get('.col1 form').fillSearchForm({code: packages[0].code}).submit()
        cy.get('.table__content')
            .should('contain', packages[0].code)
            .should('not.contain', packages[1].code)
    })

    it('can update a package', () => {
        cy.contains('a', /Packages/).click({ force: true })
        cy.get('.col1 form').fillSearchForm({code: packages[0].code}).submit()
        cy.contains('td', packages[0].code).selectEditPencil().click()
        cy.get('form').fillUpdatePackageForm({
            isEnabled: false,
            isVisible: false,
            title: 'My Updated Package',
            selectRoomTypes: ['Single', 'Double']
        }).submit()
        cy.contains('td', packages[0].code).parent('tr').should('contain.text', 'My Updated Package')
    })

    it('can delete a package', () => {
        cy.contains('a', /Packages/).click({ force: true })
        cy.get('.col1 form').fillSearchForm({code: packages[1].code}).submit()
        cy.contains('td', packages[1].code).selectTrashCan().click()
        cy.get('.message.success').should('contain.text', `${packages[1].title} package deleted`)
    })
    it('Set manage Package permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('Reset Permissions').click()
        cy.get('#permissions-manager-Packages-viewPackage').click()
        cy.get('#permissions-manager-Packages-updatePackage').click()
        cy.get('#permissions-manager-Packages-deletePackage').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })
    it('Hotelier Manager Access Denied Packages', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Packages/).click({ force: true })
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    it('Set manage Packages permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Packages-viewPackage').click()
        cy.contains('Save').click()
        //cy.wait(5000)
        cy.get('.logout').click()
    })
    it('Hotelier Manager cant edit Packages', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Packages/).click({ force: true })
        cy.get('td.actions')
            .first().within(()=> {
            cy.get('i.fa-pencil')
                .invoke('attr', 'data-cy').then(($url) => {
                cy.log($url)
                cy.fn_safeVisit($url)
            })
        })
        cy.get('.module__header').should('contain','Access Denied')
    })
    it('Hotelier Manager cant create Packages', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Packages/).click({ force: true })
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/packages/create', {failOnStatusCode: false})
        cy.get('.module__header').should('contain','Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})
