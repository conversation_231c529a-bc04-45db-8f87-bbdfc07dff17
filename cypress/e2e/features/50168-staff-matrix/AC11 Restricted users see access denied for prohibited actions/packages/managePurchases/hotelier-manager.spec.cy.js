import accounts from '../../../../../../fixtures/accounts'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Hotelier Manager cant visit edit Payment', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('Make a payment as a hotelier', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get('.sorting_1 > a').first().click()
        cy.contains('Purchase').click()
        cy.get('#description').type('added by Cypress')
        cy.get('.currency-switch').type('5.99')
        cy.get('.list > :nth-child(2) > label').click()
        cy.contains('Submit').click()
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})