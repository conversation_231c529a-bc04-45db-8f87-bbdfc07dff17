import accounts from '../../../../../../fixtures/accounts'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Hotelier Front desk cant visit edit Stays', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('Hotelier can edit a Stay', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/'+ accounts.cypress_a.slug)
        cy.visit('/hotels/'+ accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get('.sorting_1 > a').first().click()
        cy.get('.box.breakdown > .module__header > .push-right > .fa').click()
        cy.get('.stay > .edit').click()
        cy.get('.currency-switch').clear().type('£70.00')
        cy.contains('Submit').click()
        cy.get('.logout').click()
    })

    it('sets the permission to not be able to edit a booking', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/'+ accounts.cypress_a.slug +'/permissions')
        cy.contains('Reset Permissions').click()
        cy.get('#permissions-front-desk-Stays-updateStay').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Hotelier should no longer be able to access the edit Stay', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/'+ accounts.cypress_a.slug)
        cy.visit('/hotels/'+ accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get('.sorting_1 > a').first().click()
        cy.get('.box.breakdown > .module__header > .push-right > .fa').click()
        cy.get('li.stay span').first().invoke('attr', 'data-cy').then(($url) => {
            cy.log($url)
            cy.fn_safeVisit($url)
        })
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})