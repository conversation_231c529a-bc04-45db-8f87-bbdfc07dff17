import accounts from '../../../../../../fixtures/accounts';
import venues from '../../../../../../fixtures/venues';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Booking Management: Hotelier Manager can manage Venues', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can create venues', () => {
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Venues/).click({ force: true })
        venues.forEach(venue => {
            cy.contains('a', /Create Venue/).click()
            cy.get('form').fillCreateVenueForm(venue).submit()
            cy.get('.message.success').should('contain', `${venue.type} created`)
            cy.url().should('contain', '/venues')
        })
    })

    it ('can search venues', () => {
        cy.contains('a', /Venues/).click({ force: true })
        cy.get('.col1 form').fillSearchForm({
            code: venues[0].code
        }).submit()
        cy.get('.table__content')
            .should('contain', venues[0].code)
            .should('not.contain', venues[1].code)
    })

    it('can delete a venue', () => {
        let venue = venues[0]
        cy.contains('a', /Venues/).click({ force: true })
        cy.contains('td', venue.code).selectTrashCan().click()
        cy.get('.message.success').should('contain', venue.type + ' deleted')
    })

    it('Set manage Venues permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Venues-viewVenue').click()
        cy.get('#permissions-front-desk-Venues-updateVenue').click()
        cy.get('#permissions-front-desk-Venues-deleteVenue').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant visit /venues', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Venues/).click({force: true})
        cy.get('.module__header').should('contain','Access Denied')
    })

    it('Set manage Venues permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Venues-viewVenue').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant edit an existing venue', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Venues/).click({force: true})
        cy.get('td.actions').first().within(()=> {
            cy.get('i.fa-pencil')
                .invoke('attr', 'data-cy').then(($url) => {
                    cy.log($url)
                    cy.fn_safeVisit($url)
                })
        })
        cy.get('.module__header').should('contain','Access Denied')
    })

    it('cant create a new venue', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Venues/).click({force: true})
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/venues/create', {failOnStatusCode: false})
        cy.get('.module__header').should('contain','Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})
