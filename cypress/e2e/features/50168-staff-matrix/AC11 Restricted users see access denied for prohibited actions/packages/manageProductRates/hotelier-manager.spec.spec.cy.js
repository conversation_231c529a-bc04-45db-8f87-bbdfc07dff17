import accounts from '../../../../../../fixtures/accounts'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Hotelier Manager Permissions Product Rates', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('View and edit product rates as a hotelier', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/products/rateByDate')
        cy.get('.Product-98.price > :nth-child(3) > .currency-switch').type('90')
        cy.contains('Save').click()
    })
    it('Set manage product rates permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Product_Rates-viewProductRate').click()
        cy.get('#permissions-manager-Product_Rates-updateProductRate').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant view product rates', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/products/rateByDate', {failOnStatusCode: false})
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('Set manage product rates permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Product_Rates-viewProductRate').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })
    it('View and edit product rates as a hotelier', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/products/rateByDate')
        cy.get('.Product-98.price > :nth-child(3) > .currency-switch').type('90')
        cy.contains('Save')
            .should('have.class','disabled')
            .should('have.css','cursor','not-allowed')
            //.should('have.attr', 'disabled', 'disabled')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})