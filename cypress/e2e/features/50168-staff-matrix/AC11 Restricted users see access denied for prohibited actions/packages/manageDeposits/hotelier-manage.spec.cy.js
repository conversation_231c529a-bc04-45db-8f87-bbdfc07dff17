import accounts from '../../../../../../fixtures/accounts';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Manage Deposits', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can set up deposits', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Deposit/).click({force: true})
        cy.get('.container > .box > .form__contents > ol > :nth-child(1) > input').type('10')
        cy.get('.container > .box > .form__contents > ol > :nth-child(2) > input').type('5')
    })

    it('Set manage deposits permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Deposits-viewDeposit').click()
        cy.get('#permissions-manager-Deposits-updateDeposit').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant access deposits', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Deposit/).click({force: true})
        cy.get('.module__header').should('contain','Access Denied')
    })

    it('Set manage deposits permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Deposits-viewDeposit').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant change deposits', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Deposit/).click({force: true})
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})