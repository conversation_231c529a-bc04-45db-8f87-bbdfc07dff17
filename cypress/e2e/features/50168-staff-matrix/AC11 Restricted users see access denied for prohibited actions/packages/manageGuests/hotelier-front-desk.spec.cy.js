import guests from '../../../../../../fixtures/guests'
import accounts from '../../../../../../fixtures/accounts';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('On-Boarding: Hotelier managers can create guests', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can create guests', () => {
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', 'Dashboard').should('be.visible')
        cy.contains('a', /Guests/).click({ force: true })
        guests.forEach(guest => {
            // Create Guest
            cy.contains('a', /Create guest/).click()
            cy.contains('.module__header', /Create guest/)
            cy.get('form').fillCreateGuestForm(guest).submit()
            cy.get('.message.success').should('contain', 'Guest account created')
            cy.get('.table__content')
                .should('contain', guest.name)
                .should('contain', guest.email)
        })
    })

    it('can search guests', () => {
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', 'Dashboard').should('be.visible')
        cy.contains('a', /Guests/).click({ force: true })

        // Search Guest by name
        cy.get('.col1 form').fillSearchForm({name: guests[0].name}).submit()
        cy.get('.table__content')
            .should('contain', guests[0].name)
            .should('contain', guests[0].email)
            .should('not.contain', guests[1].name)
            .should('not.contain', guests[1].email)

        // Search Guest by email
        cy.get('.col1 form').fillSearchForm({email: guests[0].email}).submit()
        cy.get('.table__content')
            .should('contain', guests[0].name)
            .should('contain', guests[0].email)
            .should('not.contain', guests[1].name)
            .should('not.contain', guests[1].email)

        // Search for a garbage string
        cy.get('.col1 form').fillSearchForm({email: 'q!w"e£r$t%y^u&i*o'}).submit()
        cy.get('.table__content')
            .should('contain', 'No data available in table')
    })

    it('can delete guests', () => {
        guests.forEach(guest => {
            cy.fn_safeVisit('/hotels/cypress-a/guests')
            cy.contains('td', guest.email).selectTrashCan().click()
            cy.get('.message.success').should('contain', 'Guest account deleted')
        })
    })
    it('Set manage guest permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('Reset Permissions').click()
        cy.get('#permissions-front-desk-Guests-updateGuest').click()
        cy.get('#permissions-front-desk-Guests-deleteGuest').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })
    it('Hotelier front desk cant edit guests', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Guests/).click({ force: true })
        cy.get('td.actions')
            .first().within(()=> {
            cy.get('span')
                .invoke('attr', 'data-cy').then(($url) => {
                cy.log($url)
                cy.fn_safeVisit($url)
            })
        })
        cy.get('.module__header').should('contain','Access Denied')
    })
    it('cant create guests', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', /Guests/).click({ force: true })
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/guests/create', {failOnStatusCode: false})
        cy.get('.module__header').should('contain','Access Denied')
    })
    
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})
