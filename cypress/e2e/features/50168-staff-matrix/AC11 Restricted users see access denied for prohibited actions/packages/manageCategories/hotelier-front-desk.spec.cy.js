import accounts from '../../../../../../fixtures/accounts';
import categories from '../../../../../../fixtures/categories';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Onboarding: Hotelier Manager can manage Categories', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can create categories', () => {
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Categories/).click()
        categories.forEach(category => {
            cy.contains('a', /Create category/).click()
            cy.get('form').fillCreateCategoryForm(category).submit()
            cy.get('.message.success').should('contain', `Category ${category.title} created`)
            cy.get('.table__content')
                .should('contain', category.title)
                .should('contain', category.code)
        })
    });

    it('can search categories', () => {
        cy.contains('a', /Categories/).click()
        cy.get('.col1 form').fillSearchForm({
            code: categories[0].code
        }).submit()
        cy.get('.table__content')
            .should('contain', categories[0].code)
            .should('not.contain', categories[1].code)
    });

    it('can delete a category', () => {
        let category = categories[0]
        cy.contains('a', /Categories/).click({force: true})
        cy.contains('td', category.code).selectTrashCan().click()
        cy.get('.message.success').should('contain', category.title + ' deleted')
    })

    it('Set manage Categories permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('Reset Permissions').click()
        cy.get('#permissions-front-desk-Categories-viewCategory').click()
        cy.get('#permissions-front-desk-Categories-updateCategory').click()
        cy.get('#permissions-front-desk-Categories-deleteCategory').click()
        cy.contains('Save').click()
        //cy.wait(5000)
        cy.get('.logout').click()
    })

    it('Hotelier Front Desk Access Denied categories', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Categories/).click()
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('Set manage Categories permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Categories-viewCategory').click()
        cy.contains('Save').click()
        //cy.wait(5000)
        cy.get('.logout').click()
    })

    it('Hotelier Front Desk cant edit categories', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Categories/).click({ force: true })
        cy.get('td.actions')
            .first().within(()=> {
                cy.get('i.fa-pencil')
                    .invoke('attr', 'data-cy').then(($url) => {
                        cy.log($url)
                        cy.fn_safeVisit($url)
                    })
            })
        cy.get('.module__header').should('contain','Access Denied')
    })

    it('Hotelier Front Desk cant edit categories', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Categories/).click()
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/categories/create', {failOnStatusCode: false})
        cy.get('.module__header').should('contain','Access Denied')
    })

    // it('Reset Permissions', () => {
    //     cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    //     cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
    //     cy.contains('button', 'Reset Permissions').click()
    // })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})
