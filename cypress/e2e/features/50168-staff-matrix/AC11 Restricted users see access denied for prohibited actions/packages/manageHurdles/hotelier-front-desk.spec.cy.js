import accounts from '../../../../../../fixtures/accounts';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Plan Management: Hotelier Front Desk cannot create a rate hurdle', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('cannot create a rate hurdle', () => {
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        // Set up listeners
        cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
        cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
        cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
        cy.intercept({method: 'POST', url: '/hurdles/'}).as('fetchRateHurdles');
        cy.intercept({method: 'POST', url: '/hurdles/create'}).as('createRateHurdle');
        cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
        cy.contains('a', 'Rate Hurdles').click({ force: true })
            .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRateHurdles'])
        cy.contains('a', 'Create New Hurdle').should('not.exist')
    })

    it('Set manage hurdles permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Hurdles-viewHurdle').click()
        cy.get('#permissions-front-desk-Hurdles-updateHurdle').click()
        cy.get('#permissions-front-desk-Hurdles-deleteHurdle').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
        cy.wait(5000)
    })

    it('cant view hurdles', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Rate Hurdles').click({ force: true })
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})