import accounts from '../../../../../../fixtures/accounts'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {

describe('Hotelier Manager - Manage Gifts', () => {

    before(() => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()

        // Delete Gift - if it exists
        cy.query(`DELETE r
                  FROM rewards r
                  LEFT JOIN hotels h ON r.hotel_id = h.id
                  WHERE h.slug = ?
                  AND code = ?`, [accounts.cypress_a.slug, 'cypress_a_reward'])

        // Create a Gift
        cy.query(`INSERT INTO rewards (hotel_id, type, code, description, sortOrder) 
                SELECT  id,
                    'Test Reward' as type,
                    'cypress_a_reward' as code,
                    'Cypress Test Reward (Hotel front desk)' as description,
                    0 as sortOrder
                FROM hotels
                WHERE slug = ?`, [accounts.cypress_a.slug]);
    })

    it('Hotelier can create a Gift', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(3) > .sorting_1 > a').click()
        cy.get('a.button')
            .contains('Reward')
            .click()
        cy.contains('Submit').click()
    })

    it('Hotelier can edit a gift', () =>{
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(3) > .sorting_1 > a').click()
        cy.get('h2.module__header')
            .contains('Rewards')
            .siblings('.box__details')
            .find('a.edit.fa.fa-pencil')
            .first().click()
        cy.get('#isFulfilled').select('Yes')
        cy.contains('Submit').click()
    })

    it('Hotelier can delete a gift', () => {
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(3) > .sorting_1 > a').click()
        cy.get('h2.module__header')
            .contains('Rewards')
            .siblings('.box__details')
            .find('a.edit.fa.fa-pencil')
            .first().click()
        cy.get('#deletepassword').type(accounts.cypress_a.hotelier.manager.password)
        cy.contains('Delete This Gift').click()
    })

    it('Set gifts permissions', ()=>{
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Gifts-updateGift').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Hotelier can still create a Gift', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(3) > .sorting_1 > a').click()
        cy.get('a.button')
            .contains('Reward')
            .click()
        cy.contains('Submit').click()
    })

    it('Hotelier cant edit a gift', () =>{
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(3) > .sorting_1 > a').click()
        cy.get('h2.module__header')
            .contains('Rewards')
            .siblings('.box__details')
            .find('span.edit.fa.fa-pencil.disabled')
            .invoke('attr', 'data-cy')
            .then(($url) => {
                cy.log($url)
                cy.fn_safeVisit($url)
            })
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('Set gifts permissions', ()=> {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Gifts-updateGift').click()
        cy.get('#permissions-manager-Gifts-deleteGift').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Hotelier cant delete a gift', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(3) > .sorting_1 > a').click()
        cy.get('h2.module__header')
            .contains('Rewards')
            .siblings('.box__details')
            .find('a.edit.fa.fa-pencil')
            .first().click()
        cy.get('#deletepassword').should('not.exist')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	

})