import accounts from '../../../../../../fixtures/accounts';
import vouchers from '../../../../../../fixtures/cypress_a/vouchers';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Booking Management: Hotelier Manager can manage Vouchers', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can create vouchers', () => {
        cy.fn_safeVisit('/hotels/cypress-a')
        // Navigate to Vouchers
        cy.contains('a', /Vouchers/).click({force: true})
        // Create Vouchers
        vouchers.forEach(voucher => {
            cy.contains('a', /Create voucher/).click()
            cy.get('form').fillCreateVoucherForm(voucher).submit()
            cy.get('.message.success').should('contain', `${voucher.description} Voucher created`)
            cy.get('.voucher-index-table').should('contain', voucher.description.substr(0, 10))
        })
    })

    it('can search vouchers', () => {
        let voucher = vouchers[1]
        cy.contains('a', /Vouchers/).click({force: true})
        // Search
        cy.get('.col1 form').fillSearchForm({code: voucher.code}).submit()
        // Confirm
        cy.get('.voucher-index-table').should('contain', voucher.code)
    })

    it('can edit a voucher', () => {
        let voucher = vouchers[1]
        let original_code = voucher.code
        // Select a Voucher to Edit
        cy.contains('a', /Vouchers/).click({force: true})
        cy.contains('td', original_code).selectEditPencil().click()
        // Update
        cy.get('form').fillUpdateVoucherForm(voucher).submit()
        // Confirm
        cy.get('.voucher-index-table').should('contain', original_code)
    })

    it('can delete a voucher', () => {
        let voucher = vouchers[2]
        // Delete a Voucher
        cy.contains('a', /Vouchers/).click({force: true})
        cy.contains('td', voucher.code).selectTrashCan().click()
        // Confirm
        cy.get('.message.success').should('contain', voucher.description + ' Voucher deleted')
    })

    it('Set manage Voucher permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Vouchers-viewVoucher').click()
        cy.get('#permissions-manager-Vouchers-updateVoucher').click()
        cy.get('#permissions-manager-Vouchers-deleteVoucher').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant visit vouchers', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')

        cy.fn_safeVisit('/hotels/cypress-a')
        // Navigate to Vouchers
        cy.contains('a', /Vouchers/).click({force: true})
        cy.get('.module__header').should('contain','Access Denied')
    })

    it('Set manage Voucher permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Vouchers-viewVoucher').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant edit a voucher vouchers', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')

        cy.fn_safeVisit('/hotels/cypress-a')
        // Navigate to Vouchers
        cy.contains('a', /Vouchers/).click({force: true})
        cy.get('td.actions')
            .first().within(()=> {
                cy.get('i.fa-pencil')
                    .invoke('attr', 'data-cy').then(($url) => {
                        cy.log($url)
                        cy.fn_safeVisit($url)
                    })
            })
        cy.get('.module__header').should('contain','Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})
