import accounts, {cypress_a} from '../../../../../../fixtures/accounts';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('On-Boarding: Hotelier managers can create Images', () => {
    const uuid = () => Cypress._.random(0, 1e6)
    const id = uuid()
    const testname = `testname${id}`

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()

        // Create an Image record, to trigger the correct layout
        cy.query(`INSERT INTO images (hotel_id, name, description, filename, sortOrder) 
                SELECT  id,
                    'cypress_image_placeholder' as name,
                    'Cypress Blank Image record' as description,
                    CONCAT(rand(), 'image.png') as filename,
                    0 as sortOrder
                FROM hotels
                WHERE slug = ?`, [accounts.cypress_a.slug]);
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')

    })

    it('can create an image', () => {
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', /Gallery/).click({force: true})
        cy.contains('Upload Image').click()
        cy.get('#name').type(testname)
        cy.get('#description').type(testname)
        cy.get('input[type=file]').selectFile('tests/cypress/fixtures/image.png')
        cy.contains('Submit').click()
    })

    it('can delete an image', () => {
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', /Gallery/).click({force: true})
        cy.get('h2.module__header').first().within(() => {
            cy.get('a.edit.fa.fa-pencil').click();
        })
        cy.get('#deletepassword').type(accounts.cypress_a.hotelier.front_desk.password)
        cy.contains('Delete This Image').click()
    })

    it('set delete image permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Images-deleteImage').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant delete an image', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', /Gallery/).click({force: true})
        cy.get('h2.module__header').first().within(() => {
            cy.get('a.edit.fa.fa-pencil').click();
        })
        cy.get('#deletepassword').should('not.exist')
    })

    it('set update image permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Images-updateImage').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant edit an image', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', /Gallery/).click({force: true})
        cy.get('.module__header i[data-cy]').invoke('attr', 'data-cy').then(($url) => {
            cy.log($url)
            cy.fn_safeVisit($url)
        })
        cy.get('.module__header').should('contain','Access Denied')

    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})