import accounts from '../../../../../../fixtures/accounts'
import policies from '../../../../../../fixtures/policies'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Hotelier Front Desk - Manage Policies', () => {

    // const uuid = () => Cypress._.random(0, 1e6)
    // const id = uuid()
    // const testname = `testname${id}`

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('Hotelier can create a Policy', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Policies/).click({force: true})
        // Create 2 policies
        policies.forEach(policy => {
            cy.contains('Create policy').click()
            cy.get('.name').type(policy.name)
            cy.get('.hours').type(policy.hours)
            cy.get('.days').type(policy.days)
            cy.get('[type="checkbox"]').check()
            cy.contains('Submit').click()
        })
    })

    it('Hotelier can edit a Policy', () => {
        cy.contains('a', /Policies/).click({force: true})
        cy.get('td.actions a.fa.fa-pencil').first().click()
        cy.get('.hours').type('2')
        cy.contains('Submit').click()
    })

    it('Hotelier can delete a Policy', () => {
        cy.contains('a', /Policies/).click({force: true})
        cy.get('td.actions i.fa-trash').first().click()
        cy.get('.message.success').should('contain.text', 'deleted')
    })

    it('sets Policy permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Policies-viewPolicy').click()
        cy.get('#permissions-front-desk-Policies-updatePolicy').click()
        cy.get('#permissions-front-desk-Policies-deletePolicy').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant visit policies', ()=>{
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Policies/).click({force: true})
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('sets Policy permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Policies-viewPolicy').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Hotelier cant edit a policy', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Policies/).click({force: true})
        cy.get('td.actions').first().within(() => {
            cy.get('i.fa-pencil')
                .invoke('attr', 'data-cy').then(($url) => {
                    cy.log($url)
                    cy.fn_safeVisit($url)
                })
        })
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('Hotelier cant create a policy', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Policies/).click({force: true})
        cy.contains('Create policy')
            .should('have.class', 'disabled')
            .should('have.css', 'cursor', 'not-allowed')
    })

    it('Hotelier cant visit create a policy', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/policies/create', { failOnStatusCode: false })
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})