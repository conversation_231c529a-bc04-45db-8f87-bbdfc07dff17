import accounts from '../../../../../../fixtures/accounts';
import products from '../../../../../../fixtures/cypress_a/products';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Onboarding: Hotelier Front Desk can manage Products', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it ('can create products', () => {
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        // Navigate to Products
        cy.contains('a', /Products/).click({ force: true })
        // Create Products
        products.forEach(product => {
            cy.contains('a', /Create product/).click()
            cy.get('form').fillCreateProductForm(product).submit()
            cy.get('.message.success').should('contain', `${product.name} created`)
            cy.get('.table__content')
                .should('contain', product.name)
                .should('contain', product.code)
        })
    })

    it ('can search products', () => {
        // Navigate to Products
        cy.contains('a', /Products/).click({ force: true })
        // Search Products
        cy.get('.col1 form').fillSearchForm({
            code: products[0].code
        }).submit()
        // Confirm filtered results
        cy.get('.table__content')
            .should('contain', products[0].code)
            .should('not.contain', products[1].code)
    })

    it('can delete a product', () => {
        let product = products[0]
        // Delete a Product
        cy.contains('a', /Products/).click({ force: true })
        cy.contains('td', product.code).selectTrashCan().click()
        // Code Error
        // cy.get('.message.success').should('contain', product.type + ' deleted')
        // Tmp
        cy.get('.product-index-table').should('not.contain', product.type)
    })
    it('Set manage Products permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Products-viewProduct').click()
        cy.get('#permissions-front-desk-Products-updateProduct').click()
        cy.get('#permissions-front-desk-Products-deleteProduct').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it ('can create products', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Products/).click({force: true})
        cy.get('.module__header').should('contain','Access Denied')
    })
    it('Set manage Products permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Products-viewProduct').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })
    it ('can create products', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Products/).click({force: true})
        cy.get('td.actions').first()
            .within(()=> {
                cy.get('i.fa-pencil')
                    .invoke('attr', 'data-cy').then(($url) => {
                        cy.log($url)
                        cy.fn_safeVisit($url)
                    })
            })
        cy.get('.module__header').should('contain','Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})
