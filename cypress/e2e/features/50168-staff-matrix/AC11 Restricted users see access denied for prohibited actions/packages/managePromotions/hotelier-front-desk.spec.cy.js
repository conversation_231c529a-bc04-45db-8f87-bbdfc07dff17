import accounts from '../../../../../../fixtures/accounts'
import rates from '../../../../../../fixtures/cypress_a/rates';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
var rate = rates[0]
rate.manageOnly = 'No - Not bookable in PMS';

describe('Hotelier Front Desk - Manage Promotions', () => {
    const uuid = () => Cypress._.random(0, 1e6)
    const id = uuid()
    const testname = `testname${id}`

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()

        // Create a non-bookable Rate
        cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
        cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
        cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
        cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
        cy.intercept({method: 'POST', url: '/rates/create'}).as('createRate');
        // Navigate to Rate Plans
        cy.contains('a', /Rate Plans/)
            .click({ force: true })
            .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRates'])
        cy.contains('a', /Create New Rate/).click({force: true}).wait('@fetchRoomTypes')
        cy.get('.page-title').should('contain.text', 'Create Rate')
        cy.get('form').fillCreateRateForm(rate).submit().wait('@createRate')
        cy.url().should('contain', '/v2/rates/overview')
    })

    it('Hotelier can create a Promotion', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Promotional Codes/).click({force: true})
        cy.contains('Create promotion').click()
        cy.get('.code').type(testname)
        cy.get('.description').type(testname)
        cy.get('[type="checkbox"]').check()
        cy.contains('Submit').click()
    })

    it('Hotelier can edit a Promotion', () => {
        cy.contains('a', /Promotional Codes/).click({force: true})
        cy.get('td.actions a.fa.fa-pencil').first().click()
        cy.get('#description').clear().type(testname)
        cy.contains('Submit').click()
    })

    it('Hotelier can delete a Promotion', () => {
        cy.contains('a', /Promotional Codes/).click({force: true})
        cy.get('td.actions i.fa-trash').first().click()
        cy.get('.message.success').should('contain.text', 'deleted')
    })

    it('sets Promotions permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Promotional_Codes-viewPromoCode').click()
        cy.get('#permissions-front-desk-Promotional_Codes-updatePromoCode').click()
        cy.get('#permissions-front-desk-Promotional_Codes-deletePromoCode').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('cant visit Promotions', ()=>{
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Promotional Codes/).click({force: true})
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('sets Promotions permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Promotional_Codes-viewPromoCode').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Hotelier cant edit a Promotions', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Promotional Codes/).click({force: true})
        cy.get('td.actions').first()
            .within(() => {
                cy.get('i.fa-pencil')
                    .invoke('attr', 'data-cy').then(($url) => {
                        cy.log($url)
                        cy.fn_safeVisit($url)
                    })
            })
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('Hotelier cant create a Promotions', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Promotional Codes/).click({force: true})
        cy.contains('Create promotion')
            .should('have.class', 'disabled')
            .should('have.css', 'cursor', 'not-allowed')
    })

    it('Hotelier cant visit create a Promotions', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/promotions/create', { failOnStatusCode: false })
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})