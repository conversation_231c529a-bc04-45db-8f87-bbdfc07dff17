import corporations from '../../../../../../fixtures/corporations'
import accounts from '../../../../../../fixtures/accounts';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('On-Boarding: Hotelier Manager can create corporations', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can create corporations', () => {
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', 'Dashboard').should('be.visible')
        cy.contains('a', /Corporations/).click({ force: true })
        corporations.forEach(corporation => {
            cy.contains('a', /Create corporation/).click()
            cy.contains('.module__header', /Create corporation/)
            cy.get('form').fillCreateCorporationForm(corporation).submit()
            cy.get('.message.success').should('contain', 'Corporation account created')
            cy.get('.table__content')
                .should('contain', corporation.name)
                .should('contain', corporation.email)
        })
    })

    it('can search corporations', () => {
        cy.contains('a', 'Dashboard').should('be.visible')
        cy.contains('a', /Corporations/).click({ force: true })
        // Search Corporation by name
        cy.get('.col1 form').fillSearchForm({name: corporations[0].name}).submit()
        cy.get('.table__content')
            .should('contain', corporations[0].name)
            .should('contain', corporations[0].email)
            .should('not.contain', corporations[1].name)
            .should('not.contain', corporations[1].email)
        // Search Corporation by email
        cy.get('.col1 form').fillSearchForm({email: corporations[0].email}).submit()
        cy.get('.table__content')
            .should('contain', corporations[0].name)
            .should('contain', corporations[0].email)
            .should('not.contain', corporations[1].name)
            .should('not.contain', corporations[1].email)
        // Search for a garbage string
        cy.get('.col1 form').fillSearchForm({email: 'q!w"e£r$t%y^u&i*o'}).submit()
        cy.get('.table__content')
            .should('contain', 'No data available in table')
    })

    it('can delete corporations', () => {
        corporations.forEach(corporation => {
            cy.fn_safeVisit('/hotels/cypress-a/corporations')
            cy.contains('td', corporation.email).selectTrashCan().click()
            cy.get('.message.success').should('contain', 'Corporation account deleted')
        })
    })

    it('Set manage corporate permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('Reset Permissions').click()
        cy.get('#permissions-manager-Corporations-updateCorporation').click()
        cy.get('#permissions-manager-Corporations-deleteCorporation').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })
    it('Hotelier Manager cant edit corporation', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Corporations/).click({ force: true })
        cy.get('td.actions')
            .first().within(()=> {
                cy.get('span')
                    .invoke('attr', 'data-cy').then(($url) => {
                        cy.log($url)
                        cy.fn_safeVisit($url)
                    })
            })
        cy.get('.module__header').should('contain','Access Denied')
    })
    it('cant create corporations', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', /Corporations/).click({ force: true })
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/corporations/create', {failOnStatusCode: false})
        cy.get('.module__header').should('contain','Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})
