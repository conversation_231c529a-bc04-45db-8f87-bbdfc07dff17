import accounts from '../../../../../../fixtures/accounts'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Hotelier Manager test Payment permissions', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('Can make a payment as a hotelier', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()

        cy.get('.sorting_1 > a').first().click()
        cy.contains('a.button','Add Payment').click()

        // Payment modal - make payment
        cy.get('.modal-body > #payment-wizard > #payment-type-step > .payment-type-wrapper > #payment-goto-softpayment').click()
        cy.get('.modal-body > #payment-wizard > #payment-softpayment-step > #softpayment-form-wrapper > form > :nth-child(1) > #paymentmethod').select('Cash')
        cy.get('.modal-body > #payment-wizard > #payment-softpayment-step > #softpayment-form-wrapper > form > :nth-child(3) > #softpayment-amount').clear().type('£10.00')
        cy.get('.modal-body > #payment-wizard > .bottom-bar > .save-btn').click()
        cy.get('.modal-body > #payment-wizard > .bottom-bar > .save-btn').click()
        cy.get('.modal-body > #payment-wizard > .bottom-bar > .cancel-btn').click()

        cy.get('.box.breakdown > .module__header > .push-right > .fa').click()
        cy.get('.payments > .list > :nth-child(1) > .fa-pencil').click()
        cy.url().should('contain', '/payments').and('contain', '/edit')
        cy.get('.message.notes.info').should('contain.text', 'This payment / refund cannot be deleted. You may only edit the notes.')
    })

    it('UnSet manage Payments permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Payments-updatePayment').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Cannot edit a payment as a hotelier', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get('.sorting_1 > a').first().click()

        cy.get('.box.breakdown > .module__header > .push-right > .fa').click()
        cy.get('.payments .list span').first()
            .should('have.class', 'disabled')

        cy.get('.payments .list span').first()
            .invoke('attr', 'data-cy')
            .then(($url) => {
                cy.log($url)
                cy.fn_safeVisit($url)
            })
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})