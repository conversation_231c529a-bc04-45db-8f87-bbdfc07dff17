import outlets from '../../../../../../fixtures/outlets'
import accounts from '../../../../../../fixtures/accounts';
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Booking Management: Hotelier Front Desk can manage Outlets', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('can log in', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
    })

    it('can create outlets', () => {
        cy.fn_safeVisit('/hotels/cypress-a')
        cy.contains('a', /Outlets/).click({ force: true })
        outlets.forEach(outlet => {
            cy.contains('a', /Create Outlet/).click()
            cy.get('form').fillCreateOutletForm(outlet).submit()
            cy.get('.message.success').should('contain', `${outlet.type} created`)
            cy.url().should('contain', '/outlets')
        })
    })

    it('can search outlets', () => {
        cy.contains('a', /Outlets/).click({ force: true })
        cy.get('.col1 form').fillSearchForm({
            code: outlets[0].code
        }).submit()
        cy.get('.table__content')
            .should('contain', outlets[0].code)
            .should('not.contain', outlets[1].code)
    })

    it('can delete an outlet', () => {
        let outlet = outlets[0];
        cy.contains('a', /Outlets/).click({ force: true })
        cy.contains('td', outlet.code).selectTrashCan().click()
        cy.get('.message.success').should('contain', outlet.type + ' deleted')
    })
    it('Set manage outlets permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Outlets-viewOutlet').click()
        cy.get('#permissions-front-desk-Outlets-updateOutlet').click()
        cy.get('#permissions-front-desk-Outlets-deleteOutlet').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })
    // it('cant visit outlets', () => {
    //     cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
    //     cy.get('.message.success').should('contain', 'Logged in')
    //
    //     cy.fn_safeVisit('/hotels/cypress-a')
    //     // Navigate to Vouchers
    //     cy.contains('a', /Outlets/).click({force: true})
    //     cy.get('td.actions')
    //         .first().within(()=> {
    //         cy.get('a.fa-eye').click()
    //     })
    //     cy.get('.module__header').should('contain','Access Denied')
    // })
    it('Hotelier Front Desk view outlets', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Outlets/).click({ force: true })
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('Hotelier Front Desk cant edit outlets', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/outlets/bar')
    })

    it('Set manage outlets permission', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-front-desk-Outlets-viewOutlet').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Hotelier Front Desk cant edit outlets', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        cy.get('.message.success').should('contain', 'Logged in')
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Outlets/).click({ force: true })
        cy.get('td.actions')
            .first().within(() => {
            cy.get('i.fa-pencil')
                .invoke('attr', 'data-cy').then(($url) => {
                cy.log($url)
                cy.fn_safeVisit($url)
            })
        })
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})