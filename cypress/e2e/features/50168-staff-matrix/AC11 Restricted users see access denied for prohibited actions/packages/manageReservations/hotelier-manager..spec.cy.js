import accounts from '../../../../../../fixtures/accounts'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Hotelier manager cant visit edit Reservation', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('Hotelier can edit a Reservation', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/'+ accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.get('.box.breakdown > .module__header > .push-right > .fa').click()
        cy.get('li.reservation').first().within(() => {
            cy.get('a.edit.fa.fa-pencil').first().click();
        })
        cy.get('#allowEposPostings').select('No')
        cy.contains('Submit').click()
        cy.get('.logout').click()
    })

    it('sets the permission to not be able to edit a Reservation', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/'+ accounts.cypress_a.slug +'/permissions')
        cy.get('#permissions-manager-Reservations-updateReservation').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Hotelier should no longer be able to access the edit Reservation', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/'+ accounts.cypress_a.slug)
        cy.contains('a', 'Bookings').click()
        cy.get(':nth-child(1) > .sorting_1 > a').click()
        cy.get('.box.breakdown > .module__header > .push-right > .fa').click()
        cy.get('.reservations').find('span[data-cy]')
            .invoke('attr', 'data-cy').then(($url) => {
                cy.log($url)
                cy.fn_safeVisit($url)
            })
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})	
})