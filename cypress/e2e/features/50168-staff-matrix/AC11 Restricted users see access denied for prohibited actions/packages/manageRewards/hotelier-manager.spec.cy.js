import accounts from '../../../../../../fixtures/accounts'
import rewards from '../../../../../../fixtures/rewards'
import TestFilters from '../../../../../../support/filterTests';

TestFilters(['permissionsMatrix'], () => {
describe('Hotelier Manager - Manage Rewards', () => {

    before('Reset Permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
    })

    it('Hotelier can create a reward', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Rewards/).click({force: true})

        rewards.forEach(reward => {
            cy.contains('Create Reward').click()
            cy.get('.type').type(reward.type)
            cy.get('.code').type(reward.code)
            cy.get('.description').type(reward.description)
            cy.get('.points').type(reward.points)
            cy.contains('Submit').click()
        })
    })

    it('Hotelier can delete a reward', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Rewards/).click({force: true})
        cy.get('td.actions i.fa-trash').first().click()
        cy.get('.message.success').should('contain.text', 'deleted')
    })

    it('sets rewards permissions', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.get('#permissions-manager-Rewards-updateReward').click()
        cy.get('#permissions-manager-Rewards-deleteReward').click()
        cy.contains('Save').click()
        cy.get('.logout').click()
    })

    it('Hotelier cant edit a reward', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Rewards/).click({force: true})
        cy.get('td.actions').first().within(() => {
            cy.get('i.fa-pencil')
                .invoke('attr', 'data-cy').then(($url) => {
                    cy.log($url)
                    cy.fn_safeVisit($url)
                })
        })
        cy.get('.module__header').should('contain', 'Access Denied')
    })

    it('Hotelier cant create a reward', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug)
        cy.contains('a', /Rewards/).click({force: true})
        cy.contains('Create Reward')
            .should('have.class', 'disabled')
            .should('have.css', 'cursor', 'not-allowed')
    })

    it('Hotelier cant visit create a reward', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        cy.visit('/hotels/' + accounts.cypress_a.slug + '/rewards/create', { failOnStatusCode: false })
        cy.get('.module__header').should('contain', 'Access Denied')
    })
    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})