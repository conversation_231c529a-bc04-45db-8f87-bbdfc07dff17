import TestFilters from "../../../../support/filterTests";
import accounts from "../../../../fixtures/accounts";
import { specialCharacterData } from "../../../../fixtures/cypress_a/notes";
import { activityReportPage } from "../../../../support/pageObjectModel/Pages/activityReport";
import { bookingHubPage } from "../../../../support/pageObjectModel/Pages/bookingHub";
import { bookingsPage } from "../../../../support/pageObjectModel/Pages/bookings";
import { guestProfilePage } from "../../../../support/pageObjectModel/Pages/guestProfile";
import { notesPage } from "../../../../support/pageObjectModel/Pages/unifiedNotesPage";

const date = cy.helpers.dateYMD();
const hotelSlug = accounts.cypress_a.slug
const notes = ['booking', 'guest', 'housekeeping', 'channel'];

TestFilters(['Notes'], () => {

describe('Hotelier Manager: Is able to filter bookings by note type', () => {

    before('reset test seed', () => {
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
    })

    beforeEach('Hotelier Manager is able to login and access activity report', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        bookingsPage.open(hotelSlug)

        //Enter notes in 3 different reservations
        bookingsPage.clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.clearAllNoteFields()
        cy.get(notesPage.selectors.bookingNoteField).type(specialCharacterData.bookingNotes)
        notesPage.assertNoteSavedAlert()

        bookingsPage.open(hotelSlug)
        bookingsPage.clickSecondExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.clearAllNoteFields()
        cy.get(notesPage.selectors.channelNoteField).type(specialCharacterData.channelNotes)
        notesPage.assertNoteSavedAlert()

        bookingsPage.open(hotelSlug)
        bookingsPage.clickThirdExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.clearAllNoteFields()
        cy.get(notesPage.selectors.housekeepingNoteField).type(specialCharacterData.housekeepingNotes)
        notesPage.assertNoteSavedAlert()

        activityReportPage.open(hotelSlug, date)
    })

    it('verifies that notes filters are displayed and clickable', () => {

        activityReportPage.open(hotelSlug, date)

        //Verify that note boxes are all unchecked as you visit the page

        notes.forEach(note => {
            activityReportPage.verifyUncheckedState(note);
            activityReportPage.verifyNoteCheckboxFunctionality(note);
            activityReportPage.unselectCheckbox(note)
            activityReportPage.verifyUncheckedState(note)
        });
        

        //verify select all checkbox selects all filters
        cy.get(activityReportPage.selectors.notesCheckAllCheckbox).should('be.visible')
        cy.get(activityReportPage.selectors.notesCheckAllCheckbox).should('be.visible').check().should('be.checked');
        
        notes.forEach(note => {
            activityReportPage.verifyCheckedState(note);
        });

        //verify that unchecking select all checkbox deselects all checkboxes 
        cy.get(activityReportPage.selectors.notesCheckAllCheckbox).uncheck().should('not.be.checked')
        notes.forEach(note => {
            activityReportPage.verifyUncheckedState(note);
        });
    })

    it('Verifies that only reservations with guest notes are displayed when guest checkbox is checked', () => {

        //remove booking note from reservation and add guest note in guest profile 
        bookingsPage.open(hotelSlug)
                    .clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        cy.get(notesPage.selectors.bookingNoteField).clear()
        notesPage.assertNoteSavedAlert()

        //Navigate to guest profile and enter guest note
        notesPage.clickBackToBookingSummary()
        bookingHubPage.clickGuestAccountButton()
        cy.get(guestProfilePage.selectors.guestNoteField).type(`${specialCharacterData.guestNotes}`)
        guestProfilePage.assertNoteSavedAlert()

        activityReportPage.open(hotelSlug, date)
        //check guest checkbox
        activityReportPage.selectCheckbox(notes[1])
        activityReportPage.clickSearchButton()
        activityReportPage.assertIsVisible(specialCharacterData.guestNotes)
        cy.contains(`${specialCharacterData.channelNotes}`).should('not.exist')
        cy.contains(`${specialCharacterData.housekeepingNotes}`).should('not.exist');

        //Clear guest note 
        bookingsPage.open(hotelSlug)
                    .clickFirstExistingBooking()
        bookingHubPage.clickGuestAccountButton()
        cy.get(guestProfilePage.selectors.guestNoteField).clear()
        guestProfilePage.assertNoteSavedAlert()

    })

    it('Verifies that only reservations with booking notes are displayed when the booking checkbox is checked', () => {
        //check booking checkbox
        activityReportPage.selectCheckbox(notes[0])
        activityReportPage.clickSearchButton()
        activityReportPage.assertIsVisible(specialCharacterData.bookingNotes)
        cy.contains(`${specialCharacterData.channelNotes}`).should('not.exist')
        cy.contains(`${specialCharacterData.housekeepingNotes}`).should('not.exist');
    })

    it('Verifies that only reservations with channel notes are displayed when the channel checkbox is checked', () => {
        //check channel checkbox
        activityReportPage.selectCheckbox(notes[3])
        activityReportPage.clickSearchButton()
        activityReportPage.assertIsVisible(specialCharacterData.channelNotes)
        cy.contains(`${specialCharacterData.bookingNotes}`).should('not.exist')
        cy.contains(`${specialCharacterData.housekeepingNotes}`).should('not.exist');
    })

    it('Verifies that only reservations with houskeeping notes are displayed when the housekeeping checkbox is clicked', () => {
        //check housekeeping checkbox
        activityReportPage.selectCheckbox(notes[2])
        activityReportPage.clickSearchButton()
        activityReportPage.assertIsVisible(specialCharacterData.housekeepingNotes)
        cy.contains(`${specialCharacterData.bookingNotes}`).should('not.exist')
        cy.contains(`${specialCharacterData.channelNotes}`).should('not.exist')
    })

    it('Verifies that only reservations with both booking and channel notes are displayed when booking and channel checkboxes are checked', () => {
        //add channel note 
        bookingsPage.open(hotelSlug)
                    .clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        cy.get(notesPage.selectors.channelNoteField).type(specialCharacterData.channelNotes)
        notesPage.assertNoteSavedAlert()

        //navigate to activity report
        activityReportPage.open(hotelSlug, date)
        // check channel and booking checkbox
        activityReportPage.selectCheckbox(notes[0])
        activityReportPage.selectCheckbox(notes[3])
        activityReportPage.clickSearchButton()
        activityReportPage.assertIsVisible(specialCharacterData.bookingNotes)
        activityReportPage.assertIsVisible(specialCharacterData.channelNotes)
        cy.contains(`${specialCharacterData.housekeepingNotes}`).should('not.exist');
    })

    it('Verifies only reservations with booking and housekeeping notes are displayed when booking and housekeeping checkboxes are checked', () => {
        //add houskeeping note 
        bookingsPage.open(hotelSlug)
                    .clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        cy.get(notesPage.selectors.housekeepingNoteField).type(specialCharacterData.housekeepingNotes)
        notesPage.assertNoteSavedAlert()

        //navigate to activity report
        activityReportPage.open(hotelSlug, date)
        //select booking and housekeeping checkbox
        activityReportPage.selectCheckbox(notes[0])
        activityReportPage.selectCheckbox(notes[2])
        activityReportPage.clickSearchButton()
        activityReportPage.assertIsVisible(specialCharacterData.bookingNotes)
        activityReportPage.assertIsVisible(specialCharacterData.housekeepingNotes)
        cy.contains(`${specialCharacterData.channelNotes}`).should('not.exist')
    })

    it('Verifies only reservations with channel and houskeeping notes are displayed when channel and housekeeping checkboxes are checked', () => {
        //add houskeeping note 
        bookingsPage.open(hotelSlug)
                    .clickSecondExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        cy.get(notesPage.selectors.housekeepingNoteField).type(specialCharacterData.housekeepingNotes)
        notesPage.assertNoteSavedAlert()

        //navigate to activity report
        activityReportPage.open(hotelSlug)
        //select housekeeping and channel checkbox
        activityReportPage.selectCheckbox(notes[2])
        activityReportPage.selectCheckbox(notes[3])
        activityReportPage.clickSearchButton()
        cy.contains(`${specialCharacterData.bookingNotes}`).should('not.exist')
        activityReportPage.assertIsVisible(specialCharacterData.housekeepingNotes)
        activityReportPage.assertIsVisible(specialCharacterData.channelNotes)
    })

    it('Verifies that clicking Search without checking any checkboxes displays all reservations', () => {

        activityReportPage.clickSearchButton()
        activityReportPage.assertIsVisible(specialCharacterData.housekeepingNotes)
        activityReportPage.assertIsVisible(specialCharacterData.channelNotes)
        activityReportPage.assertIsVisible(specialCharacterData.bookingNotes)
    })

    it('Verifies that only reservations with all notes are displayed when the select all checkbox is clicked', () => {

        //add notes to one reservation 
        bookingsPage.open(hotelSlug)
        cy.get(bookingsPage.selectors.firstExistingBookingRef).invoke('text').then((allNotesRef) => {
            bookingsPage.clickFirstExistingBooking()
            cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        cy.get(notesPage.selectors.channelNoteField).type(specialCharacterData.channelNotes)
        cy.get(notesPage.selectors.housekeepingNoteField).type(specialCharacterData.housekeepingNotes)
        notesPage.assertNoteSavedAlert()
        notesPage.clickBackToBookingSummary()
        bookingHubPage.clickGuestAccountButton()
        cy.get(guestProfilePage.selectors.guestNoteField).type(`${specialCharacterData.guestNotes}`)
        guestProfilePage.assertNoteSavedAlert()

        bookingsPage.open(hotelSlug)
        cy.get(bookingsPage.selectors.secondExistingBookingRef).invoke('text').then((channelOnlyRef) => {
        
        cy.get(bookingsPage.selectors.thirdExistingBookingRef).invoke('text').then((housekeepingOnlyRef) => {
        
        activityReportPage.open(hotelSlug, date)
        cy.get(activityReportPage.selectors.notesCheckAllCheckbox).check()
        activityReportPage.clickSearchButton()

        activityReportPage.assertIsVisible(specialCharacterData.housekeepingNotes)
        activityReportPage.assertIsVisible(specialCharacterData.channelNotes)
        activityReportPage.assertIsVisible(specialCharacterData.bookingNotes)
        activityReportPage.assertIsVisible(specialCharacterData.guestNotes)   
        
        // Assert that other reservations are not displayed
        cy.contains(channelOnlyRef).should('not.exist');
        cy.contains(housekeepingOnlyRef).should('not.exist');
        cy.contains(allNotesRef).should('be.visible');
        });
        });
        })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})
})