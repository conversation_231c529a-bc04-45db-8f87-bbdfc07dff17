import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { bookingsPage } from "../../../../../support/pageObjectModel/Pages/bookings";
import { notesPage } from "../../../../../support/pageObjectModel/Pages/unifiedNotesPage";
import { activityReportPage } from "../../../../../support/pageObjectModel/Pages/activityReport";
import { specialCharacterData } from "../../../../../fixtures/cypress_a/notes";
import { guestProfilePage } from "../../../../../support/pageObjectModel/Pages/guestProfile";

const date = cy.helpers.dateYMD();
const hotelSlug = accounts.cypress_a.slug

TestFilters(['Notes'], () => {

describe('Staff Support: Notes page is accessible by hyperlink/URL', () => {

  before('reset test seed', () => {
    cy.clearCookies();
    cy.visit('/automation/tests/reseed');
    cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)
  });

    beforeEach('Access Unified Notes Page and clear notes', () => {
    bookingsPage.open(hotelSlug)
                .clickFirstExistingBooking()
    cy.get(bookingHubPage.selectors.bookingNotesBox).click()
    notesPage.clearAllNoteFields()
    cy.get(notesPage.selectors.bookingNoteField).type(specialCharacterData.bookingNotes)
    cy.get(notesPage.selectors.channelNoteField).type(specialCharacterData.channelNotes)
    cy.get(notesPage.selectors.housekeepingNoteField).type(specialCharacterData.housekeepingNotes)
    notesPage.assertNoteSavedAlert()
    // notesPage.clearAllNoteFields()
  });

  it('Verify Guest, Booking and Channel note sections are displayed in the correct order', () => {
    activityReportPage.open(hotelSlug, date)
                      .clickSearchButton()
    activityReportPage.assertGuestRowAboveBookingRow()
    activityReportPage.assertBookingRowAboveChannelRow()
    activityReportPage.assertChannelRowAboveHousekeepingRow()
  })

  it('Enter only guest note and verify guest note only is displayed on the activity report', () => {
    notesPage.clearAllNoteFields()
    notesPage.clickBackToBookingSummary()
    bookingHubPage.clickGuestAccountButton()
    cy.get(guestProfilePage.selectors.guestNoteField).type(specialCharacterData.guestNotes)
    guestProfilePage.assertNoteSavedAlert()

    activityReportPage.open(hotelSlug, date)
                      .clickSearchButton()
    cy.get(activityReportPage.selectors.guestNote).should('contain', `${specialCharacterData.guestNotes}`)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.bookingNote)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.channelNote)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.housekeepingNote)
  })

  it('Enter only booking note and verify booking note only is displayed on the activity report', () => {
    notesPage.clearAllNoteFields()
    cy.get(notesPage.selectors.bookingNoteField).type(specialCharacterData.bookingNotes)
    notesPage.assertNoteSavedAlert()

    notesPage.clickBackToBookingSummary()
    bookingHubPage.clickGuestAccountButton()
    cy.get(guestProfilePage.selectors.guestNoteField).clear()
    guestProfilePage.assertNoteSavedAlert()

    activityReportPage.open(hotelSlug, date)
                      .clickSearchButton()
    cy.get(activityReportPage.selectors.bookingNote).should('contain', `${specialCharacterData.bookingNotes}`)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.guestNote)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.channelNote)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.housekeepingNote)
  })

  it('Enter only channel note and verify channel note only is displayed on the activity report', () => {
    notesPage.clearAllNoteFields()
    cy.get(notesPage.selectors.channelNoteField).type(specialCharacterData.channelNotes)
    notesPage.assertNoteSavedAlert()

    activityReportPage.open(hotelSlug, date)
                      .clickSearchButton()
    cy.get(activityReportPage.selectors.channelNote).should('contain', `${specialCharacterData.channelNotes}`)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.guestNote)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.bookingNote)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.housekeepingNote)
  }),

  it('Enter only housekeeping note and verify housekeeping note only is displayed on the activity report', () => {
    notesPage.clearAllNoteFields()
    cy.get(notesPage.selectors.housekeepingNoteField).type(specialCharacterData.housekeepingNotes)
    notesPage.assertNoteSavedAlert()

    activityReportPage.open(hotelSlug, date)
                      .clickSearchButton()
    cy.get(activityReportPage.selectors.housekeepingNote).should('contain', `${specialCharacterData.housekeepingNotes}`)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.guestNote)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.bookingNote)
    activityReportPage.assertIsNotVisible(activityReportPage.selectors.channelNote)
  })
  
  afterEach(function () {
    cy.fn_afterEachJira(this.currentTest)
  })

})
})
    
