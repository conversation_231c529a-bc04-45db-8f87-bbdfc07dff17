import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts";
import { specialCharacterData } from "../../../../../fixtures/cypress_a/notes";
import { conferenceCreationModal } from "../../../../../support/pageObjectModel/Components/conferenceCreationModal";
import { eventCalendarPage } from "../../../../../support/pageObjectModel/Pages/eventCalendar";
import { payPage } from "../../../../../support/pageObjectModel/Pages/pay";
import { guestDetails } from "../../../../../fixtures/guestDetails";
import { guestDetailsPage } from "../../../../../support/pageObjectModel/Pages/guestDetails";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { bookingsPage } from "../../../../../support/pageObjectModel/Pages/bookings";
import { guestProfilePage } from "../../../../../support/pageObjectModel/Pages/guestProfile";
import { notesPage } from "../../../../../support/pageObjectModel/Pages/unifiedNotesPage";
import { activityReportPage } from "../../../../../support/pageObjectModel/Pages/activityReport";

const date = cy.helpers.dateYMD();
const hotelSlug = accounts.cypress_a.slug

TestFilters(['Notes'], () => {

describe('Staff Support: Notes are saved accurately in the activity report', () => {
    
    before('Log in as a Staff Support and create a single conference booking via events calendar', () => {
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)
        eventCalendarPage.open(hotelSlug)
                         .clickToday()
        conferenceCreationModal.clickNewGuest()
                    
        guestDetailsPage.fillInGuestBasicInfo(guestDetails)
                        .basketComponent.clickContinueButton()
                            
        payPage.skipPayment()
                            
        bookingHubPage.assertBookingRefIsVisible()
    })


    it('Verify that the activity report is viewable with the correct sections', () => {

        // Verify that guest, booking, conference layout, conference kitchen, conference terms and conference notes sections are displayed when present
        cy.get(bookingHubPage.selectors.bookingReference).invoke('text').then((text) => {
                const bookingRef = text.replace(/\s+/g, ' ').trim()
            //Enter guest note in guest profile 
            bookingHubPage.clickGuestAccountButton()
            cy.get(guestProfilePage.selectors.guestNoteField).type(`${specialCharacterData.guestNotes}`)
            guestProfilePage.assertNoteSavedAlert()
            bookingsPage.open(hotelSlug)
            bookingsPage.clickBookingRef(bookingRef)
        

       
       //Enter booking, conference layout, conference kitchen, conference terms and conference note in unified notes page
       cy.get(bookingHubPage.selectors.bookingNotesBox).click()
       cy.get(notesPage.selectors.bookingNoteField).type(specialCharacterData.bookingNotes)
       guestProfilePage.assertNoteSavedAlert()
       cy.get(notesPage.selectors.conferenceLayoutField).type(specialCharacterData.conferenceLayoutNotes)
       guestProfilePage.assertNoteSavedAlert()
       cy.get(notesPage.selectors.conferenceKitchenField).type(specialCharacterData.conferenceKitchenNotes)
       guestProfilePage.assertNoteSavedAlert()
       cy.get(notesPage.selectors.conferenceTermField).type(specialCharacterData.conferenceTermsNotes)
       guestProfilePage.assertNoteSavedAlert()
       cy.get(notesPage.selectors.conferenceNoteField).type(specialCharacterData.conferenceNotes)
       guestProfilePage.assertNoteSavedAlert()

       //Navigate to activity report and verify that booking notes section is below the guest notes section
       activityReportPage.open(hotelSlug, date)
                         .clickSearchButton()
       activityReportPage.assertGuestRowAboveBookingRow()

       //Verify that all notes are displayed in the correct sections
        cy.get(activityReportPage.selectors.guestNote).should('contain', `${specialCharacterData.guestNotes}`)
        cy.get(activityReportPage.selectors.bookingNote).should('contain', `${specialCharacterData.bookingNotes}`)
        activityReportPage.assertIsVisible('Layout')
        activityReportPage.assertIsVisible('Kitchen')
        activityReportPage.assertIsVisible('Terms')
        activityReportPage.assertIsVisible('Notes')           
        cy.get(activityReportPage.selectors.conferenceLayoutNote).should('contain', `${specialCharacterData.conferenceLayoutNotes}`)
        cy.get(activityReportPage.selectors.conferenceKitchenNotes).should('contain', `${specialCharacterData.conferenceKitchenNotes}`)
        cy.get(activityReportPage.selectors.conferenceTermNotes).should('contain', `${specialCharacterData.conferenceTermsNotes}`)
        cy.get(activityReportPage.selectors.conferenceNoteNotes).should('contain', `${specialCharacterData.conferenceNotes}`)
       
    //    //Verify that all notes are displayed in print preview 
    //    cy.get('.button[onclick="window.print();"]').click()
    //    cy.window().then(printWindow => {
    //     cy.wrap(printWindow.document.body).contains(`${specialCharacterData.guestNotes}`)
    //                                       .contains(`${specialCharacterData.bookingNotes}`)
    //                                       .contains(`${specialCharacterData.conferenceLayoutNotes}`)
    //                                       .contains(`${specialCharacterData.conferenceKitchenNotes}`)
    //                                       .contains(`${specialCharacterData.conferenceTermsNotes}`)
    //                                       .contains(`${specialCharacterData.conferenceNotes}`)
    //     })

       //Verify guest, booking, conference layout, conference kitchen, conference terms and conference notes sections are not displayed when not present

       //Clear guest note

       bookingHubPage.open(bookingRef, hotelSlug)
                     .clickGuestAccountButton()
       cy.get(guestProfilePage.selectors.guestNoteField).clear()
       guestProfilePage.assertNoteSavedAlert()

       //Clear booking, conference layout, kitchen, terms, and notes note

       bookingHubPage.open(bookingRef, hotelSlug)
       cy.get(bookingHubPage.selectors.bookingNotesBox).click()
       notesPage.clearAllNoteFields()
       guestProfilePage.assertNoteSavedAlert()

       //Navigate to activity report and verify that note sections are not displayed
       activityReportPage.open(hotelSlug, date)
                         .clickSearchButton()
       activityReportPage.assertIsNotVisible(activityReportPage.selectors.bookingNote)
       activityReportPage.assertIsNotVisible(activityReportPage.selectors.guestNote)
       activityReportPage.assertIsNotVisible(activityReportPage.selectors.conferenceNotes)
    })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})