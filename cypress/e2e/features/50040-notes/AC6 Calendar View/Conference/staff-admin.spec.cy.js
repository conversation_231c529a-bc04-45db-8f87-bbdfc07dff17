import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts";
import { eventCalendarPage } from "../../../../../support/pageObjectModel/Pages/eventCalendar";
import { conferenceCreationModal } from "../../../../../support/pageObjectModel/Components/conferenceCreationModal";
import { guestDetails } from "../../../../../fixtures/guestDetails";
import { guestDetailsPage } from "../../../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../../../support/pageObjectModel/Pages/pay";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { saveNoteAndVerifyEventCalendar } from "../../../../../support/helpers/noteCalendarDisplay";

const hotelSlug = accounts.cypress_a.slug
const date = cy.helpers.dateYMD();
const noteTypes = ['booking-notes', 'conference-layout', 'conference-kitchen', 'conference-terms', 'conference-notes'];
const noteLengths = [200, 199, 201];

TestFilters(['Notes'], () => {

describe('Staff Admin:  Notes are viewable on the events calendar to a limit of 200 characters', () => {
    
    before('Log in as a Staff Admin and create a single conference booking via events calendar', () => {
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

        eventCalendarPage.open(hotelSlug)
                         .clickToday()
        conferenceCreationModal.clickNewGuest()
                      
        guestDetailsPage.fillInGuestBasicInfo(guestDetails)
                        .basketComponent.clickContinueButton()
                              
        payPage.skipPayment()
                              
         bookingHubPage.assertBookingRefIsVisible()
        eventCalendarPage.open(hotelSlug)
        
    })

    it('Verifies that conference notes are displayed in the conference calendar at a maximum of 200 characters', () => {

        noteTypes.forEach(noteType => {
            noteLengths.forEach(length => {
            const testData = cy.create_string(length);
            const truncatedText = length === 201 ? testData.slice(0, -1) + "..." : testData;
        
                // Verify the correct note is saved and displayed in calendar
            saveNoteAndVerifyEventCalendar(noteType, truncatedText, hotelSlug, date);
            })
        })

    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

 })
})