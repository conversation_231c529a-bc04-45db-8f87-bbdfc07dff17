import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts"
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { bookingsPage } from "../../../../../support/pageObjectModel/Pages/bookings";
import { calendarPage } from "../../../../../support/pageObjectModel/Pages/calendar";
import { notesPage } from "../../../../../support/pageObjectModel/Pages/unifiedNotesPage";

const hotelSlug = accounts.cypress_a.slug
const date = cy.helpers.dateYMD();
const bookingName = 'Cypress Guest A'
let testData

TestFilters(['Notes'], () => {

describe('Group Manager : Notes are viewable on the reservations calendar to a limit of 200 characters', () => {

  before('resets test seed', () => {
      cy.clearCookies();
      cy.visit('/automation/tests/reseed')
      cy.fn_login('hotelier', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
  })
    beforeEach('Can log in and view booking hub page', () => {
      bookingsPage.open(hotelSlug)
      bookingsPage.clickFirstExistingBooking()
      cy.get(bookingHubPage.selectors.bookingNotesBox).click()
      notesPage.clearAllNoteFields()
    })

    it('Verifies that booking note of 200 characters is displayed in the calendar in full', () => {
      //Verifies that booking note of 200 characters is displayed in full on the calendar
      testData = cy.create_string(200)

      //Save 200 character note in BOOKING notes field
      cy.get(notesPage.selectors.bookingNoteField).type(testData)
      notesPage.assertNoteSavedAlert()

      //Navigates to calendar and verifies that the correct note is displayed in the booking hover in full
      calendarPage.open(hotelSlug, date)
      calendarPage.calendarEntryHover(bookingName)
      calendarPage.assertBookingInfoCardText(testData)
    })


      it('Verifies that booking note of 199 characters is displayed in full on the calendar', () => {
      testData = cy.create_string(199)

      //Save 200 character note in BOOKING notes field
      cy.get(notesPage.selectors.bookingNoteField).type(testData)
      notesPage.assertNoteSavedAlert()

      //Navigates to calendar and verifies that the correct note is displayed in the booking hover in full
      calendarPage.open(hotelSlug, date)
      calendarPage.calendarEntryHover(bookingName)
      calendarPage.assertBookingInfoCardText(testData)
      })

      
    it('Verifies that booking note of over 200 characters is truncated to 200 characters on the calendar with an ellipses at the end', () => {
      testData = cy.create_string(201)
        let truncatedText = testData.slice(0, -1) + "..."

      //Save 201 character note in BOOKING notes field
      cy.get(notesPage.selectors.bookingNoteField).type(testData)
      notesPage.assertNoteSavedAlert()

      //Navigates to calendar and verifies that the correct note is truncated to 200 characters on the calendar with an ellipses at the end
      calendarPage.open(hotelSlug, date)
      calendarPage.calendarEntryHover(bookingName)
      calendarPage.assertBookingInfoCardText(truncatedText)
      })


      it('Verifies that channel note of 200 characters is displayed in the calendar in full', () => {
        //Verifies that channel note of 200 characters is displayed in full on the calendar
        testData = cy.create_string(200)
  
        //Save 200 character note in CHANNEL notes field
        cy.get(notesPage.selectors.channelNoteField).type(testData)
        notesPage.assertNoteSavedAlert()
  
        //Navigates to calendar and verifies that the correct note is displayed in the booking hover in full
        calendarPage.open(hotelSlug, date)
        calendarPage.calendarEntryHover(bookingName)
        calendarPage.assertBookingInfoCardText(testData)
      })
  
  
        it('Verifies that channel note of 199 characters is displayed in full on the calendar', () => {
        testData = cy.create_string(199)
  
        //Save 200 character note in CHANNEL notes field
        cy.get(notesPage.selectors.channelNoteField).type(testData)
        notesPage.assertNoteSavedAlert()
  
        //Navigates to calendar and verifies that the correct note is displayed in the booking hover in full
        calendarPage.open(hotelSlug, date)
        calendarPage.calendarEntryHover(bookingName)
        calendarPage.assertBookingInfoCardText(testData)
        })
  
        
        it('Verifies that channel note of over 200 characters is truncated to 200 characters on the calendar with an ellipses at the end', () => {
        testData = cy.create_string(201)
          let truncatedText = testData.slice(0, -1) + "..."
  
        //Save 201 character note in CHANNEL notes field
        cy.get(notesPage.selectors.channelNoteField).type(testData)
        notesPage.assertNoteSavedAlert()
  
        //Navigates to calendar and verifies that the correct note is truncated to 200 characters on the calendar with an ellipses at the end
        calendarPage.open(hotelSlug, date)
        calendarPage.calendarEntryHover(bookingName)
        calendarPage.assertBookingInfoCardText(truncatedText)
        })


        it('Verifies that housekeeping note of 200 characters is displayed in the calendar in full', () => {
          testData = cy.create_string(200)
    
          //Save 200 character note in HOUSEKEEPING notes field
          cy.get(notesPage.selectors.housekeepingNoteField).type(testData)
          notesPage.assertNoteSavedAlert()
    
          //Navigates to calendar and verifies that the correct note is displayed in the booking hover in full
          calendarPage.open(hotelSlug, date)
          calendarPage.calendarEntryHover(bookingName)
          calendarPage.assertBookingInfoCardText(testData)
        })
    
    
          it('Verifies that housekeeping note of 199 characters is displayed in full on the calendar', () => {
          testData = cy.create_string(199)
    
          //Save 200 character note in HOUSEKEEPING notes field
          cy.get(notesPage.selectors.housekeepingNoteField).type(testData)
          notesPage.assertNoteSavedAlert()
    
          //Navigates to calendar and verifies that the correct note is displayed in the booking hover in full
          calendarPage.open(hotelSlug, date)
          calendarPage.calendarEntryHover(bookingName)
          calendarPage.assertBookingInfoCardText(testData)
          })
    
          
        it('Verifies that housekeeping note of over 200 characters is truncated to 200 characters on the calendar with an ellipses at the end', () => {
          testData = cy.create_string(201)
            let truncatedText = testData.slice(0, -1) + "..."
    
          //Save 201 character note in HOUSEKEEPING notes field
          cy.get(notesPage.selectors.housekeepingNoteField).type(testData)
          notesPage.assertNoteSavedAlert()
    
          //Navigates to calendar and verifies that the correct note is truncated to 200 characters on the calendar with an ellipses at the end
          calendarPage.open(hotelSlug, date)
          calendarPage.calendarEntryHover(bookingName)
          calendarPage.assertBookingInfoCardText(truncatedText)
          })

          afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
          })
})
})   