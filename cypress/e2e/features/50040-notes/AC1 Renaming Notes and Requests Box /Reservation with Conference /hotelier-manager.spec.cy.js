import accounts from "../../../../../fixtures/accounts"
import { conferenceCreationModal } from "../../../../../support/pageObjectModel/Components/conferenceCreationModal";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { bookingsPage } from "../../../../../support/pageObjectModel/Pages/bookings"
import { eventCalendarPage } from "../../../../../support/pageObjectModel/Pages/eventCalendar";
import { notesPage } from "../../../../../support/pageObjectModel/Pages/unifiedNotesPage";

const hotelSlug = accounts.cypress_a.slug
const noteBoxSelectors = [bookingHubPage.selectors.bookingNotesBox, bookingHubPage.selectors.channelNotesBox, bookingHubPage.selectors.housekeepingNotesBox, bookingHubPage.selectors.eventNotesBox]

TestFilters(['Notes'], () => {

describe('Hotelier Manager: Notes and Requests Box is renamed to Notes and Attachments', () => {
    
    before('Can log in and view booking hub page', () => { 
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')

        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        bookingsPage.open(hotelSlug)
        cy.get(bookingsPage.selectors.firstExistingBookingRef).invoke('text').then(text => {
            const bookingRef = text;
        eventCalendarPage.open(hotelSlug)   
                         .clickToday()
        conferenceCreationModal.clickExistingBooking()
        cy.get(conferenceCreationModal.selectors.bookingRefField).should('be.visible')
        conferenceCreationModal.fillBookingRef(bookingRef)
                               .clickConfirmButton()
    })
})    

    it('Verify that Notes and Attachments box has been removed', () => {
        bookingsPage.open(hotelSlug)
                    .clickFirstExistingBooking()
        bookingHubPage.assertIsNotVisible('Notes & Attachments')
        
    
        cy.get(bookingHubPage.selectors.bookingNotesBox).should('be.visible').within(() => {
            cy.get(bookingHubPage.selectors.bookingNoteIcon).should('be.visible')
            bookingHubPage.assertIsVisible('Add Booking notes')
            bookingHubPage.assertIsNotVisible('Add attachments')
            cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist')
        })
        
        cy.get(bookingHubPage.selectors.channelNotesBox).should('be.visible').within(() => {
            cy.get(bookingHubPage.selectors.channelNoteIcon).should('be.visible')
            bookingHubPage.assertIsVisible('Add Channel notes')
            bookingHubPage.assertIsNotVisible('Add attachments')
            cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist')
        })
        
        cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('be.visible').within(() => {
            cy.get(bookingHubPage.selectors.housekeepingNoteIcon).should('be.visible')
            bookingHubPage.assertIsVisible('Add Housekeeping notes')
            bookingHubPage.assertIsNotVisible('Add attachments')
            cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist')
        })

        cy.get(bookingHubPage.selectors.eventNotesBox).should('be.visible').within(() => {
            cy.get(bookingHubPage.selectors.eventNoteIcon).should('be.visible')
            bookingHubPage.assertIsVisible('Add Event notes')
            bookingHubPage.assertIsNotVisible('Add attachments')
            cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist')
        })



    //Clicking Booking, Channel, Housekeeping or Events notes box redirects user to the unified notes page and booking ref is displayed
        noteBoxSelectors.forEach(noteBoxSelector => {
            bookingsPage.open(hotelSlug)
            cy.get(bookingsPage.selectors.firstExistingBookingRef).invoke('text').then(text => {
            const bookingRef = text;
            bookingsPage.clickFirstExistingBooking()
            cy.get(noteBoxSelector).click()
            notesPage.assertURL(hotelSlug, bookingRef)
            cy.get(notesPage.selectors.bookingReferenceHeader).should((text) => {
                expect(text.text()).to.contain(bookingRef)
            })
            })   
        })
    

//     //Clicking on booking reference banner redirects user to booking hub page
        bookingsPage.open(hotelSlug)
            cy.get(bookingsPage.selectors.firstExistingBookingRef).invoke('text').then(text => {
            const bookingRef = text;
            bookingsPage.clickFirstExistingBooking()
            cy.get(bookingHubPage.selectors.bookingNotesBox).click()
            notesPage.clickBookingRef(bookingRef)
            cy.url().should('contain', `/hotels/${hotelSlug}/bookings/${bookingRef}`)
        
        })
})
})

})