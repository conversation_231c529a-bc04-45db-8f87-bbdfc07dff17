import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts";
import { conferenceCreationModal } from "../../../../../support/pageObjectModel/Components/conferenceCreationModal";
import { eventCalendarPage } from "../../../../../support/pageObjectModel/Pages/eventCalendar";
import { guestDetailsPage } from "../../../../../support/pageObjectModel/Pages/guestDetails";
import { guestDetails } from "../../../../../fixtures/guestDetails";
import { payPage } from "../../../../../support/pageObjectModel/Pages/pay";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { notesPage } from "../../../../../support/pageObjectModel/Pages/unifiedNotesPage";
const hotelSlug = accounts.cypress_a.slug

TestFilters(['Notes'], () => {

describe('Staff Support: Notes and Requests Box is renamed to Notes and Attachments', () => {
    
    before('Log in as a Staff Support and create a single conference booking via events calendar', () => {
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)

        eventCalendarPage.open(hotelSlug)
                        .clickToday()
        conferenceCreationModal.clickNewGuest()

        guestDetailsPage
                    .fillInGuestBasicInfo(guestDetails)
                    .basketComponent.clickContinueButton()
        
        payPage.skipPayment()
        
        bookingHubPage.assertBookingRefIsVisible()
    })

    
    it('Verify that Notes and Attachments box has been removed', () => {
        bookingHubPage.assertIsNotVisible('Notes & Attachments')

        cy.get(bookingHubPage.selectors.bookingNotesBox).should('be.visible').within(() => {
            cy.get(bookingHubPage.selectors.bookingNoteIcon).should('be.visible')
            bookingHubPage.assertIsVisible('Add Booking notes')
            bookingHubPage.assertIsNotVisible('Add attachments')
            cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist')
        })
                                                    
        cy.get(bookingHubPage.selectors.eventNotesBox).should('be.visible').within(() => {
            cy.get(bookingHubPage.selectors.eventNoteIcon).should('be.visible')
            bookingHubPage.assertIsVisible('Add Event notes')
            bookingHubPage.assertIsNotVisible('Add attachments')
            cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist')
        })

        cy.get(bookingHubPage.selectors.channelNotesBox).should('not.exist')
        cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('not.exist')

    
        
        //Clicking 'Booking notes' box redirects user to the unified notes page and booking ref is displayed
        cy.get(bookingHubPage.selectors.bookingReference).invoke('text').then((text) => {
        const bookingRef = text.replace(/\s+/g, ' ').trim()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click();
        notesPage.assertURL(hotelSlug, bookingRef)
        cy.get(notesPage.selectors.bookingReferenceHeader).should('contain', bookingRef)
        })


        //Clicking 'Event Notes' box redirects user to the unified notes page and booking ref is displayed 
        notesPage.clickBackToBookingSummary()
        cy.get(bookingHubPage.selectors.bookingReference).invoke('text').then((text) => {
        const bookingRef = text.replace(/\s+/g, ' ').trim()
        cy.get(bookingHubPage.selectors.eventNotesBox).click();
        notesPage.assertURL(hotelSlug, bookingRef)
        cy.get(notesPage.selectors.bookingReferenceHeader).should('contain', bookingRef)

        //Verify clicking booking ref redirects user to the booking hub page 
        notesPage.clickBookingRef(bookingRef)
        cy.url().should('contain', `/hotels/${hotelSlug}/bookings/${bookingRef}`)
        })
       
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

    })

})