import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { bookingsPage } from "../../../../../support/pageObjectModel/Pages/bookings";
import { notesPage } from "../../../../../support/pageObjectModel/Pages/unifiedNotesPage";
const hotelSlug = accounts.cypress_a.slug

TestFilters(['Notes'], () => {

describe('Group Manager: Notes and Requests Box is renamed to Notes and Attachments', () => {
    
    before('reset test seed', () => { 
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('hotelier', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
    })
    
    beforeEach('Can log in and view booking hub page', () => { 
        bookingsPage.open(hotelSlug)
    })

    it('Verify that Notes and Attachments box has been removed', () => {
        bookingsPage.clickFirstExistingBooking()
        bookingHubPage.assertIsNotVisible('Notes & Attachments')
        
    
        cy.get(bookingHubPage.selectors.bookingNotesBox).should('be.visible').within(() => {
            cy.get(bookingHubPage.selectors.bookingNoteIcon).should('be.visible')
            bookingHubPage.assertIsVisible('Add Booking notes')
            bookingHubPage.assertIsNotVisible('Add attachments')
            cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist')
        })
        
        cy.get(bookingHubPage.selectors.channelNotesBox).should('be.visible').within(() => {
            cy.get(bookingHubPage.selectors.channelNoteIcon).should('be.visible')
            bookingHubPage.assertIsVisible('Add Channel notes')
            bookingHubPage.assertIsNotVisible('Add attachments')
            cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist')
        })
        
        cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('be.visible').within(() => {
            cy.get(bookingHubPage.selectors.housekeepingNoteIcon).should('be.visible')
            bookingHubPage.assertIsVisible('Add Housekeeping notes')
            bookingHubPage.assertIsNotVisible('Add attachments')
            cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist')
        })
        
        cy.get(bookingHubPage.selectors.eventNotesBox).should('not.exist')
    })

    it('Clicking Booking notes box redirects user to the unified notes page and booking ref is displayed', () => {
        cy.get(bookingsPage.selectors.firstExistingBookingRef).invoke('text').then(text => {
        const bookingRef = text;
        bookingsPage.clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.assertURL(hotelSlug, bookingRef)
        cy.get(notesPage.selectors.bookingReferenceHeader).should('contain', bookingRef)
        })   
    })

    it('Clicking Channel notes box redirects user to the unified notes page and booking ref is displayed', () => {
        cy.get(bookingsPage.selectors.firstExistingBookingRef).invoke('text').then(text => {
        const bookingRef = text;
        bookingsPage.clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.channelNotesBox).click()
        notesPage.assertURL(hotelSlug, bookingRef)
        cy.get(notesPage.selectors.bookingReferenceHeader).should('contain', bookingRef)
        })   
    })

    it('Clicking Housekeeping notes box redirects user to the unified notes page and booking ref is displayed', () => {
        cy.get(bookingsPage.selectors.firstExistingBookingRef).invoke('text').then(text => {
        const bookingRef = text;
        bookingsPage.clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.housekeepingNotesBox).click()
        notesPage.assertURL(hotelSlug, bookingRef)
        cy.get(notesPage.selectors.bookingReferenceHeader).should('contain', bookingRef)
        })   
    })

    it('Clicking on booking reference banner redirects user to booking hub page', () => {
        cy.get(bookingsPage.selectors.firstExistingBookingRef).invoke('text').then(text => {
        const bookingRef = text;
        bookingsPage.clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.clickBookingRef(bookingRef)
        cy.url().should('contain', `/hotels/${hotelSlug}/bookings/${bookingRef}`)
        })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})