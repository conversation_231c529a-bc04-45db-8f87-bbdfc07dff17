import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts";
import { header } from "../../../../../support/pageObjectModel/Components/header";
import { guestProfilePage } from "../../../../../support/pageObjectModel/Pages/guestProfile";
import { hotelierLogin } from "../../../../../support/pageObjectModel/Pages/hotelierLoginPage";
import { notesPage } from "../../../../../support/pageObjectModel/Pages/unifiedNotesPage";
import { yourStayPage } from "../../../../../support/pageObjectModel/Pages/yourStay";

const hotelSlug = accounts.cypress_a.slug

TestFilters(['Notes'], () => {

describe('Guests cannot access the unified notes page', () => {
    
    before('reset test seed', () => {
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
        yourStayPage.assertHeaderTitle()
    })
        
    it('Can not access notes page directly', () => {
        header.clickLoginButton()
        header.clickMyAccountButton()
        cy.get(guestProfilePage.selectors.bookingDetails).first().invoke('text').then((text) => {
            const bookingRef = text.replace(/\s+/g, ' ').trim()
        notesPage.open(hotelSlug, bookingRef)
        hotelierLogin.assertHeaderTitle()
        hotelierLogin.assertUrl()
        })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})    