import TestFilters from "../../../../../../support/filterTests"
import accounts from "../../../../../../fixtures/accounts"
import { bookingsPage } from "../../../../../../support/pageObjectModel/Pages/bookings"
import { bookingHubPage } from "../../../../../../support/pageObjectModel/Pages/bookingHub"
import { notesPage } from "../../../../../../support/pageObjectModel/Pages/unifiedNotesPage"

const hotelSlug = accounts.cypress_a.slug
const fileTypes = [
  '10_MB.jpg', '3.8-MB.png', 'pdf.pdf', 'XLS.xls', 'XLSX.xlsx',
  'DOC.doc', 'DOCX.docx', 'ODT.odt', 'ODS.ods'
];

Cypress.config('redirectionLimit', 100000)

TestFilters(['Notes'], () => {

describe('Staff Admin: Notes page is accessible by hyperlink/URL', () => {

    before('visits unified notes page', () => {
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password')) 
        bookingsPage.open(hotelSlug)
                    .clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
    })    
    
    it('Verify attachment functionality of the unified notes page', () => {

        notesPage.addAttachment('3.8-MB.png', 0)
        notesPage.addAttachment('3.8-MB.png', 1)
        notesPage.addAttachment('3.8-MB.png', 2)

        notesPage.assertNumberOfAttachments(3)

        // //Navigate to booking hub and verify attachment count and paperclip appearance
        notesPage.clickBackToBookingSummary()

        bookingHubPage.assertNoteAttachment(bookingHubPage.selectors.bookingNotesBox, 1)
        bookingHubPage.assertNoteAttachment(bookingHubPage.selectors.channelNotesBox, 1)
        bookingHubPage.assertNoteAttachment(bookingHubPage.selectors.housekeepingNotesBox, 1)

        //Verify that attachments can be deleted and that attachment function returns to default 'Add attachment' after attachment is deleted
        //-delete booking attachment

        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.deleteAttachment('3.8-MB.png', 0)
        notesPage.clickBackToBookingSummary()
        cy.get(bookingHubPage.selectors.bookingNotesBox).within(() => {
          cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist');
          bookingHubPage.assertIsNotVisible('(1)')
        })

        //-delete channel attachment
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.deleteAttachment('3.8-MB.png', 1)
        notesPage.clickBackToBookingSummary()
        cy.get(bookingHubPage.selectors.channelNotesBox).within(() => {
          cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist');
          bookingHubPage.assertIsNotVisible('(1)')
        })

        //-delete housekeeping attachment
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.deleteAttachment('3.8-MB.png', 3)
        notesPage.clickBackToBookingSummary()
        cy.get(bookingHubPage.selectors.housekeepingNotesBox).within(() => {
          cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist');
          bookingHubPage.assertIsNotVisible('(1)')
        })

        cy.get(bookingHubPage.selectors.bookingNotesBox).click()

        //Verify that all compatible file types can be added by every note attachment function for a conference booking
        fileTypes.forEach(file => {
            for (let i = 0; i <= 2; i++) {
            notesPage.addAttachment(file, i);
            }
        });
        

        //Refresh the page
        cy.reload();

        // //Cannot add image over 5MB to BOOKING attachment function
        cy.get(notesPage.selectors.addAttachmentButton).eq(0).selectFile('tests/cypress/fixtures/test files/5.47-MB.png')
        cy.on("window:alert", (x)=> {
          expect(x).to.contains('File size 5.47MB exceeds maximum file size limit 5MB')
        })
        
        //Cannot add image over 5MB to CONFERENCE LAYOUT attachment function
        cy.get(notesPage.selectors.addAttachmentButton).eq(1).selectFile('tests/cypress/fixtures/test files/5.47-MB.png')
        cy.on("window:alert", (x)=> {
          expect(x).to.contains('File size 5.47MB exceeds maximum file size limit 5MB')
        })

        //Cannot add image over 5MB to CONFERENCE KICTHEN attachment function
        cy.get(notesPage.selectors.addAttachmentButton).eq(2).selectFile('tests/cypress/fixtures/test files/5.47-MB.png')
        cy.on("window:alert", (x)=> {
          expect(x).to.contains('File size 5.47MB exceeds maximum file size limit 5MB')
        })
        
        
        //Can preview booking attachment 
        notesPage.assertAttachmentPreview('10_MB.jpg', 0, '10_MB' )
        //Can preview conference layout attachment
        notesPage.assertAttachmentPreview('3.8-MB.png', 9, '3.8-MB' )
        //Can preview conference kitchen attachment
        notesPage.assertAttachmentPreview('10_MB.jpg', 17, '10_MB' )

        
        //Can download attachments -- todo
        // booking
        // notesPage.downloadAttachment('10_MB.jpg', 0)
        // channel
        // notesPage.downloadAttachment('3.8-MB.png', 10)
        // housekeeping
        // notesPage.downloadAttachment('pdf.pdf', 20)

        })

        afterEach(function () {
          cy.fn_afterEachJira(this.currentTest)
      })
})

})   

