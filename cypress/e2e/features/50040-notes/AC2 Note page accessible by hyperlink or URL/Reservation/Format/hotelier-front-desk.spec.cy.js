import TestFilters from "../../../../../../support/filterTests"
import accounts from "../../../../../../fixtures/accounts"
import { bookingHubPage } from "../../../../../../support/pageObjectModel/Pages/bookingHub"
import { bookingsPage } from "../../../../../../support/pageObjectModel/Pages/bookings"
import { notesPage } from "../../../../../../support/pageObjectModel/Pages/unifiedNotesPage"
import { notesDateConverter } from "../../../../../../support/functions/notesFormatDate"

const hotelSlug = accounts.cypress_a.slug
const date = notesDateConverter(cy.helpers.dateYMD())

TestFilters(['Notes'], () => {

describe('Hotelier Front Desk: Notes page is accessible by hyperlink/URL', () => {

    before('reset test seed', () => {
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
    })
    
    beforeEach('Unified Notes Page is accessible', () => {
        bookingsPage.open(hotelSlug)
                    .clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
    })

    it('Verify format of the unified notes page for a single reservation booking', () => {
        
        notesPage.assertBookingRefHeader()
        cy.get(notesPage.selectors.notebBanner).contains('Notes').should('be.visible').within(() => {
            cy.get(notesPage.selectors.notesBannerIcon).should('be.visible')
        })
        cy.get(notesPage.selectors.attachmentBanner).contains('Attachments').should('be.visible').within(() => {
            cy.get(notesPage.selectors.attachmentBannerIcon).should('be.visible')
        })

        notesPage.assertBookingNoteSection()
        notesPage.assertChannelNoteSection()
        notesPage.assertHousekkepingNoteSection()

        // Check attachment buttons for each section
        //Booking add attachment function is displayed 
        cy.get(notesPage.selectors.addAttachmentButton).eq(0).should('be.visible')
        cy.get(notesPage.selectors.addAttachmentText).eq(0).should('contain', 'Add attachment')

        //Channel add attachment function is displayed
        cy.get(notesPage.selectors.addAttachmentButton).eq(1).should('be.visible')
        cy.get(notesPage.selectors.addAttachmentText).eq(1).should('contain', 'Add attachment')

        //Housekeeping add attachment function is displayed
        cy.get(notesPage.selectors.addAttachmentButton).eq(2).should('be.visible')
        cy.get(notesPage.selectors.addAttachmentText).eq(3).should('contain', 'Add attachment')

    })

    it('Verifies housekeeping notes dynamic heading', () => {
        cy.get(notesPage.selectors.housekeepingDyamicText).should('contain', '1 night')
                                                       .should('contain', 'Single') 
                                                       .should('contain', 'Rm_1')
                                                       .should('contain', date)
       
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})
