import TestFilters from "../../../../../../support/filterTests"
import accounts from "../../../../../../fixtures/accounts"
import { guestDetails } from "../../../../../../fixtures/guestDetails"
import { guestDetailsPage } from "../../../../../../support/pageObjectModel/Pages/guestDetails"
import { conferenceCreationModal } from "../../../../../../support/pageObjectModel/Components/conferenceCreationModal"
import { payPage } from "../../../../../../support/pageObjectModel/Pages/pay"
import { eventCalendarPage } from "../../../../../../support/pageObjectModel/Pages/eventCalendar"
import { bookingHubPage } from "../../../../../../support/pageObjectModel/Pages/bookingHub"
import { notesPage } from "../../../../../../support/pageObjectModel/Pages/unifiedNotesPage"
import { updateConferencePage } from "../../../../../../support/pageObjectModel/Pages/updateConferencePage"

const hotelSlug = accounts.cypress_a.slug

describe('Staff Support: Notes page is accessible by hyperlink/URL', () => {
    
    before('visits unified notes page', () => { 
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)
        eventCalendarPage.open(hotelSlug)
                         .clickToday()
        conferenceCreationModal.clickNewGuest()
        
        guestDetailsPage.fillInGuestBasicInfo(guestDetails)
                        .basketComponent.clickContinueButton()
                
        payPage.skipPayment()
                
        bookingHubPage.assertBookingRefIsVisible()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
    })

    it('Verify format of the unified notes page for a single conference booking', () => {
        //Top of the page displays booking reference
        notesPage.assertBookingRefHeader()
        cy.get(notesPage.selectors.notebBanner).contains('Notes').should('be.visible').within(() => {
            cy.get(notesPage.selectors.notesBannerIcon).should('be.visible')
        })
        cy.get(notesPage.selectors.attachmentBanner).contains('Attachments').should('be.visible').within(() => {
            cy.get(notesPage.selectors.attachmentBannerIcon).should('be.visible')
        })
     
        notesPage.assertBookingNoteSection()
        notesPage.assertConferenceSection()

        // Check attachment buttons for each section
        //Booking add attachment function is displayed 
        cy.get(notesPage.selectors.addAttachmentButton).eq(0).should('be.visible')
        cy.get(notesPage.selectors.addAttachmentText).eq(0).should('contain', 'Add attachment')

        //Conference layout add attachment function is displayed
        cy.get(notesPage.selectors.addAttachmentButton).eq(1).should('be.visible')
        cy.get(notesPage.selectors.addAttachmentText).eq(2).should('contain', 'Add attachment')

        //Conference kitchen add attachment function is displayed
        cy.get(notesPage.selectors.addAttachmentButton).eq(2).should('be.visible')
        cy.get(notesPage.selectors.addAttachmentText).eq(3).should('contain', 'Add attachment')

        //Conference terms add attachment function is displayed
        cy.get(notesPage.selectors.addAttachmentButton).eq(3).should('be.visible')
        cy.get(notesPage.selectors.addAttachmentText).eq(4).should('contain', 'Add attachment')

        //Conference terms add attachment function is displayed
        cy.get(notesPage.selectors.addAttachmentButton).eq(4).should('be.visible')
        cy.get(notesPage.selectors.addAttachmentText).eq(5).should('contain', 'Add attachment')

        //Housekeeping and channel notes should not be displayed 
        cy.get(notesPage.selectors.housekeepingNoteField).should('not.exist');
        cy.get(notesPage.selectors.channelNoteField).should('not.exist');

        //Verify that note fields on update conference page are empty 
        notesPage.clickBackToBookingSummary()
        bookingHubPage.clickConferenceEditPencil()
        
        cy.get(updateConferencePage.selectors.conferenceLayoutField).should('have.value', '')
        cy.get(updateConferencePage.selectors.conferenceKitchenField).should('have.value', '')
        cy.get(updateConferencePage.selectors.conferenceTermsField).should('have.value', '')
        cy.get(updateConferencePage.selectors.conferenceNotesField).should('have.value', '')  
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})