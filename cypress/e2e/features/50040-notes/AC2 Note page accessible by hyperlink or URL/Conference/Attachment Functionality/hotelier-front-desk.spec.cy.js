import TestFilters from "../../../../../../support/filterTests"
import accounts from "../../../../../../fixtures/accounts"
import { eventCalendarPage } from "../../../../../../support/pageObjectModel/Pages/eventCalendar"
import { conferenceCreationModal } from "../../../../../../support/pageObjectModel/Components/conferenceCreationModal"
import { guestDetailsPage } from "../../../../../../support/pageObjectModel/Pages/guestDetails"
import { guestDetails } from "../../../../../../fixtures/guestDetails"
import { payPage } from "../../../../../../support/pageObjectModel/Pages/pay"
import { bookingHubPage } from "../../../../../../support/pageObjectModel/Pages/bookingHub"
import { notesPage } from "../../../../../../support/pageObjectModel/Pages/unifiedNotesPage"

const hotelSlug = accounts.cypress_a.slug
const fileTypes = [
    '10_MB.jpg', '3.8-MB.png', 'pdf.pdf', 'XLS.xls', 'XLSX.xlsx',
    'DOC.doc', 'DOCX.docx', 'ODT.odt', 'ODS.ods'
];

Cypress.config('redirectionLimit', 100000)


describe('Hotelier Front Desk : Notes page is accessible by hyperlink/URL', () => {
  
    before('visits unified notes page', () => {
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        eventCalendarPage.open(hotelSlug)
                                .clickToday()
        conferenceCreationModal.clickNewGuest()
               
        guestDetailsPage.fillInGuestBasicInfo(guestDetails)
                        .basketComponent.clickContinueButton()
                       
        payPage.skipPayment()
                       
        bookingHubPage.assertBookingRefIsVisible()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
    })
    
    it('Verify attachment functionality of the unified notes page', () => {

        notesPage.addAttachment('3.8-MB.png', 0)
        notesPage.addAttachment('3.8-MB.png', 1)
        notesPage.addAttachment('3.8-MB.png', 2)
        notesPage.addAttachment('3.8-MB.png', 3)
        notesPage.addAttachment('3.8-MB.png', 4)

        notesPage.assertNumberOfAttachments(5)

        //Navigate to booking hub and verify attachment count and paperclip appearance
        notesPage.clickBackToBookingSummary()
        bookingHubPage.assertNoteAttachment(bookingHubPage.selectors.bookingNotesBox, 1)
        bookingHubPage.assertNoteAttachment(bookingHubPage.selectors.eventNotesBox, 4)
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()


        //Verify that attachments can be deleted and that attachment function returns to default 'Add attachment' after attachment is deleted
        //-delete booking attachment
        notesPage.deleteAttachment('3.8-MB.png', 0)
        notesPage.clickBackToBookingSummary()
        cy.get(bookingHubPage.selectors.bookingNotesBox).within(() => {
          cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist');
          bookingHubPage.assertIsNotVisible('(1)')
        })

        //-delete conference layout attachment
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.deleteAttachment('3.8-MB.png', 2)
        notesPage.clickBackToBookingSummary()
        bookingHubPage.assertNoteAttachment(bookingHubPage.selectors.eventNotesBox, 3)

        //-delete conference kitchen attachment
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.deleteAttachment('3.8-MB.png', 3)
        notesPage.clickBackToBookingSummary()
        bookingHubPage.assertNoteAttachment(bookingHubPage.selectors.eventNotesBox, 2)

        //-delete conference terms attachment
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.deleteAttachment('3.8-MB.png', 4)
        notesPage.clickBackToBookingSummary()
        bookingHubPage.assertNoteAttachment(bookingHubPage.selectors.eventNotesBox, 1)

        //-delete conference notes attachment
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        notesPage.deleteAttachment('3.8-MB.png', 5)
        notesPage.clickBackToBookingSummary()
        cy.get(bookingHubPage.selectors.eventNotesBox).within(() => {
          cy.get(bookingHubPage.selectors.noteAttachmentIcon).should('not.exist');
          bookingHubPage.assertIsNotVisible('(1)')
        })
        
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()

        //Verify that all compatible file types can be added by every note attachment function for a conference booking
        fileTypes.forEach(file => {
            for (let i = 0; i <= 4; i++) {
            notesPage.addAttachment(file, i);
            }
        });

        //Refresh the page
        cy.reload();

        // //Cannot add image over 5MB to BOOKING attachment function
        cy.get(notesPage.selectors.addAttachmentButton).eq(0).selectFile('tests/cypress/fixtures/test files/5.47-MB.png')
        cy.on("window:alert", (x)=> {
          expect(x).to.contains('File size 5.47MB exceeds maximum file size limit 5MB')
        })
        
        //Cannot add image over 5MB to CONFERENCE LAYOUT attachment function
        cy.get(notesPage.selectors.addAttachmentButton).eq(1).selectFile('tests/cypress/fixtures/test files/5.47-MB.png')
        cy.on("window:alert", (x)=> {
          expect(x).to.contains('File size 5.47MB exceeds maximum file size limit 5MB')
        })

        //Cannot add image over 5MB to CONFERENCE KICTHEN attachment function
        cy.get(notesPage.selectors.addAttachmentButton).eq(2).selectFile('tests/cypress/fixtures/test files/5.47-MB.png')
        cy.on("window:alert", (x)=> {
          expect(x).to.contains('File size 5.47MB exceeds maximum file size limit 5MB')
        })
        
        //Cannot add image over 5MB to CONFERENCE TERMS attachment function
        cy.get(notesPage.selectors.addAttachmentButton).eq(3).selectFile('tests/cypress/fixtures/test files/5.47-MB.png')
        cy.on("window:alert", (x)=> {
          expect(x).to.contains('File size 5.47MB exceeds maximum file size limit 5MB')
        })
        
        //Cannot add image over 5MB to CONFERENCE NOTES attachment function
        cy.get(notesPage.selectors.addAttachmentButton).eq(4).selectFile('tests/cypress/fixtures/test files/5.47-MB.png')
        cy.on("window:alert", (x)=> {
          expect(x).to.contains('File size 5.47MB exceeds maximum file size limit 5MB')
    
        })

        
        //Can preview booking attachment 
        notesPage.assertAttachmentPreview('10_MB.jpg', 0, '10_MB' )
        //Can preview conference layout attachment
        notesPage.assertAttachmentPreview('3.8-MB.png', 9, '3.8-MB' )
        //Can preview conference kitchen attachment
        notesPage.assertAttachmentPreview('10_MB.jpg', 17, '10_MB' )
        //Can preview conference terms attachment
        notesPage.assertAttachmentPreview('3.8-MB.png', 27, '3.8-MB' )
        //Can preview conference notes attachment 
        notesPage.assertAttachmentPreview('10_MB.jpg', 35, '10_MB' )

    

        //Can download attachments -- todo
        //booking
        // notesPage.downloadAttachment('10_MB.jpg', 0)
        //conference layout
        // notesPage.downloadAttachment('3.8-MB.png', 9)
        // //conference kitchen
        // notesPage.downloadAttachment('pdf.pdf', 19)
        // //conference terms
        // notesPage.downloadAttachment('XLS.xls', 29)
        // //conference notes
        // notesPage.downloadAttachment('DOCX.docx', 41)

        
        })

        afterEach(function () {
          cy.fn_afterEachJira(this.currentTest)
      })
})

    

