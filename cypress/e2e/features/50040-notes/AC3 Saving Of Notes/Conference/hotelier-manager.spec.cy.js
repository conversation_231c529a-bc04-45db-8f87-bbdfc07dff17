import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts";
import { specialCharacterData } from "../../../../../fixtures/cypress_a/notes";
import { eventCalendarPage } from "../../../../../support/pageObjectModel/Pages/eventCalendar";
import { conferenceCreationModal } from "../../../../../support/pageObjectModel/Components/conferenceCreationModal";
import { guestDetails } from "../../../../../fixtures/guestDetails";
import { guestDetailsPage } from "../../../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../../../support/pageObjectModel/Pages/pay";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { notesPage } from "../../../../../support/pageObjectModel/Pages/unifiedNotesPage";
import { updateConferencePage } from "../../../../../support/pageObjectModel/Pages/updateConferencePage";

const date = cy.helpers.dateYMD();
const hotelSlug = accounts.cypress_a.slug

TestFilters(['Notes'], () => {

describe('Hotelier Manager: Saving of Notes', () => {
    
    before('Log in as a Hotelier Manager and create a single conference booking via events calendar', () => {
        cy.clearCookies();
        cy.visit('/automation/tests/reseed')
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
        cy.contains('button', 'Reset Permissions').click()
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        eventCalendarPage.open(hotelSlug)
                         .clickToday()
        conferenceCreationModal.clickNewGuest()
              
        guestDetailsPage.fillInGuestBasicInfo(guestDetails)
                        .basketComponent.clickContinueButton()
                      
        payPage.skipPayment()
                      
        bookingHubPage.assertBookingRefIsVisible()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
    });

    it('Unified Notes Page : Saves note permanantly in the correct field only ', () => {
        
        //Verifies that special character BOOKING note is saved permanently in correct field only
  
        //Save special character note in BOOKING notes field
        cy.get(notesPage.selectors.bookingNoteField).type(specialCharacterData.bookingNotes);
        notesPage.assertNoteSavedAlert()
        //Verify that booking note is saved permanently 
        cy.get(notesPage.selectors.bookingNoteField).should('have.value', `${specialCharacterData.bookingNotes}`);

        //Verify that other notes field are not populated 
        cy.get(notesPage.selectors.conferenceLayoutField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceKitchenField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceTermField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceNoteField).should('have.value', '');
  
        
        //Navigates to booking hub and verifies that the correct note is saved in the booking notes box 
        notesPage.clickBackToBookingSummary()
        cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', specialCharacterData.bookingNotes);
        cy.get(bookingHubPage.selectors.eventNotesBox).should('contain', 'Add Event notes' );
  
         //Verify that booking note can be deleted 
        cy.get(bookingHubPage.selectors.bookingNotesBox).click()
        cy.get(notesPage.selectors.bookingNoteField).clear();
        notesPage.assertNoteSavedAlert()
        cy.get(notesPage.selectors.bookingNoteField).should('have.value', '');
  
  
  
        //Verifies that special character CONFERENCE LAYOUT note is saved permanently in correct field only
  
        //Save special character note in CONFERENCE LAYOUT notes field
        cy.get(notesPage.selectors.conferenceLayoutField).type(specialCharacterData.conferenceLayoutNotes);
        notesPage.assertNoteSavedAlert()
        //Verify that other notes field are not populated 
        cy.get(notesPage.selectors.bookingNoteField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceKitchenField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceTermField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceNoteField).should('have.value', '');
  
        //Verify that conference layout note is saved permanently in conference layout field
        cy.get(notesPage.selectors.conferenceLayoutField).should('have.value', `${specialCharacterData.conferenceLayoutNotes}`);
  
        //Navigates to booking hub and verifies that the Event Notes box remains empty
        notesPage.clickBackToBookingSummary()
        cy.contains(`${specialCharacterData.conferenceLayoutNotes}`).should('not.exist');
        cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
        cy.get(bookingHubPage.selectors.eventNotesBox).should('contain', 'Add Event notes');
  
        //Verify that conference note can be deleted 
        cy.get(bookingHubPage.selectors.eventNotesBox).click();
        cy.get(notesPage.selectors.conferenceLayoutField).clear();
        notesPage.assertNoteSavedAlert()
        cy.get(notesPage.selectors.conferenceLayoutField).should('have.value', '');
          
  
          
        //Verifies that special character CONFERENCE KITCHEN note is saved permanently in correct field only
  
        //Save special character note in CONFERENCE KITCHEN notes field
        cy.get(notesPage.selectors.conferenceKitchenField).type(specialCharacterData.conferenceKitchenNotes);
        notesPage.assertNoteSavedAlert()
        //Verify that other notes field are not populated 
        cy.get(notesPage.selectors.bookingNoteField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceLayoutField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceTermField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceNoteField).should('have.value', '');
  
        //Verify that conference kitchen note is saved permanently in conference kitchen field
        cy.get(notesPage.selectors.conferenceKitchenField).should('have.value', `${specialCharacterData.conferenceKitchenNotes}`);
  
        //Navigates to booking hub and verifies that the Event Notes box remains empty
        notesPage.clickBackToBookingSummary()
        cy.contains(`${specialCharacterData.conferenceKitchenNotes}`).should('not.exist');
        cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
        cy.get(bookingHubPage.selectors.eventNotesBox).should('contain', 'Add Event notes');
  
        //Verify that conference note can be deleted 
        cy.get(bookingHubPage.selectors.eventNotesBox).click();
        cy.get(notesPage.selectors.conferenceKitchenField).clear();
        notesPage.assertNoteSavedAlert()
        cy.get(notesPage.selectors.conferenceKitchenField).should('have.value', '');
  
  
        //Verifies that special character CONFERENCE TERMS note is saved permanently in correct field only
  
        //Save special character note in CONFERENCE TERMS notes field
        cy.get(notesPage.selectors.conferenceTermField).type(specialCharacterData.conferenceTermsNotes);
        notesPage.assertNoteSavedAlert()
        //Verify that other notes field are not populated 
        cy.get(notesPage.selectors.bookingNoteField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceLayoutField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceKitchenField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceNoteField).should('have.value', '');
  
        //Verify that conference terms note is saved permanently in conference terms field
        cy.get(notesPage.selectors.conferenceTermField).should('have.value', `${specialCharacterData.conferenceTermsNotes}`);
  
        //Navigates to booking hub and verifies that the Event Notes box remains empty
        notesPage.clickBackToBookingSummary()
        cy.contains(`${specialCharacterData.conferenceTermsNotes}`).should('not.exist');
        cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
        cy.get(bookingHubPage.selectors.eventNotesBox).should('contain', 'Add Event notes');
  
        //Verify that conference note can be deleted 
        cy.get(bookingHubPage.selectors.eventNotesBox).click();
        cy.get(notesPage.selectors.conferenceTermField).clear();
        notesPage.assertNoteSavedAlert()
        cy.get(notesPage.selectors.conferenceTermField).should('have.value', '');
  
          
        //Verifies that special character CONFERENCE NOTES note is saved permanently in correct field only
  
        //Save special character note in CONFERENCE NOTES notes field
        cy.get(notesPage.selectors.conferenceNoteField).type(specialCharacterData.conferenceNotes);
        notesPage.assertNoteSavedAlert()
        //Verify that other notes field are not populated 
        cy.get(notesPage.selectors.bookingNoteField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceLayoutField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceKitchenField).should('have.value', '');
        cy.get(notesPage.selectors.conferenceTermField).should('have.value', '');
  
        //Verify that conference notes note is saved permanently in conference notes field
        cy.get(notesPage.selectors.conferenceNoteField).should('have.value', `${specialCharacterData.conferenceNotes}`);
  
        //Navigates to booking hub and verifies that the Event Notes box remains empty
        notesPage.clickBackToBookingSummary()
        cy.contains(`${specialCharacterData.conferenceNotes}`).should('not.exist');
        cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
        cy.get(bookingHubPage.selectors.eventNotesBox).should('contain', 'Add Event notes');
  
         //Verify that conference note can be deleted 
        cy.get(bookingHubPage.selectors.eventNotesBox).click();
        cy.get(notesPage.selectors.conferenceNoteField).clear();
        notesPage.assertNoteSavedAlert()
        cy.get(notesPage.selectors.conferenceNoteField).should('have.value', '');
  
  
        //Enter conference notes on the unified notes page and verify that they are displayed on the update conference page
        cy.get(notesPage.selectors.conferenceLayoutField).type(specialCharacterData.conferenceLayoutNotes)
        notesPage.clickBody();
        cy.get(notesPage.selectors.conferenceKitchenField).type(specialCharacterData.conferenceKitchenNotes)
        notesPage.clickBody();
        cy.get(notesPage.selectors.conferenceTermField).type(specialCharacterData.conferenceTermsNotes)
        notesPage.clickBody();
        cy.get(notesPage.selectors.conferenceNoteField).type(specialCharacterData.conferenceNotes)
        notesPage.clickBody()
        notesPage.clickBackToBookingSummary()
        bookingHubPage.clickConferenceEditPencil()
        updateConferencePage.assertURL()
        
        cy.get(updateConferencePage.selectors.conferenceLayoutField).should('contain', `${specialCharacterData.conferenceLayoutNotes}`)
        cy.get(updateConferencePage.selectors.conferenceKitchenField).should('contain', `${specialCharacterData.conferenceKitchenNotes}`)
        cy.get(updateConferencePage.selectors.conferenceTermsField).should('contain', `${specialCharacterData.conferenceTermsNotes}`)
        cy.get(updateConferencePage.selectors.conferenceNotesField).should('contain', `${specialCharacterData.conferenceNotes}`)
  
        //Delete notes on the update conference page and verify that they are not displayed on the unified notes page
  
        cy.get(updateConferencePage.selectors.conferenceLayoutField).clear()
        cy.get(updateConferencePage.selectors.conferenceKitchenField).clear()
        cy.get(updateConferencePage.selectors.conferenceTermsField).clear()
        cy.get(updateConferencePage.selectors.conferenceNotesField).clear()
        updateConferencePage.clickSubmitButton()
        cy.get(bookingHubPage.selectors.bookingNotesBox).click();
  
        cy.get(notesPage.selectors.conferenceLayoutField).should('have.value', '')
        cy.get(notesPage.selectors.conferenceKitchenField).should('have.value', '')
        cy.get(notesPage.selectors.conferenceTermField).should('have.value', '')
        cy.get(notesPage.selectors.conferenceNoteField).should('have.value', '')
      })

      afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
      
})
})



