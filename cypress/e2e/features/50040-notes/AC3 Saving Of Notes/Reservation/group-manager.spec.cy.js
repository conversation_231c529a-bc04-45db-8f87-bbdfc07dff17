import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts";
import { specialCharacterData } from "../../../../../fixtures/cypress_a/notes";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { bookingsPage } from "../../../../../support/pageObjectModel/Pages/bookings";
import { housekeepingReportPage } from "../../../../../support/pageObjectModel/Pages/housekeepingReport";
import { notesPage } from "../../../../../support/pageObjectModel/Pages/unifiedNotesPage";

const hotelSlug = accounts.cypress_a.slug
const date = cy.helpers.dateYMD();

TestFilters(['Notes'], () => {

describe('Group Manager: Saving of Notes', () => {

  before('reset test seed', () => {
    cy.clearCookies();
    cy.visit('/automation/tests/reseed');
    cy.fn_login('hotelier', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
  });

    beforeEach('Unified Notes Page is accessible', () => {
    bookingsPage.open(hotelSlug)
                .clickFirstExistingBooking()
    cy.get(bookingHubPage.selectors.bookingNotesBox).click()
    notesPage.clearAllNoteFields()
  });

  it('Verifies that special character booking note is saved permanently in correct field only', () => {
      
    //Save special character note in BOOKING notes field
    cy.get(notesPage.selectors.bookingNoteField).type(specialCharacterData.bookingNotes);
    notesPage.assertNoteSavedAlert()
    //Verify that booking note is saved permanently in booking notes field
    cy.get(notesPage.selectors.bookingNoteField).should('have.value', `${specialCharacterData.bookingNotes}`);
    //Verify that other notes field are not populated 
    cy.get(notesPage.selectors.channelNoteField).should('have.value', '');
    cy.get(notesPage.selectors.housekeepingNoteField).should('have.value', '');

    //Navigates to booking hub and verifies that the correct note is saved in the booking notes box only
    notesPage.clickBackToBookingSummary()
    cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', `${specialCharacterData.bookingNotes}`);
    cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', 'Add Channel notes');
    cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', 'Add Housekeeping notes');

    //Verify that booking note can be deleted 
    cy.get(bookingHubPage.selectors.bookingNotesBox).click();
    cy.get(notesPage.selectors.bookingNoteField).clear();
    notesPage.assertNoteSavedAlert()

    cy.get(notesPage.selectors.bookingNoteField).should('have.value', '');
});
    
      

  it('Verifies that special character channel note is saved permanently in the correct field only', () => {

      //Save special character note in CHANNEL notes field
      cy.get(notesPage.selectors.channelNoteField).type(specialCharacterData.channelNotes);
      notesPage.assertNoteSavedAlert()
      //Verify that notes are saved permanently in channel notes field 
      cy.get(notesPage.selectors.channelNoteField).should('have.value', `${specialCharacterData.channelNotes}`);
      //Verify that other note fields are not populated
      cy.get(notesPage.selectors.bookingNoteField).should('have.value', '');
    cy.get(notesPage.selectors.housekeepingNoteField).should('have.value', '');

      
      //Navigates to booking hub and verifies that the correct note is saved in the channel notes box only
      notesPage.clickBackToBookingSummary()
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', `${specialCharacterData.channelNotes}`);
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', 'Add Housekeeping notes');

      //Verify that channel note can be deleted
      cy.get(bookingHubPage.selectors.channelNotesBox).click()
      cy.get(notesPage.selectors.channelNoteField).clear()
      notesPage.assertNoteSavedAlert()
      cy.get(notesPage.selectors.channelNoteField).should('have.value', '');
  });


  it('Verifies that special character houskeeping note is saved permanently in the correct field only', () => {

    //Save special character note in HOUSKEEPING notes field
      cy.get(notesPage.selectors.housekeepingNoteField).type(specialCharacterData.housekeepingNotes);
      notesPage.assertNoteSavedAlert()
      //Verify that notes are saved permanently in housekeeping notes field
      cy.get(notesPage.selectors.housekeepingNoteField).should('have.value', `${specialCharacterData.housekeepingNotes}`);
      //Verify that other notes field are not populated 
      cy.get(notesPage.selectors.bookingNoteField).should('have.value', '');
      cy.get(notesPage.selectors.channelNoteField).should('have.value', '');

      //Navigates to booking hub and verifies that the correct note is saved in the houskeeping notes box 
      notesPage.clickBackToBookingSummary()
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', 'Add Channel notes');
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', `${specialCharacterData.housekeepingNotes}`);

      //Verifies that note is displayed in the housekeeping report 
      housekeepingReportPage.open(hotelSlug, date)
      housekeepingReportPage.clickSearchButton()
      housekeepingReportPage.assertIsVisible(specialCharacterData.housekeepingNotes)

        //Verify that housekeeping note can be deleted 
      bookingsPage.open(hotelSlug)
                  .clickFirstExistingBooking()
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).click()
      cy.get(notesPage.selectors.housekeepingNoteField).clear()
      notesPage.assertNoteSavedAlert()
      cy.get(notesPage.selectors.housekeepingNoteField).should('have.value', '');

      //Verify that houskeeping report no longer displays note 
      housekeepingReportPage.open(hotelSlug, date)
      housekeepingReportPage.clickSearchButton()
      housekeepingReportPage.assertIsNotVisible(specialCharacterData.housekeepingNotes)
  })


  it('Verifies that booking note of 400 characters is displayed in full on the booking hub page', () => {
          const testData = cy.create_string(400);

      //Save 400 character note in BOOKING notes field
      cy.get(notesPage.selectors.bookingNoteField).type(testData);
      notesPage.clickBody()
      //Navigates to booking hub and verifies that the correct note is displayed in the booking notes box in full
      notesPage.clickBackToBookingSummary()
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', `${testData}`);
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', 'Add Channel notes');
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', 'Add Housekeeping notes');
  });


  it('Verifies that booking note of under 400 characters is displayed in full on the booking hub page', () => {
          const testData = cy.create_string(399);

      //Save 399 character note in BOOKING notes field
      cy.get(notesPage.selectors.bookingNoteField).type(testData);
      notesPage.clickBody()

      //Navigates to booking hub and verifies that the correct note is displayed in the booking notes box in full
      notesPage.clickBackToBookingSummary()
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', `${testData}`);
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', 'Add Channel notes');
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', 'Add Housekeeping notes');
    });
        

      it('Verifies that booking note of over 400 characters is truncated to 400 characters on the booking hub page with an ellipses at the end', () => {
          const testData = cy.create_string(401);

      //Save 401 character note in BOOKING notes field
      cy.get(notesPage.selectors.bookingNoteField).type(testData);
      notesPage.clickBody()

      //Navigates to booking hub and verifies that the correct note is displayed truncated to 400 characters on the booking hub page with an ellipses at the end
      notesPage.clickBackToBookingSummary()
          const truncatedText = testData.slice(0, -1) + "...";
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', `${truncatedText}`);
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', 'Add Channel notes');
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', 'Add Housekeeping notes');
  });


  it('Verifies that channel note of 400 characters is displayed in full on the booking hub page', () => {
          const testData = cy.create_string(400);

      //Save 400 character note in CHANNEL notes field
      cy.get(notesPage.selectors.channelNoteField).type(testData);
      notesPage.clickBody()

      //Navigates to booking hub and verifies that the correct note is displayed in the channel notes box in full
      notesPage.clickBackToBookingSummary()
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', `${testData}`);
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', 'Add Housekeeping notes');
  });


  it('Verifies that channel note of under 400 characters is displayed in full on the booking hub page', () => {
          const testData = cy.create_string(399);

      //Save 399 character note in CHANNEL notes field
      cy.get(notesPage.selectors.channelNoteField).type(testData);
      notesPage.clickBody()
    
      //Navigates to booking hub and verifies that the correct note is displayed in the channel notes box in full
      notesPage.clickBackToBookingSummary()
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', `${testData}`);
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', 'Add Housekeeping notes');
  });
        

  it('Verifies that channel note of over 400 characters is truncated to 400 characters on the booking hub page with an ellipses at the end', () => {
          const testData = cy.create_string(401);

      //Save 401 character note in CHANNEL notes field
      cy.get(notesPage.selectors.channelNoteField).type(testData);
      notesPage.clickBody()

      //Navigates to booking hub and verifies that the correct note is displayed truncated to 400 characters on the booking hub page with an ellipses at the end
      notesPage.clickBackToBookingSummary()
          const truncatedText = testData.slice(0, -1) + "...";
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', `${truncatedText}`);
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', 'Add Housekeeping notes');
  });
    

  it('Verifies that houskeeping note of 200 characters is displayed in full on the booking hub page', () => {
        const testData = cy.create_string(200);

      //Save 400 character note in HOUSEKEEPING notes field
      cy.get(notesPage.selectors.housekeepingNoteField).type(testData);
      notesPage.clickBody()

      //Navigates to booking hub and verifies that the correct note is displayed in the houskeeping notes box in full
      notesPage.clickBackToBookingSummary()
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', `${testData}`);
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', 'Add Channel notes');
  });


  it('Verifies that houskeeping note of under 200 characters is displayed in full on the booking hub page', () => {
          const testData = cy.create_string(199);

      //Save 199 character note in HOUSKEEPING notes field
      cy.get(notesPage.selectors.housekeepingNoteField).type(testData);
      notesPage.clickBody()
    
      //Navigates to booking hub and verifies that the correct note is displayed in the housekeeping notes box in full
      notesPage.clickBackToBookingSummary()
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', `${testData}`);
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', 'Add Channel notes');
  });
        

  it('Verifies that housekeeping note of over 200 characters is truncated to 200 characters on the booking hub page with an ellipses at the end', () => {
          const testData = cy.create_string(201);

      //Save 201 character note in HOUSKEEPING notes field
      cy.get(notesPage.selectors.housekeepingNoteField).type(testData);
      notesPage.clickBody()

      //Navigates to booking hub and verifies that the correct note is displayed truncated to 200 characters on the booking hub page with an ellipses at the end
      notesPage.clickBackToBookingSummary()
      const truncatedText = testData.slice(0, -1) + "..."
      cy.get(bookingHubPage.selectors.housekeepingNotesBox).should('contain', `${truncatedText}`);
      cy.get(bookingHubPage.selectors.bookingNotesBox).should('contain', 'Add Booking notes');
      cy.get(bookingHubPage.selectors.channelNotesBox).should('contain', 'Add Channel notes');
  })

  afterEach(function () {
    cy.fn_afterEachJira(this.currentTest)
  })

})
})
    


 