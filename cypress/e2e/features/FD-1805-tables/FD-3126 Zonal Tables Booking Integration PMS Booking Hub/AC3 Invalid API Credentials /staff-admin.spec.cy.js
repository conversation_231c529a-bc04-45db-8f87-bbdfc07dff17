import accounts from "../../../../../fixtures/accounts";
import { createOutletReservationPage } from "../../../../../support/pageObjectModel/Pages/createOutletReservation";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { bookingsPage } from "../../../../../support/pageObjectModel/Pages/bookings";
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings";
import { zonalOutletsPage } from "../../../../../support/pageObjectModel/Pages/zonalOutlets";
const hotelSlug = accounts.cypress_a.slug
const username = Cypress.env('zonalAPIUsername');
const invalidPassword = 'Password'

describe('Staff Admin : Invalid API Credentials', () => {

    before('Login as staff admin and access hotel get help page, enable setting and add invalid credentials', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })
        
        //add invalid credentials 
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').click({force : true})
        cy.get('#api_username').type(username)
        cy.get('#api_password').type(invalidPassword)
        cy.get('input.button[type="submit"][value=" create "]').click()

    })

    it('Verify that the API connection is displayed as failed on the create reservation page and verify that the "Import outlets" button is disabled', () => {
        bookingsPage.open(hotelSlug)
                .clickFirstExistingBooking()
        cy.get(bookingHubPage.selectors.createTableReservationButton).click()

        cy.get(createOutletReservationPage.selectors.connectionStatusBox).should('be.visible').within(() => {
            cy.contains('Connection failed').should('be.visible')
            cy.get(createOutletReservationPage.selectors.crossIcon).should('be.visible')
        })

         zonalOutletsPage.open(hotelSlug)
         cy.get(zonalOutletsPage.selectors.importOutletsButton).should('have.class', 'disabled')

         cy.get(zonalOutletsPage.selectors.connectionStatusBox).should('be.visible').within(() => {
            zonalOutletsPage.assertIsVisible('Connection failed')
            cy.get(zonalOutletsPage.selectors.crossIcon).should('be.visible')
    })

   
})    
})   