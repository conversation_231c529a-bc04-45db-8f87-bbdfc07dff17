import accounts from "../../../../../fixtures/accounts";
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { bookingsPage } from "../../../../../support/pageObjectModel/Pages/bookings";
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings";
const hotelSlug = accounts.cypress_a.slug

describe('Staff Admin : Table Reservations Access', () => {

    before('Login as staff admin and access hotel get help page, enable setting and add credentials', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })

    })

    beforeEach(() => {
        bookingsPage.open(hotelSlug)
        .clickFirstExistingBooking()
    })

    it('Verify user is able to view table reservations box with "Create table reservation" button', () => {
        cy.get(bookingHubPage.selectors.leftColumn).within(() => {
            cy.get(bookingHubPage.selectors.tableReservationBox).should('be.visible').within(() => {
                cy.get(bookingHubPage.selectors.createTableReservationButton).should('be.visible')
            })
        })
    })

    it('Click "Create table reservation button" and verify user is redirected to hotel settings with hotel config error', () => {
        bookingHubPage.clickCreateTableReservationButton()
        hotelSettingsPage.assertURL(hotelSlug)
        cy.get(pmsGeneral.selectors.messageError).should('contain', 'Hotel is not configured for zonal tables')
    })

})       