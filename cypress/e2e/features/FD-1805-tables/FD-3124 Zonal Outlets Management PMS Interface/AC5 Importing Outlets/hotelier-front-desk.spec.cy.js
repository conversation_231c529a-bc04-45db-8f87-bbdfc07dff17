
import accounts from "../../../../../fixtures/accounts"
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral";
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings"
import { zonalOutletsPage } from "../../../../../support/pageObjectModel/Pages/zonalOutlets";
const hotelSlug = accounts.cypress_a.slug
const username = Cypress.env('zonalAPIUsername');
const password = Cypress.env('zonalAPIPassword');

describe('Hotelier Front Desk : Importing Outlets', () => {

    before('Access hotel get help page, enable setting, add credentials and log in as hotelier front desk', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })

        //add credentials 
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').click({force : true})
        cy.get('#api_username').type(username)
        cy.get('#api_password').type(password)
        cy.get('input.button[type="submit"][value=" create "]').click()
        cy.fn_logout()
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)

    })

    beforeEach(() => {
        zonalOutletsPage.open(hotelSlug)
    })

    it('Verify user is able to import outlet successfully and outlet is displayed correctly', () => {
        
        zonalOutletsPage.clickImportOutletsButton()
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', '1 Zonal Outlet imported')
        zonalOutletsPage.assertDefaultImportedOutlet()
    })

   
})       