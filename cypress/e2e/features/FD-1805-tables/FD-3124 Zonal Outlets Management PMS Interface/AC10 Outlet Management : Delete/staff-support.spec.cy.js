
import accounts from "../../../../../fixtures/accounts"
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral";
import { pmsNavBar } from "../../../../../support/pageObjectModel/Components/pmsNavBar"
import { extrasPage } from "../../../../../support/pageObjectModel/Pages/extras";
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings"
import { yourStayPage } from "../../../../../support/pageObjectModel/Pages/yourStay";
import { zonalOutletsPage } from "../../../../../support/pageObjectModel/Pages/zonalOutlets";
const hotelSlug = accounts.cypress_a.slug
const username = Cypress.env('zonalAPIUsername');
const password = Cypress.env('zonalAPIPassword');
const roomSelector = '.bordered:contains("Double")'

describe('Staff Support : Outlet Management - Delete', () => {

    before('Login as staff support and access hotel get help page, enable setting and add credentials', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)

        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })

        //add credentials 
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').click({force : true})
        cy.get('#api_username').type(username)
        cy.get('#api_password').type(password)
        cy.get('input.button[type="submit"][value=" create "]').click()

        //import outlet 
        cy.get(pmsNavBar.selectors.headerMenu).within(() => {
            cy.get(pmsNavBar.selectors.zonalOutletsMenuItem).click({force : true})
        })
        zonalOutletsPage.clickImportOutletsButton()
    })


    it('Delete outlet and verify outlet is not displayed on the Availability Extras page and removed from the Zonal Outlets Page', () => {
        zonalOutletsPage.deleteOutlet()
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Zonal Outlet ZHSBooking deleted')
        cy.get('tr:eq(1)').should('not.exist')
        cy.get(zonalOutletsPage.selectors.outletsHeader).should('contain', '0 outlets')
        yourStayPage.open(hotelSlug)
        yourStayPage.clickSelectDates()
        yourStayPage.selectNextDayDate()
        yourStayPage.selectTwoDaysAheadDate()
        yourStayPage.clickSearchButton()
        yourStayPage.clickOccupancySearchButton()

        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).click();
        cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');

        yourStayPage.clickContinueButton()
        extrasPage.assertHeaderTitle()
        //assert outlet is displayed
        cy.get(extrasPage.selectors.productTile).filter(':contains("ZHSBooking")').should('not.exist')
    })
   
})       