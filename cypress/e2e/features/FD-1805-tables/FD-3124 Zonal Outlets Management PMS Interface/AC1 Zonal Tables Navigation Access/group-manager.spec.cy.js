import accounts from "../../../../../fixtures/accounts";
import { pmsNavBar } from "../../../../../support/pageObjectModel/Components/pmsNavBar";
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral";
import { dashboardPage } from "../../../../../support/pageObjectModel/Pages/dashboard";
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings";
const hotelSlug = accounts.cypress_a.slug

describe('Group Manager : Zonal Tables Navigation Access', () => {

    before('Access hotel get help page, enable setting and login as group manager', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.fn_logout()
        cy.fn_login('hotelier', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
        hotelSettingsPage.open(hotelSlug)
        dashboardPage.open(hotelSlug)        
    })

    it('Verify user can view the Zonal Outlets option under the Bookings menu item', () => {
        cy.get(pmsNavBar.selectors.headerMenu).within(() => {
            cy.get(pmsNavBar.selectors.zonalOutletsMenuItem).should('exist')
            cy.get(pmsNavBar.selectors.zonalOutletsIcon).should('exist')
        })
    }) 

    it('Attempt to access Zonal Outlets and verify user is redirected to the hotel settings page when API credentials are absent', () => {
        cy.get(pmsNavBar.selectors.headerMenu).within(() => {
            cy.get(pmsNavBar.selectors.zonalOutletsMenuItem).click({force : true})
        })
        cy.url().should('contain', `/hotels/${hotelSlug}/edit?tab=zonal-tables`)
        cy.get(pmsGeneral.selectors.messageError).should('contain', 'Hotel is not configured for zonal tables')
    })

})      