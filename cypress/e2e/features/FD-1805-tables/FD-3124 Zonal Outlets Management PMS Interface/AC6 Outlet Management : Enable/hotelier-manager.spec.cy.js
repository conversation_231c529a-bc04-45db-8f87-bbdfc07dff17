
import accounts from "../../../../../fixtures/accounts"
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral";
import { pmsNavBar } from "../../../../../support/pageObjectModel/Components/pmsNavBar"
import { editOutletPage } from "../../../../../support/pageObjectModel/Pages/editOutlet";
import { extrasPage } from "../../../../../support/pageObjectModel/Pages/extras";
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings"
import { yourStayPage } from "../../../../../support/pageObjectModel/Pages/yourStay";
import { zonalOutletsPage } from "../../../../../support/pageObjectModel/Pages/zonalOutlets";
const hotelSlug = accounts.cypress_a.slug
const username = Cypress.env('zonalAPIUsername');
const password = Cypress.env('zonalAPIPassword');
const roomSelector = '.bordered:contains("Double")'
const updatedOutletName = 'Test Outlet'
const updatedOutletDescription = 'updated outlet description'

describe('Hotelier Manager : Outlet Management - Enable and Update', () => {

    before('Access hotel get help page, enable setting, add credentials and login as hotelier manager', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })

        //add credentials 
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').click({force : true})
        cy.get('#api_username').type(username)
        cy.get('#api_password').type(password)
        cy.get('input.button[type="submit"][value=" create "]').click()

        //import outlet 
        cy.get(pmsNavBar.selectors.headerMenu).within(() => {
            cy.get(pmsNavBar.selectors.zonalOutletsMenuItem).click({force : true})
        })
        zonalOutletsPage.clickImportOutletsButton()
        cy.fn_logout()
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
    })

    beforeEach(() => {
        zonalOutletsPage.open(hotelSlug)
        cy.get(zonalOutletsPage.selectors.editOutletButton).click()
        editOutletPage.enableOutlet()

    })

    it('Enable outlet and verify outlet is displayed on the availability engine when enabled', () => {
        editOutletPage.clickSubmitButton()
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Zonal Outlet ZHSBooking updated')
        yourStayPage.open(hotelSlug)
        yourStayPage.clickSelectDates()
        yourStayPage.selectNextDayDate()
        yourStayPage.selectTwoDaysAheadDate()
        yourStayPage.clickSearchButton()
        yourStayPage.clickOccupancySearchButton()

        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).click();
        cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');

        yourStayPage.clickContinueButton()
        extrasPage.assertHeaderTitle()
        //assert outlet is displayed
        cy.get(extrasPage.selectors.productTile).filter(':contains("ZHSBooking")').should('be.visible').within(() => {
            extrasPage.assertIsVisible('Imported from HLS STG Site')
        })
        //verify image -- manual
        


    })

    it('Update outlet name and description and verify that changes are displayed on the zonal outlets page and extras page of the availability engine', () => {
        editOutletPage.editOutletName(updatedOutletName)
        editOutletPage.editOutletDescription(updatedOutletDescription)
        editOutletPage.clickSubmitButton()
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', `Zonal Outlet ${updatedOutletName} updated`)
        cy.get('tr:eq(1)').within(() => {
            cy.get('td').eq(0).should('have.text', updatedOutletName);
            cy.get('td').eq(1).should('have.text', updatedOutletDescription);
        });

        //verify updated outlet is displayed correctly o the extras page 
        yourStayPage.open(hotelSlug)
        yourStayPage.clickSelectDates()
        yourStayPage.selectNextDayDate()
        yourStayPage.selectTwoDaysAheadDate()
        yourStayPage.clickSearchButton()
        yourStayPage.clickOccupancySearchButton()

        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).click();
        cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');

        yourStayPage.clickContinueButton()
        extrasPage.assertHeaderTitle()
        //assert outlet is displayed
        cy.get(extrasPage.selectors.productTile).filter(`:contains("${updatedOutletName}")`).should('be.visible').within(() => {
            extrasPage.assertIsVisible(updatedOutletDescription)
        })
    })

   
})       