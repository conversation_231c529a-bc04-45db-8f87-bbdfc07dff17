
import accounts from "../../../../../fixtures/accounts"
import { pmsNavBar } from "../../../../../support/pageObjectModel/Components/pmsNavBar"
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings"
import { zonalOutletsPage } from "../../../../../support/pageObjectModel/Pages/zonalOutlets";
const hotelSlug = accounts.cypress_a.slug
const username = Cypress.env('zonalAPIUsername');
const password = Cypress.env('zonalAPIPassword');

describe('Staff Support : Valid Connection UI Layout', () => {

    before('Login as staff support and access hotel get help page, enable setting and add credentials', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })

        //add credentials 
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').click({force : true})
        cy.get('#api_username').type(username)
        cy.get('#api_password').type(password)
        cy.get('input.button[type="submit"][value=" create "]').click()

    })

    beforeEach(() => {
        cy.get(pmsNavBar.selectors.headerMenu).within(() => {
            cy.get(pmsNavBar.selectors.zonalOutletsMenuItem).click({force : true})
        })
    })

    it('Verify the left column UI Layout of Zonal Outlets page with valid connection', () => {
        
        cy.get(zonalOutletsPage.selectors.connectionStatusBox).should('be.visible').within(() => {
            zonalOutletsPage.assertIsVisible('Connected')
            cy.get(zonalOutletsPage.selectors.tickIcon).should('be.visible')

        })

        cy.get(zonalOutletsPage.selectors.actionsBox).should('be.visible').within(() => {
            cy.get(zonalOutletsPage.selectors.importOutletsButton).should('be.visible')
            zonalOutletsPage.assertIsVisible('Import Outlets from Zonal Tables')
            cy.get(zonalOutletsPage.selectors.editOutletText).within(() => {
                cy.get(zonalOutletsPage.selectors.editPencil).should('be.visible')
            })
            zonalOutletsPage.assertIsVisible('Edits made here will apply to the Booking Journey only. Modifications will not be updated in Zonal Tables')
            cy.get(zonalOutletsPage.selectors.outletImagesText).within(() => {
                cy.get(zonalOutletsPage.selectors.cameraIcon).should('be.visible')
            })
            zonalOutletsPage.assertIsVisible('Images can be added to an Outlet from the Gallery')
            cy.get(zonalOutletsPage.selectors.deleteOutletText).within(() => {
                cy.get(zonalOutletsPage.selectors.trashIcon).should('be.visible')
            })
            zonalOutletsPage.assertIsVisible('Deleted Outlets will not be removed from Zonal Tables')
        })
        
    })

    it('Verify the right column UI Layout of Zonal Outlets page with valid connection', () => {
        
       cy.get(zonalOutletsPage.selectors.outletsHeader). should('contain', '0 outlets')
       zonalOutletsPage.assertTableColumns()
        
    })

})       