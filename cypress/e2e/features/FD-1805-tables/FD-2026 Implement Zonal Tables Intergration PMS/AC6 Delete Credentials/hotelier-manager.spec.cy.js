import accounts from "../../../../../fixtures/accounts";
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral";
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings";
const hotelSlug = accounts.cypress_a.slug
const username = Cypress.env('zonalAPIUsername');
const password = Cypress.env('zonalAPIPassword');

describe('Hotelier Manager : Delete Intergration Setting', () => {

    before('Access hotel get help page, enable setting, add credentials and login as hotelier manager', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })

        //add credentials 
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').click({force : true})
        cy.get('#api_username').type(username)
        cy.get('#api_password').type(password)
        cy.get('input.button[type="submit"][value=" create "]').click()
        cy.fn_logout()
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').click({force : true})

    })

    it('Verify user is able to delete credentials', () => {

        cy.get('label[for="client_delete"]').should('have.text', '\n                                \n                                I would like to delete these credentials.\n                                I understand this will cause integration between the PMS and Zonal Tables to cease immediately.\n                            ')
        cy.get('#client_delete').click({force : true})
        cy.get('input.button[value=" update "]').click({force : true})
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Credentials deleted')
        cy.get('input.button[value=" create "]').should('be.visible')
        cy.get('#api_username').should('have.value', '')
        cy.get('#api_password').should('have.value', '')
    })

})       