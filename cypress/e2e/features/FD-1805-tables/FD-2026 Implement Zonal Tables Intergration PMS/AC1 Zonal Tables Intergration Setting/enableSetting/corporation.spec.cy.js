import accounts from "../../../../../../fixtures/accounts";
const hotelSlug = accounts.cypress_a.slug

describe('Corporation : Enable Zonal Tables Intergration', () => {

    before('Login as corporation', () => {
        cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
    })

    it('Verify user cannot access get help page', () => {
        let url = `/hotels/${hotelSlug}/options/help`
        cy.fn_safeVisit(url)
        cy.request({method: 'GET', url, failOnStatusCode: false}).its('status').should('equal', 401);
    })
 
})