
import accounts from "../../../../../../fixtures/accounts";
import { hotelSettingsPage } from "../../../../../../support/pageObjectModel/Pages/hotelSettings";
const hotelSlug = accounts.cypress_a.slug

describe('Group Admin : Enable Zonal Tables Intergration', () => {

    before('<PERSON><PERSON> as group manager and access hotel get help page', () => {
        cy.fn_login('group', accounts.cypress_a.group_admin.email, accounts.cypress_a.group_admin.password)
    })

    it('Verify user cannot view integration.zonal-tables setting', () => {
        hotelSettingsPage.open(hotelSlug)
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('not.exist')
    })
})