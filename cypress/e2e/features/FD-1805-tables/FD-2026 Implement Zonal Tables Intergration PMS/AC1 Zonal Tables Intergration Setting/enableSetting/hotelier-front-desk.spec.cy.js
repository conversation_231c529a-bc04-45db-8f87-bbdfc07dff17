import accounts from "../../../../../../fixtures/accounts";
const hotelSlug = accounts.cypress_a.slug

describe('Hotelier Front Desk : Enable Zonal Tables Intergration', () => {

    before('Login as hotelier front desk', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
    })

    it('Verify user cannot access get help page', () => {
        let url = `/hotels/${hotelSlug}/options/help`
        cy.fn_safeVisit(url)
        cy.request({method: 'GET', url, failOnStatusCode: false}).its('status').should('equal', 401);
    })

 
})