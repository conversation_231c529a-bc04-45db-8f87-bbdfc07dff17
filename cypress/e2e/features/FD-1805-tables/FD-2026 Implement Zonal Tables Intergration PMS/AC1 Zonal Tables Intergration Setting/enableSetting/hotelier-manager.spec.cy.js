
import accounts from "../../../../../../fixtures/accounts";
const hotelSlug = accounts.cypress_a.slug

describe('Hotelier Manager : Enable Zonal Tables Intergration', () => {

    before('Login as hotelier manager and access hotel get help page', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
    })

    it('Verify user cannot view integration.zonal-tables setting', () => {
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('not.exist')
    })
})