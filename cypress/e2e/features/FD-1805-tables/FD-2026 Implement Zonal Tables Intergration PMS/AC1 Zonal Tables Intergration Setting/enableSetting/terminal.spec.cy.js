import accounts from "../../../../../../fixtures/accounts";
const hotelSlug = accounts.cypress_a.slug

describe('Terminal : Enable Zonal Tables Intergration', () => {

    before('Login as terminal', () => {
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
    })

    it('Verify user cannot access get help page', () => {
        let url = `/hotels/${hotelSlug}/options/help`
        cy.fn_safeVisit(url)
        cy.request({method: 'GET', url, failOnStatusCode: false}).its('status').should('equal', 401);
    })
 
})