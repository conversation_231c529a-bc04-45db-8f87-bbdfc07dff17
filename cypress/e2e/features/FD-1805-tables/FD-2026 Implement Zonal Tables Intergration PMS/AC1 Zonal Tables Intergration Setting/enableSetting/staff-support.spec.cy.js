import accounts from "../../../../../../fixtures/accounts";
import { pmsGeneral } from "../../../../../../support/pageObjectModel/Components/pmsGeneral";
const hotelSlug = accounts.cypress_a.slug

describe('Staff Support : Enable Zonal Tables Intergration', () => {

    before('Login as staff support', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)
    })

    beforeEach('Access hotel get help page', () => {
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
         })
    })

    it('Verify setting is off by default', () => {
        
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('ul.fa-ul li span.value.default').should('contain.text', 'Off')
                                                .siblings('i.fa-check-square-o').should('exist');

        })    
    })

    it('Verify correct interface and description is displayed ', () => {

        cy.get('#integration\\.zonal-tables-option').should('contain', 'Enable Zonal Tables integration')
                                                    .should('contain', 'This option can set to either') 
    })

    it('Verify setting can be toggled to On and displays correct success message', () => {
    
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })

        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option integration.zonal-tables created')
        cy.url().should('contain', `/hotels/${hotelSlug}/options`)
    })
})