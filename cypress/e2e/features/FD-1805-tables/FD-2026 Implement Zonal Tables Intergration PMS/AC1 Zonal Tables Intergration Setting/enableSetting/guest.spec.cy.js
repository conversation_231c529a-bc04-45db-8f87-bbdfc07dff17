import accounts from "../../../../../../fixtures/accounts";
const hotelSlug = accounts.cypress_a.slug

describe('Guest : Enable Zonal Tables Intergration', () => {

    before('Login as guest', () => {
        cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
    })

    it('Verify user cannot access get help page', () => {
        let url = `/hotels/${hotelSlug}/options/help`
        cy.fn_safeVisit(url)
        cy.request({method: 'GET', url, failOnStatusCode: false}).its('status').should('equal', 500);
    })
 
})