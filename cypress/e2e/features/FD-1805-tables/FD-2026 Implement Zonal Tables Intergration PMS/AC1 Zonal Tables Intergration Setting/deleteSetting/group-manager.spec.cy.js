import accounts from "../../../../../../fixtures/accounts"
const hotelSlug = accounts.cypress_a.slug

const filters = ['tables'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
describe('Group Manager : Delete Intergration Setting', () => {

    before('Login as staff support and access hotel get help page, enable setting and add credentials', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.fn_logout()

    })

    it('Login as group manager and verify integration.zonal-tables setting is not editable', () => {
        cy.fn_login('hotelier', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
        cy.visit(`/hotels/${hotelSlug}/options`)
        cy.get('h2.module__header.big-right').contains('integration.zonal-tables').within(() => {
            cy.get('.fa-pencil').should('not.exist')
        })

    })

})    
})   