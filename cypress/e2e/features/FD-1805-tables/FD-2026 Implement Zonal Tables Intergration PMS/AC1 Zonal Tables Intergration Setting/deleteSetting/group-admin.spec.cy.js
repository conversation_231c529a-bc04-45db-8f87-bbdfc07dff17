import accounts from "../../../../../../fixtures/accounts"
import { hotelSettingsPage } from "../../../../../../support/pageObjectModel/Pages/hotelSettings"
const hotelSlug = accounts.cypress_a.slug

const filters = ['tables'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {

describe('Group Admin : Delete Intergration Setting', () => {

    before('Login as staff support and access hotel get help page, enable setting and add credentials', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.fn_logout()

    })

    it('Login as group admin and verify integration.zonal-tables setting is not editable', () => {
        cy.fn_login('group', accounts.cypress_a.group_admin.email, accounts.cypress_a.group_admin.password)
        cy.get('#downshift-1-toggle-button').click()
        cy.contains('a', 'Go To Property').invoke('removeAttr', 'target').click()
        cy.pause()
        cy.visit(`/hotels/${hotelSlug}/options`)
        cy.get('h2.module__header.big-right').contains('integration.zonal-tables').within(() => {
            cy.get('.fa-pencil').should('not.exist')
        })

    })

})   
})    