import accounts from "../../../../../../fixtures/accounts"
import { pmsGeneral } from "../../../../../../support/pageObjectModel/Components/pmsGeneral"
import { hotelSettingsPage } from "../../../../../../support/pageObjectModel/Pages/hotelSettings"
const hotelSlug = accounts.cypress_a.slug
const invalidPassword = 'Password'
const username = Cypress.env('zonalAPIUsername');
const password = Cypress.env('zonalAPIPassword');

describe('Staff Admin : Delete Intergration Setting', () => {

    before('Login as staff admin and access hotel get help page, enable setting and add credentials', () => {

        cy.clearAllCookies()
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        cy.visit('automation/tests/reseed')
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })

        //add credentials 
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').click({force : true})
        cy.get('#api_username').type(username)
        cy.get('#api_password').type(password)
        cy.get('input.button[type="submit"][value=" create "]').click()

    })

    it('Enter invalid password and verify setting is not deleted, remains unchanged and zonal api credentials remain stored', () => {

        cy.visit(`/hotels/${hotelSlug}/options`)
        cy.get('h2.module__header.big-right').contains('integration.zonal-tables').within(() => {
            cy.get('.fa-pencil').click({force : true})
        })
        cy.get('#deletepassword').type(invalidPassword)
        cy.get('input[value="Delete This Option"]').click({force : true})
        cy.get(pmsGeneral.selectors.messageError).should('contain', 'Option integration.zonal-tables cannot be deleted: incorrect password supplied')

        //verify setting remains unchanged 
        cy.get('h2.module__header.big-right').contains('integration.zonal-tables').next('div.box__details') 
                                             .find('pre').should('contain.text', '1');

        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
         })
        cy.get('#integration\\.zonal-tables-option').within(() => {
        cy.get('ul.fa-ul li span.value').should('contain.text', 'On')
         .siblings('i.fa-check-square-o').should('exist')
        })

        //verify zonal api credentials remain stored 
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').click({force : true})
        cy.get('#api_username').should('have.value', username)
        cy.get('input.button[value=" update "]').should('be.visible')

    })

    it('Enter valid password and verify setting is deleted, setting is removed from options page and credentials are deleted from service ', () => {
        cy.visit(`/hotels/${hotelSlug}/options`)
        cy.get('h2.module__header.big-right').contains('integration.zonal-tables').within(() => {
            cy.get('.fa-pencil').click({force : true})
        })
        cy.get('#deletepassword').type(Cypress.env('staff_password'))
        cy.get('input[value="Delete This Option"]').click({force : true})
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option integration.zonal-tables deleted')
        cy.get('body').should('not.contain', '#integration\\.zonal-tables-option')

        //verify setting displays as off on the get help page
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('ul.fa-ul li span.value.default').should('contain.text', 'Off')
                                                .siblings('i.fa-check-square-o').should('exist');
        })  

        //verify zonal api credentials are deleted from service
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Zonal Tables")').should('not.exist')

    })

})       