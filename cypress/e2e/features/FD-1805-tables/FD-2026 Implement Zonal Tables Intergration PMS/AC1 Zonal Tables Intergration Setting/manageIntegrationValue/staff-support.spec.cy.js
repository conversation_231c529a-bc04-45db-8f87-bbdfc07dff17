import accounts from "../../../../../../fixtures/accounts"
import { pmsGeneral } from "../../../../../../support/pageObjectModel/Components/pmsGeneral"
const hotelSlug = accounts.cypress_a.slug

describe('Staff Support : Manage Integration Value', () => {

    before('Login as staff support and access hotel get help page, turn setting on', () => {

        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.fn_login('staff', accounts.cypress_a.staff_support.email, accounts.cypress_a.staff_support.password)

        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })

    })

    it('Verify setting is displayed with value 1 when enabled', () => {

        cy.get('h2.module__header.big-right').contains('integration.zonal-tables').next('div.box__details') 
                                             .find('pre').should('contain.text', '1');

        //Verify setting is displayed as 'On' on get help page 
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
         })
        cy.get('#integration\\.zonal-tables-option').within(() => {
        cy.get('ul.fa-ul li span.value').should('contain.text', 'On')
         .siblings('i.fa-check-square-o').should('exist')
        })
    })

    it('Verify setting is displayed with value 0 when disabled ', () => {

        cy.get('h2.module__header.big-right').contains('integration.zonal-tables').within(() => {
            cy.get('.fa-pencil').click({force : true})
        })
        cy.get('#value').clear().type('0')
        cy.get('input.button[value="Submit"]').click()
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option integration.zonal-tables updated')
        cy.get('h2.module__header.big-right').contains('integration.zonal-tables').next('div.box__details') 
                                             .find('pre').should('contain.text', '0');

        //Verify setting is displayed as 'Off' on get help page
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
         })
        cy.get('#integration\\.zonal-tables-option').within(() => {
        cy.get('ul.fa-ul li span.value').should('contain.text', 'Off')
         .siblings('i.fa-check-square-o').should('exist')
        })
    })

})