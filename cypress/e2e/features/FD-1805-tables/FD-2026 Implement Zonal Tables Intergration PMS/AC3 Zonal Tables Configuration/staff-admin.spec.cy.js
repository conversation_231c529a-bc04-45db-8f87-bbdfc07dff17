import accounts from "../../../../../fixtures/accounts";
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral";
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings";
const hotelSlug = accounts.cypress_a.slug
const username = Cypress.env('zonalAPIUsername');
const password = Cypress.env('zonalAPIPassword');
describe('Staff Admin : Zonal Tables Configuration', () => {

    before('Login as staff admin and enable zonal tables integration', () => {
      
        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-tables').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-tables-option').within(() => {
            cy.get('i.fa.fa-li.fa-square-o').click();
        })
    })

    beforeEach('Handle uncaught exceptions', () => {
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        hotelSettingsPage.open(hotelSlug)
    })
  
    it('Verify Zonal Tables tab and API credentials box is displayed correctly in hotel settings', () => {
        cy.get('div.tab:contains("Zonal Tables")').should('be.visible').click();
        cy.get('div.box__details:eq(0)').within(() => {
            cy.get('p').invoke('text').then((text) => {
            const normalizedText = text.replace(/\s+/g, ' ').trim();
        expect(normalizedText).to.equal('Please enter your Api credentials for Zonal Tables. These credentials will be used when we connect to the Zonal Tables service on your behalf to execute actions in the PMS');
        })
    })
        cy.get('#api_username').should('be.visible')
        cy.get('label[for="api_username"]').should('be.visible')
                                           .and('have.text', 'Api Username');
        cy.get('#api_password').should('be.visible')
        cy.get('label[for="api_password"]').should('be.visible')
                                           .and('have.text', 'Api Password');
    })
  
    it('Attempt to click Create with empty API credentials and verify error message', () => {
        cy.get('div.tab:contains("Zonal Tables")').click()
        cy.get('input.button[type="submit"][value=" create "]').click()
        cy.get(pmsGeneral.selectors.messageError).contains('Incomplete credentials')
    })

    it('Attempt to click Create with empty API username and verify error message', () => {
        cy.get('div.tab:contains("Zonal Tables")').click()
        cy.get('#api_password').type(password)
        cy.get('input.button[type="submit"][value=" create "]').click()
        cy.get(pmsGeneral.selectors.messageError).contains('Incomplete credentials')
    })

    it('Attempt to click Create with empty API password and verify error message', () => {
        cy.get('div.tab:contains("Zonal Tables")').click()
        cy.get('#api_username').type(username)
        cy.get('input.button[type="submit"][value=" create "]').click()
        cy.get(pmsGeneral.selectors.messageError).contains('Incomplete credentials')
    })
    

    it('Verify staff admin can add valid API credentials and credentials are stored in the credential service', () => {
        cy.get('div.tab:contains("Zonal Tables")').click()
        cy.get('#api_username').type(username)
        cy.get('#api_password').type(password)
        cy.get('input.button[type="submit"][value=" create "]').click()
        cy.get(pmsGeneral.selectors.messageSuccess).contains('Credentials stored')

        //verify api username displays in plain text
        cy.get('input[name="api_username"]').should('have.attr', 'type', 'text')
                                            .should('have.value', username)
        //verify api password is masked - manual
    })
   
  
  });
  