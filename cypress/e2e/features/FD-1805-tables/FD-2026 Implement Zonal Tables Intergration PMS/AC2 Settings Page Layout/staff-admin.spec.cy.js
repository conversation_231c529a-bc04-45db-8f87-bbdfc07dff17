import accounts from "../../../../../fixtures/accounts"
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings"
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral"
const hotelSlug = accounts.cypress_a.slug

describe('Staff Admin : Settings Page Layout', () => {

    before(() => {
        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

    })

    beforeEach(() => {
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        hotelSettingsPage.open(hotelSlug)
    })

    it('Verify default sections are visible', () => {
        cy.get('div.tab:contains("Hotel Settings")').should('be.visible')
        cy.get('div.tab:contains("Booking Buttons")').should('be.visible')
        cy.get('div.tab:contains("Payment Gateways")').should('be.visible')
        cy.get('div.tab:contains("API Tokens")').should('be.visible')
    })

    it('Verify conditional tabs are displayed based on integration toggles', () => {
          
        //turn on integration toggles
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.toggle').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.toggle-option').within(() => {
        cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option integration.toggle created')

        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.room-genie').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.room-genie-option').within(() => {
           cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option integration.room-genie created')

        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.zonal-loyalty').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.zonal-loyalty-option').within(() => {
           cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option integration.zonal-loyalty created')

        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.eagle-eye').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.eagle-eye-option').within(() => {
           cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option integration.eagle-eye created')

        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#liveres\\.enabled').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#liveres\\.enabled-option').within(() => {
           cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option liveres.enabled created')

        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#availability\\.css\\.upload').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#availability\\.css\\.upload-option').within(() => {
           cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option availability.css.upload created')

        //Navigate to hotel settings page and verify conditional tabs are displayed 

        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Toggle")').should('be.visible')
        cy.get('div.tab:contains("Room Genie")').should('be.visible')
        cy.get('div.tab:contains("Zonal Loyalty")').should('be.visible')
        cy.get('div.tab:contains("Eagle Eye")').should('be.visible')
        cy.get('div.tab:contains("LiveRes")').should('be.visible')
        cy.get('div.tab:contains("Availability CSS")').should('be.visible')
    })

    it('Verify right column elements', () => {
        cy.get('.col2').within(() => {
            cy.get('.module__header:contains("Options")').should('be.visible')
            cy.get('.module__header:contains("Booking Engine")').should('be.visible')
            cy.get('.module__header:contains("History")').should('be.visible')
            cy.get('.module__header:contains("Manual Overrides")').should('be.visible')
            cy.get('.module__header:contains("Hotel Delete")').should('be.visible')
        })
    })
})