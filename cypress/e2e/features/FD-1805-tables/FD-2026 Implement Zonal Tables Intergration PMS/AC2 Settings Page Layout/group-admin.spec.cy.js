
import accounts from "../../../../../fixtures/accounts"
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings"
const hotelSlug = accounts.cypress_a.slug

describe('Group Admin : Settings Page Layout', () => {

    before(() => {
        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.fn_login('group', accounts.cypress_a.group_admin.email, accounts.cypress_a.group_admin.password)

    })

    beforeEach(() => {
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        hotelSettingsPage.open(hotelSlug)
    })
 

    it('Verify integration toggles are not visible', () => {    
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.toggle').should('not.exist')
        cy.get('#integration\\.room-genie').should('not.exist')
        cy.get('#integration\\.zonal-loyalty').should('not.exist')
        cy.get('#liveres\\.enabled').should('not.exist')
        cy.get('#availability\\.css\\.upload').should('not.exist')
        cy.get('#integration\\.eagle-eye').should('not.exist')
    })


    it('Verify default sections are visible and integration toggle sections are not visible', () => {

        cy.get('div.tab:contains("Hotel Settings")').should('be.visible')
        cy.get('div.tab:contains("Booking Buttons")').should('be.visible')
        cy.get('div.tab:contains("Payment Gateways")').should('be.visible')
        cy.get('div.tab:contains("API Tokens")').should('be.visible')

        cy.get('div.tab:contains("Toggle")').should('not.exist')
        cy.get('div.tab:contains("Room Genie")').should('not.exist')
        cy.get('div.tab:contains("Zonal Loyalty")').should('not.exist')
        cy.get('div.tab:contains("Eagle Eye")').should('not.exist')
        cy.get('div.tab:contains("LiveRes")').should('not.exist')
        cy.get('div.tab:contains("Availability CSS")').should('not.exist')
    })

    
    it('Verify right column elements', () => {
        cy.get('.col2').within(() => {
            cy.get('.module__header:contains("Options")').should('be.visible')
            cy.get('.module__header:contains("Booking Engine")').should('be.visible')
            cy.get('.module__header:contains("History")').should('be.visible')
            cy.get('.module__header:contains("Manual Overrides")').should('not.exist')
            cy.get('.module__header:contains("Hotel Delete")').should('not.exist')
        })
    })
})