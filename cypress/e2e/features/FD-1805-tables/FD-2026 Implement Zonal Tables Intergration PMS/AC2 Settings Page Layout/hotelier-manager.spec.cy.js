import accounts from "../../../../../fixtures/accounts"
import { hotelSettingsPage } from "../../../../../support/pageObjectModel/Pages/hotelSettings"
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral"
const hotelSlug = accounts.cypress_a.slug

describe('Hotelier Manager : Settings Page Layout', () => {

    before(() => {
        cy.clearAllCookies()
        cy.visit('automation/tests/reseed')
        cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)

    })

    beforeEach(() => {
        cy.on("uncaught:exception", (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        hotelSettingsPage.open(hotelSlug)
    })

    it('Verify default sections are visible', () => {
        cy.get('div.tab:contains("Hotel Settings")').should('be.visible')
        cy.get('div.tab:contains("Booking Buttons")').should('be.visible')
        cy.get('div.tab:contains("Payment Gateways")').should('be.visible')
        cy.get('div.tab:contains("API Tokens")').should('be.visible')
    })

    it('Verify conditional tabs are displayed based on integration toggles', () => {
          
        //verify specific integration toggles are not displayed
        cy.visit(`/hotels/${hotelSlug}/options/help`)
        cy.get('#integration\\.toggle').should('not.exist')
        cy.get('#integration\\.room-genie').should('not.exist')
        cy.get('#integration\\.zonal-loyalty').should('not.exist')
        cy.get('#liveres\\.enabled').should('not.exist')
        cy.get('#availability\\.css\\.upload').should('not.exist')

        //verify eagle eye integration toggle is displayed and enable
        cy.get('#integration\\.eagle-eye').should('be.visible').within(() => {
            cy.get('.fa-expand').click()
        })
        cy.get('#integration\\.eagle-eye-option').within(() => {
           cy.get('i.fa.fa-li.fa-square-o').click();
        })
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Option integration.eagle-eye created')


        //Navigate to hotel settings page and verify conditional tabs are displayed 

        hotelSettingsPage.open(hotelSlug)
        cy.get('div.tab:contains("Toggle")').should('not.exist')
        cy.get('div.tab:contains("Room Genie")').should('not.exist')
        cy.get('div.tab:contains("Zonal Loyalty")').should('not.exist')
        cy.get('div.tab:contains("Eagle Eye")').should('be.visible')
        cy.get('div.tab:contains("LiveRes")').should('not.exist')
        cy.get('div.tab:contains("Availability CSS")').should('not.exist')
    })

    it('Verify right column elements', () => {
        cy.get('.col2').within(() => {
            cy.get('.module__header:contains("Options")').should('be.visible')
            cy.get('.module__header:contains("Booking Engine")').should('be.visible')
            cy.get('.module__header:contains("History")').should('be.visible')
            cy.get('.module__header:contains("Manual Overrides")').should('not.exist')
            cy.get('.module__header:contains("Hotel Delete")').should('not.exist')
        })
    })
})