import hotel from '../../../fixtures/hotel.js'
import hoteliers from '../../../fixtures/hoteliers';
import TestFilters from '../../../support/filterTests.js';

const hotelier_terminal = hoteliers.filter(hotelier => {
    return hotelier.role === 'Terminal';
})[0];

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Test the Terminal user account journeys', () => {

        before('Setup Hotel Shell and Terminal Account', () => {
            // Login
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

            cy.contains('a', 'Create').should('be.visible').click()
            cy.get('form').fillCreateHotelForm(hotel).submit()
            cy.contains('a', /Staff/).click({ force: true })
            cy.contains('a', /Create Staff/).click()
            cy.get('form').fillCreateHotelierForm({
                name: hotelier_terminal.name,
                role: hotelier_terminal.title,
                email: hotelier_terminal.email,
            }).submit()
            cy.contains('td', hotelier_terminal.email).selectEditPencil().click()
            cy.get('.col1 form').fillUpdateHotelierForm({
                password: hotelier_terminal.password
            }).submit()
            cy.contains('a', 'Log Out').click()
        })

        it('can prevent a hotelier terminal user login with the wrong password', () =>  {
            // Incorrect password
            cy.visit('/login').get('form').fillLoginForm({
                email: hotelier_terminal.email,
                password: 'incorrect_password'
            }).submit()
            cy.get('.messages.error').should('contain', 'Login failed. Username or password incorrect')
        })

        it('can login a hotelier terminal user with the correct password', () =>  {
            // Correct Password
            cy.visit('/login').get('form').fillLoginForm({
                email: hotelier_terminal.email,
                password: hotelier_terminal.password
            }).submit()

            cy.get('.message.success').should('contain.text', 'Logged in')
            cy.url().should('include', hotel.slug)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})