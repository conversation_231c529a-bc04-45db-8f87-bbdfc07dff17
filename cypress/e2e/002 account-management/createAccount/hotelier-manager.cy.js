import hotel from '../../../fixtures/hotel.js'
import hoteliers from '../../../fixtures/hoteliers';
import TestFilters from '../../../support/filterTests';

const hotelier_manager = hoteliers.filter(hotelier => {
    return hotelier.role === 'Manager';
})[0];

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Account Management: Test the Hotelier Manager user account journeys', () => {

        before('Setup Hotel Shell and Hotelier Manager Account', () => {
            // Login
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

            cy.contains('a', 'Create').should('be.visible').click()
            cy.get('form').fillCreateHotelForm(hotel).submit()
            cy.contains('a', /Staff/).click({ force: true })
            cy.contains('a', /Create Staff/).click()
            cy.get('form').fillCreateHotelierForm({
                name: hotelier_manager.name,
                role: hotelier_manager.title,
                email: hotelier_manager.email,
            }).submit()
            cy.contains('td', hotelier_manager.email).selectEditPencil().click()
            cy.get('.col1 form').fillUpdateHotelierForm({
                password: hotelier_manager.password
            }).submit()
            cy.contains('a', 'Log Out').click()
        })

        it('can prevent a hotelier manager login with the wrong password', () => {
            // Login: Wrong Password
            cy.fn_login('hotelier', hotelier_manager.email, 'wrong_password')
            cy.get('.messages.error').should('contain', 'Login failed. Username or password incorrect')
        })

        it('can login a hotelier manager with the correct password', () => {
            // Login: Correct Password
            cy.fn_login('hotelier', hotelier_manager.email, hotelier_manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
            cy.get('.header__user')
                .should('contain', hotel.name)
                .should('contain', hotelier_manager.name)
        })

        it('can change a password for a hotelier manager', function() {
            // Update Password
            let newPassword = 'NewManagerPassword1234*';
            cy.contains('a', /Staff/).click({ force: true })
            cy.contains('td', hotelier_manager.email).selectEditPencil().click()
            cy.get('.col1 form').fillUpdateHotelierForm({password: newPassword}).submit()
            cy.get('.message.success').should('contain', `Account for ${hotelier_manager.name} (${hotelier_manager.email}) updated`)

            // Attempt Login with old password
            cy.fn_login('hotelier', hotelier_manager.email, hotelier_manager.password)
            cy.get('.messages.error').should('contain', 'Login failed. Username or password incorrect')

            // Login with new password
            cy.fn_login('hotelier', hotelier_manager.email, newPassword)
            cy.get('.message.success').should('contain', 'Logged in')
            cy.get('.header__user').should('contain', hotelier_manager.name)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })

})
