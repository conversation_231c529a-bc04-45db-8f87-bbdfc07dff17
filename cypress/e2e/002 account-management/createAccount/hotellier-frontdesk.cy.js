import hotel from '../../../fixtures/hotel.js'
import hoteliers from '../../../fixtures/hoteliers';
import TestFilters from '../../../support/filterTests.js';

const hotelier_front_desk = hoteliers.filter(hotelier => {
    return hotelier.role === 'Front-desk';
})[
    0
];

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Test the Hotelier Front-Desk user account journeys', () => {

        before('Setup Hotel Shell and Hotelier Front Desk Account', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.contains('a', 'Create').should('be.visible').click()
            cy.get('form').fillCreateHotelForm(hotel).submit()
            cy.contains('a', /Staff/).click({
                force: true
            })
            cy.contains('a', /Create Staff/).click()
            cy.get('form').fillCreateHotelierForm({
                name: hotelier_front_desk.name,
                role: hotelier_front_desk.title,
                email: hotelier_front_desk.email,
            }).submit()
            cy.contains('td', hotelier_front_desk.email).selectEditPencil().click()
            cy.get('.col1 form').fillUpdateHotelierForm({
                password: hotelier_front_desk.password
            }).submit()
            cy.contains('a', 'Log Out').click()
        })

        it('can prevent a hotelier front desk user login with the wrong password', () => {
            // Incorrect password
            cy.visit('/login').get('form').fillLoginForm({
                email: hotelier_front_desk.email,
                password: 'incorrect_password'
            }).submit()
            cy.get('.messages.error').should('contain', 'Login failed. Username or password incorrect')
        })

        it('can login a hotelier front desk user with the correct password', () => {
            // Correct Password
            cy.visit('/login').get('form').fillLoginForm({
                email: hotelier_front_desk.email,
                password: hotelier_front_desk.password
            }).submit()
            cy.get('.message.success').should('contain', 'Logged in')
            cy.get('.header__user').should('contain', hotelier_front_desk.name)
        })

        it('can change a password for a hotelier front desk user', () => {
            // Login
            cy.fn_login('hotelier', hotelier_front_desk.email, hotelier_front_desk.password)

            let newPassword = 'NewPassword1234*';
            // Update Password
            cy.contains('a', /Staff/).click({
                force: true
            })
            cy.contains('td', hotelier_front_desk.email).selectEditPencil().click()
            cy.get('.col1 form').fillUpdateHotelierForm({
                password: newPassword
            }).submit()
            cy.get('.message.success').should('contain', `Account for ${hotelier_front_desk.name} (${hotelier_front_desk.email}) updated`)
            // Attempt Login with old password
            cy.visit('/login').get('form').fillLoginForm({
                email: hotelier_front_desk.email,
                password: hotelier_front_desk.password
            }).submit()
            cy.get('.messages.error').should('contain', 'Login failed. Username or password incorrect')
            // Login with new password
            cy.visit('/login').get('form').fillLoginForm({
                email: hotelier_front_desk.email,
                password: newPassword
            }).submit()
            cy.get('.message.success').should('contain', 'Logged in')
            cy.get('.header__user').should('contain', hotelier_front_desk.name)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})