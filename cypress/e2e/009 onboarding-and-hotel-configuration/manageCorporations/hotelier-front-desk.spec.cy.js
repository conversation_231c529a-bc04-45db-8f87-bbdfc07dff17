import corporations from '../../../fixtures/corporations.js'
import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('On-Boarding: Hotelier front desk can create corporations', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create corporations', () => {
            cy.contains('a', 'Dashboard').should('be.visible')
            cy.contains('a', /Corporations/).click({ force: true })
            corporations.forEach(corporation => {
                cy.contains('a', /Create corporation/).click()
                cy.contains('.module__header', /Create corporation/)
                cy.get('form').fillCreateCorporationForm(corporation).submit()
                cy.get('.message.success').should('contain', 'Corporation account created')
                cy.get('.table__content')
                    .should('contain', corporation.name)
                    .should('contain', corporation.email)
            })
        })

        it('can search corporations', () => {
            cy.contains('a', 'Dashboard').should('be.visible')
            cy.contains('a', /Corporations/).click({ force: true })
            // Search Corporation by name
            cy.get('.col1 form').fillSearchForm({name: corporations[0].name}).submit()
            cy.get('.table__content')
                .should('contain', corporations[0].name)
                .should('contain', corporations[0].email)
                .should('not.contain', corporations[1].name)
                .should('not.contain', corporations[1].email)
            // Search Corporation by email
            cy.get('.col1 form').fillSearchForm({email: corporations[0].email}).submit()
            cy.get('.table__content')
                .should('contain', corporations[0].name)
                .should('contain', corporations[0].email)
                .should('not.contain', corporations[1].name)
                .should('not.contain', corporations[1].email)
            // Search for a garbage string
            cy.get('.col1 form').fillSearchForm({email: 'q!w"e£r$t%y^u&i*o'}).submit()
            cy.get('.table__content')
                .should('contain', 'No data available in table')
        })

        it('can delete corporations', () => {
            corporations.forEach(corporation => {
                cy.fn_safeVisit('/hotels/cypress-a/corporations')
                cy.contains('td', corporation.email).selectTrashCan().click()
                cy.get('.message.success').should('contain', 'Corporation account deleted')
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})