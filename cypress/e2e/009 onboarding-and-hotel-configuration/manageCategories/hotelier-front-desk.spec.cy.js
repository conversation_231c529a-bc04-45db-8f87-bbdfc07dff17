import accounts from '../../../fixtures/accounts';
import categories from '../../../fixtures/categories';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Onboarding: Hotelier Front Desk can manage Categories', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it ('can create categories', () => {
            // Navigate to Categories
            cy.contains('a', /Categories/).click()
            categories.forEach(category => {
                // Create Category
                cy.contains('a', /Create category/).click()
                cy.get('form').fillCreateCategoryForm(category).submit()
                cy.get('.message.success').should('contain', `Category ${category.title} created`)
                cy.get('.table__content')
                    .should('contain', category.title)
                    .should('contain', category.code)
            })
        });

        it ('can search categories', () => {
            // Navigate to Categories
            cy.contains('a', /Categories/).click()
            // Search Categories
            cy.get('.col1 form').fillSearchForm({
                code: categories[0].code
            }).submit()
            // Confirm filtered results
            cy.get('.table__content')
                .should('contain', categories[0].code)
                .should('not.contain', categories[1].code)
        });

        it('can delete a category', () => {
            let category = categories[0]
            // Delete a Category
            cy.contains('a', /Categories/).click({ force: true })
            cy.contains('td', category.code).selectTrashCan().click()
            cy.get('.message.success').should('contain', category.title + ' deleted')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})