import accounts from '../../../fixtures/accounts';
import products from '../../../fixtures/cypress_a/products';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Onboarding: Hotelier Front Desk can manage Products', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it ('can create products', () => {
            // Navigate to Products
            cy.contains('a', /Products/).click({ force: true })
            // Create Products
            products.forEach(product => {
                cy.contains('a', /Create product/).click()
                cy.get('form').fillCreateProductForm(product).submit()
                cy.get('.message.success').should('contain', `${product.name} created`)
                cy.get('.table__content')
                    .should('contain', product.name)
                    .should('contain', product.code)
            })
        })

        it ('can search products', () => {
            // Navigate to Products
            cy.contains('a', /Products/).click({ force: true })
            // Search Products
            cy.get('.col1 form').fillSearchForm({
                code: products[0].code
            }).submit()
            // Confirm filtered results
            cy.get('.table__content')
                .should('contain', products[0].code)
                .should('not.contain', products[1].code)
        })

        it('can delete a product', () => {
            let product = products[0]
            // Delete a Product
            cy.contains('a', /Products/).click({ force: true })
            cy.contains('td', product.code).selectTrashCan().click()
            // Code Error
            // cy.get('.message.success').should('contain', product.type + ' deleted')
            // Tmp
            cy.get('.product-index-table').should('not.contain', product.type)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})