import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic
    describe('Onboarding: Hotelier Front Desk cannot create a Group', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.fn_safeVisit('/hotels/cypress-a');
            cy.contains('a', 'Dashboard').should('be.visible')
        })

        it('cannot create a group', () => {
            cy.fn_safeVisit('/v2/groups/overview')
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})