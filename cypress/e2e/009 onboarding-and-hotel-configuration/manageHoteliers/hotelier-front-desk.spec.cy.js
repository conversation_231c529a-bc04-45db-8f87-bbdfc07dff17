import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('On-Boarding: Hotelier Front Desk can not create a hoteliers', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create hoteliers', () => {
            cy.fn_safeVisit('/hotels/cypress-a')
            cy.contains('a', 'Dashboard').should('be.visible')

            cy.contains('a', /Staff/).click({ force: true })
            cy.contains('a', /Create Staff/).click()
            cy.get('.module__header').should('contain.text','Access Denied')
        })

        it('can search hoteliers', () => {
            cy.fn_safeVisit('/hotels/cypress-a')
            cy.contains('a', 'Dashboard').should('be.visible')
            cy.contains('a', /Staff/).click({ force: true })

            // Search Hotelier
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.hotelier.manager.name}).submit()
            cy.get('.table__content')
                .should('contain', accounts.cypress_a.hotelier.manager.name)
                .should('not.contain', accounts.cypress_a.hotelier.front_desk.name)
                .should('not.contain', accounts.cypress_a.hotelier.terminal.name)

            // Search for a garbage string
            cy.get('.col1 form').fillSearchForm({email: 'q!w"e£r$t%y^u&i*o'}).submit()
            cy.get('.module__header').should('contain', '0 Staff Members')
            cy.get('.table__content')
                .should('contain', 'No data available in table')
        })

        it('can delete a hotelier', () => {
            cy.fn_safeVisit('/hotels/cypress-a/hoteliers')
            cy.contains('td', accounts.cypress_a.hotelier.terminal.email).selectTrashCan().click()
            cy.get('.module__header').should('contain.text','Access Denied')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
