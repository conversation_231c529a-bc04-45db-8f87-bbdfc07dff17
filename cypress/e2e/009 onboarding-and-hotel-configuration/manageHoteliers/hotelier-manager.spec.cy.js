import hoteliers from '../../../fixtures/hoteliers';
import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('On-Boarding: Hotelier Managers can create a hoteliers', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create hoteliers', () => {
            cy.fn_safeVisit('/hotels/cypress-a')
            cy.contains('a', 'Dashboard').should('be.visible')
            cy.contains('a', /Staff/).click({ force: true })
            cy.get('.module__header').should('contain', 'Staff Members')

            hoteliers.forEach(hotelier => {
                // Create Hotelier
                cy.contains('a', /Create Staff/).click()
                cy.get('form').fillCreateHotelierForm({
                    name: hotelier.name,
                    role: hotelier.title,
                    email: hotelier.email,
                }).submit()
                cy.get('.message.success')
                    .should('contain', `${hotelier.role} account for ${hotelier.name} (${hotelier.email}) created`)
                cy.get('.hotelier-index-table')
                    .should('contain', hotelier.name)
                    .should('contain', hotelier.title)
                    .should('contain', hotelier.role)
                    .should('contain', hotelier.email)
            })
        })

        it('can search hoteliers', () => {
            cy.fn_safeVisit('/hotels/cypress-a')
            cy.contains('a', 'Dashboard').should('be.visible')
            cy.contains('a', /Staff/).click({ force: true })

            // Search Hotelier by name
            cy.get('.col1 form').fillSearchForm({name: hoteliers[0].name}).submit()
            cy.get('.table__content')
                .should('contain', hoteliers[0].name)
                .should('contain', hoteliers[0].email)
                .should('not.contain', hoteliers[1].name)
                .should('not.contain', hoteliers[1].email)
                .should('not.contain', hoteliers[2].name)
                .should('not.contain', hoteliers[2].email)

            // Search Hotelier by email
            cy.get('.col1 form').fillSearchForm({email: hoteliers[2].email}).submit()
            cy.get('.module__header').should('contain', '1 Staff Members')
            cy.get('.table__content')
                .should('contain', hoteliers[2].name)
                .should('contain', hoteliers[2].email)
                .should('not.contain', hoteliers[0].name)
                .should('not.contain', hoteliers[0].email)
                .should('not.contain', hoteliers[1].name)
                .should('not.contain', hoteliers[1].email)

            // Search for a garbage string
            cy.get('.col1 form').fillSearchForm({email: 'q!w"e£r$t%y^u&i*o'}).submit()
            cy.get('.module__header').should('contain', '0 Staff Members')
            cy.get('.table__content')
                .should('contain', 'No data available in table')
        })

        it('can delete a hotelier', () => {
            hoteliers.forEach(hotelier => {
                cy.fn_safeVisit('/hotels/cypress-a/hoteliers')
                cy.get('.module__header').should('contain', 'Staff Members')
                cy.contains('td', hotelier.email).selectTrashCan().click()
                cy.get('.message.success').should('contain', hotelier.name + ' deleted from 1 hotel')
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
