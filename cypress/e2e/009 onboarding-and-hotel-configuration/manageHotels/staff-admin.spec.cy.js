import hotel from '../../../fixtures/hotel'
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('On-Boarding: Staff Admins', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create a hotel', () => {
            cy.contains('a', 'Create').should('be.visible').click()
            cy.get('form').fillCreateHotelForm(hotel).submit()
            cy.get('.message.success').should('contain', `${hotel.name} created`)
            cy.get('.header__inner').should('contain',  hotel.name)
            cy.url().should('contain', `/hotels/${hotel.slug}`)
        })

        it('can search hotels', () => {
            cy.contains('a', 'Hotels').click()
            cy.get('.col1 form').fillSearchForm({name: hotel.name}).submit()
            cy.get('.module__header').should('contain', hotel.name)
            cy.contains('.module__header a', hotel.name).click()
            cy.url().should('contain', `/hotels/${hotel.slug}`)
        })

        it('can update Hotel Slug and Email', () => {
            cy.contains('a', hotel.name).click();
            cy.get('.col1 .form.box form').fillUpdateHotelForm({
                'slug': hotel.slug + '_updated',
                'name': hotel.name + ' (Updated)',
                'email': 'updated_' + hotel.email
            }).submit()
            cy.get('.message.success').should('contain', `${hotel.name} (Updated) updated`)
            cy.get('.header__inner').should('contain',  `${hotel.name} (Updated)`)
            cy.url().should('contain', `/hotels/${hotel.slug}_updated`)
        })

        it('can delete a hotel', () => {
            cy.contains('a', `${hotel.name} (Updated) Settings`).click()
            cy.get('.delete form').fillDeleteForm({password: Cypress.env('staff_password')}).submit()
            cy.get('.message.success').should('contain', `Hotel ${hotel.name} (Updated) deleted`)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
