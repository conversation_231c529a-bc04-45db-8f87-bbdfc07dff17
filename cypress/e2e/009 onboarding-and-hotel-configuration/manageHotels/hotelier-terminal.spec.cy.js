import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('On-Boarding: Hotelier Terminal', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        it('can not create a hotel', () => {
            cy.fn_safeVisit('/hotels');
            cy.get('.module__header').should('contain.text', 'Page not found')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
