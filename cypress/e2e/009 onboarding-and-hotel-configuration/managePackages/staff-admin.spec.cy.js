import accounts from '../../../fixtures/accounts';
import packages from '../../../fixtures/cypress_a/packages';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Onboarding: Staff Admin can manage Packages', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create packages', () => {
            cy.contains('a', /Packages/).click({ force: true })
            packages.forEach(packageObject => {
                cy.contains('a', /Create package/).click()
                cy.contains('.module__header', /Create package/)
                cy.get('form').fillCreatePackageForm(packageObject).submit()
                cy.get('.message.success').should('contain', 'package created')
            })
        })

        it ('can search for a package', () => {
            cy.contains('a', /Packages/).click({ force: true })
            cy.get('.col1 form').fillSearchForm({code: packages[0].code}).submit()
            cy.get('.table__content')
                .should('contain', packages[0].code)
                .should('not.contain', packages[1].code)
        })

        it('can update a package', () => {
            cy.contains('a', /Packages/).click({ force: true })
            cy.get('.col1 form').fillSearchForm({code: packages[0].code}).submit()
            cy.contains('td', packages[0].code).selectEditPencil().click()
            cy.get('form').fillUpdatePackageForm({
                isEnabled: false,
                isVisible: false,
                title: 'My Updated Package',
                selectRoomTypes: ['Single', 'Double']
            }).submit()
            cy.contains('td', packages[0].code).parent('tr').should('contain.text', 'My Updated Package')
        })

        it('can delete a package', () => {
            cy.contains('a', /Packages/).click({ force: true })
            cy.get('.col1 form').fillSearchForm({code: packages[1].code}).submit()
            cy.contains('td', packages[1].code).selectTrashCan().click()
            cy.get('.message.success').should('contain.text', `${packages[1].title} package deleted`)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
