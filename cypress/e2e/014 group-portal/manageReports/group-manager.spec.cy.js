import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    const Reports = [{
        'heading':      'Sales', // for some unknown reason each word is a separate string....
        'link':         'Group Daily Sales Report',
        'page_heading': 'Daily Sales Group',
        'page_url':     'rates', // weirdly the destination URL's are the same...?
    }, {
        'heading':      'Occupancy',
        'link':         'Group Occupancy Report',
        'page_heading': 'Occupancy Report',
        'page_url':     'rates',
    }];

    describe('Group Management: Group Manager', () => {

        it('can log in to Group Portal', () => {
            cy.fn_login('group', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
            cy.url()
                .should('contain', 'groups')
                .should('contain', accounts.cypress_a.group_manager.group_slug)
            cy.get('#downshift-1-toggle-button').should('contain.text', 'Group View')
        })

        it('can select Reports', () => {
            cy.get('.router-nav').within(() => {
                // Click 'Rates'
                cy.contains('li .title', 'Reports').click()
                // Sub menu
                cy.contains('li .title', 'Reports').parent().within(() => {
                    cy.get('.content')
                        .should('be.visible')
                        .within(() => {
                            // Click `Plans`
                            cy.contains('a', 'Overview')
                                .should('be.visible')
                                .click()
                        })
                })
                cy.url()
                    .should('contain', 'reports')
                    .should('contain', 'overview')
            })
        })

        it('can see Reports', () => {

            Reports.forEach((Report) => {
                cy.contains('.reports-card', Report.heading)
                    .within(() => {
                        cy.contains('a.link', Report.link).click()
                    })
                cy.url()
                    .should('contain', Report.page_url)
                    .should('contain', 'overview')
                cy.get('.page-title h1')
                    .should('contain.text', Report.page_heading)
                // Reload Overview page
                cy.visit(`/groups/${accounts.cypress_a.group_manager.group_slug}/v2/reports/overview`)
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
