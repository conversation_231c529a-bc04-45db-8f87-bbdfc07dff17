import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Group Management: Hotelier Front Desk', () => {

        it('can log in to Group Portal', () => {
            cy.fn_login('group', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.url().should('contain', 'login')
            cy.get('.text-danger.error-title')
                .should('be.visible')
                .and('contain.text', 'messages.token:unauthorized')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
