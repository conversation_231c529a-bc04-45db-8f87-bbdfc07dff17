
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Group Management: Staff Admin', () => {

        it('can log in to Group Portal', () => {
            cy.fn_login('group', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.url().should('contain', 'login')
            cy.get('.text-danger.error-title')
                .should('be.visible')
                .and('contain.text', 'messages.token:unauthorized')
                // .and('contain.text', 'Login failed.')
                // .and('contain.text', 'Username or password incorrect')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
