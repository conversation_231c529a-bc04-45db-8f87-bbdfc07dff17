import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Group Management: Group Manager', () => {

        it('can log in to Group Portal', () => {
            cy.fn_login('group', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
            cy.url()
                .should('contain', 'groups')
                .should('contain', accounts.cypress_a.group_manager.group_slug)
            cy.get('#downshift-1-toggle-button').should('contain.text', 'Group View')
        })

        it('can see Hotel staff', () => {

            cy.intercept({method: 'POST', url: '/hoteliers/search'}).as('hoteliers')

            cy.visit(`/groups/${accounts.cypress_a.group_manager.group_slug}/v2/users/overview`).wait(['@hoteliers'])

            cy.get('h1').should('contain', 'Manage Your Staff')

            // Convert hotelier object to array..
            let hotelStaffArray = Object.keys(accounts.cypress_a.hotelier).map((key) => accounts.cypress_a.hotelier[key]);

            // Loop Staff
            hotelStaffArray.forEach(hotelier => {
                cy.contains('.tr .td .link.name', hotelier.name)
                    .should('exist')
                    .parent()
                    .parent()
                    .within($row => {
                        cy.get($row).find('.td').eq(0).should('contain.text', hotelier.name)
                        cy.get($row).find('.td').eq(1).should('contain.text', hotelier.role)
                    })
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
