import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';
const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Group Management: Group Manager', () => {

        it('can log in to Group Portal', () => {
            cy.fn_login('group', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
            cy.url()
                .should('contain', 'groups')
                .should('contain', accounts.cypress_a.group_manager.group_slug)
            cy.get('#downshift-1-toggle-button').should('contain.text', 'Group View')
        })

        it('can see and select a Hotel in list', () => {
            cy.get('#downshift-1-toggle-button').click().then(() => {
                cy.get('#downshift-1-menu').should('be.visible')
                cy.contains('.hotel-name label.link', accounts.cypress_a.title).should('be.visible')
                cy.contains('.hotel-name', accounts.cypress_a.title).within(() => {
                    cy.get('input').click()
                });
            })
            cy.get('#downshift-1-toggle-button').should('contain.text', accounts.cypress_a.title)
        })

        it('can link to a Hotel in the PMS', () => {
            cy.get('#downshift-1-toggle-button').click().then(() => {
                cy.get('#downshift-1-menu').should('be.visible')
                cy.contains('.hotel-name label.link', accounts.cypress_a.title).should('be.visible')
                cy.contains('.hotel-name', accounts.cypress_a.title).parent().within(() => {
                    cy.get('a')
                        .should('contain.text', 'Go To Property')
                        .invoke('removeAttr', 'target')
                        .click()
                });
            })
            cy.url()
                .should('contain', 'hotels')
                .should('contain', accounts.cypress_a.slug)

            // Login back to Group Portal
            cy.fn_login('group', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
