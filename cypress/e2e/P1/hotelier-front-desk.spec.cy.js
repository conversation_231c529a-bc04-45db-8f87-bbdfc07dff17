import TestFilters from '../../support/filterTests';
import accounts from '../../fixtures/accounts';

const date = cy.helpers.dateYMD();
const hotelSlug = accounts.cypress_a.slug

const pages = [
    { name: 'Dashboard',                    url: '/hotels/' + hotelSlug + `?date=${date}`},
    { name: 'Housekeeping',                 url: '/hotels/'+ hotelSlug +'/housekeeping'},
    { name: 'Staff',                        url: '/hotels/'+ hotelSlug +'/hoteliers'},
    { name: 'Gallery',                      url: '/hotels/'+ hotelSlug +'/images/create'},
    { name: 'Emails',                       url: '/hotels/'+ hotelSlug +'/emails'},
    { name: 'Emails Sent Log',              url: '/hotels/'+ hotelSlug +'/emails/sent'},
    { name: 'Calendar',                     url: '/hotels/'+ hotelSlug +`/calendar?date=${date}`},
    { name: 'Availability',                 url: '/hotels/'+ hotelSlug +'/availability'},
    { name: 'Promotional Codes',            url: '/hotels/'+ hotelSlug +'/promotions'},
    { name: 'Vouchers',                     url: '/hotels/'+ hotelSlug +`/vouchers?date=${date}`},
    { name: 'Rate Hurdles',                 url: '/hotels/'+ hotelSlug +'/v2/rates/hurdle'},
    { name: 'Policies',                     url: '/hotels/'+ hotelSlug +'/policies'},
    { name: 'Packages',                     url: '/hotels/'+ hotelSlug +`/packages?date=${date}`},
    { name: 'Rate Plans',                   url: '/hotels/'+ hotelSlug +`/v2/rates/overview`},
    { name: 'Grid',                         url: '/hotels/'+ hotelSlug +'/v2/rates/grid'},
    { name: 'Categories',                   url: '/hotels/'+ hotelSlug +'/categories'},
    { name: 'Products',                     url: '/hotels/'+ hotelSlug +'/products'},
    { name: 'Rewards',                      url: '/hotels/'+ hotelSlug +'/rewards'},
    { name: 'Reward Rates',                 url: '/hotels/'+ hotelSlug +'/rewards/rateByDate'},
    { name: 'Rooms',                        url: '/hotels/'+ hotelSlug +'/room-types'},
    { name: 'Venues',                       url: '/hotels/'+ hotelSlug +'/venues'},
    { name: 'Outlets',                      url: '/hotels/'+ hotelSlug +'/outlets'},
    { name: 'Bookings',                     url: '/hotels/'+ hotelSlug +'/bookings'},
    { name: 'Assign Reservations',          url: '/hotels/'+ hotelSlug +'/bookings/unassigned'},
    { name: 'Guests',                       url: '/hotels/'+ hotelSlug +'/guests'},
    { name: 'Corporations',                 url: '/hotels/'+ hotelSlug +'/corporations'},
    { name: 'Reports',                      url: '/hotels/'+ hotelSlug +'/v2/reports/overview'},
    { name: 'Sales Report',                 url: '/hotels/'+ hotelSlug +`/reports/sales?date=${date}`},
    { name: 'Daily Sales Report Legacy',    url: '/hotels/'+ hotelSlug + '/reports/daily'},
    { name: 'Daily Sales Report',           url: '/hotels/'+ hotelSlug + '/v2/reports/daily-sales'},
    { name: 'Weekly Sales Report',          url: '/hotels/'+ hotelSlug +`/reports/weekly?date=${date}`},
    { name: 'Monthly Sales Report',         url: '/hotels/'+ hotelSlug +`/reports/monthly?date=${date}`},
    { name: 'Postings Report',              url: '/hotels/'+ hotelSlug +`/reports/posting?date=${date}`},
    { name: 'Purchase Report',              url: '/hotels/'+ hotelSlug +`/reports/posting/purchases?date=${date}`},
    { name: 'Outlet Report',                url: '/hotels/'+ hotelSlug +`/reports/posting/outlets?date=${date}`},
    { name: 'Ledger Report',                url: '/hotels/'+ hotelSlug +`/reports/ledger?date=${date}`},
    { name: 'Credit Report',                url: '/hotels/'+ hotelSlug +`/reports/ledger/creditor?date=${date}`},
    { name: 'Debtor Report',                url: '/hotels/'+ hotelSlug +`/reports/ledger/debtor?date=${date}`},
    { name: 'Owed Report',                  url: '/hotels/'+ hotelSlug +`/reports/ledger/owed?date=${date}`},
    { name: 'Booking Report',               url: '/hotels/'+ hotelSlug +`/reports/booking?date=${date}`},
    { name: 'Client Report',                url: '/hotels/'+ hotelSlug +`/reports/booking/client?date=${date}`},
    { name: 'Referrer Report',              url: '/hotels/'+ hotelSlug +`/reports/booking/referrer?date=${date}`},
    { name: 'Room Type Report',             url: '/hotels/'+ hotelSlug +`/reports/booking/room-type?date=${date}`},
    { name: 'Cards Report',                 url: '/hotels/'+ hotelSlug +`/reports/booking/cards?date=${date}`},
    { name: 'Cancellation Report',          url: '/hotels/'+ hotelSlug +`/reports/booking/cancellation?date=${date}`},
    { name: 'Channel Report',               url: '/hotels/'+ hotelSlug +`/reports/reservation/channel?date=${date}`},
    { name: 'Invoice Report',               url: '/hotels/'+ hotelSlug +`/reports/invoice?date=${date}`},
    { name: 'Payment Report',               url: '/hotels/'+ hotelSlug +`/reports/invoice/payment?date=${date}`},
    { name: 'Deposits Report',              url: '/hotels/'+ hotelSlug +`/reports/invoice/deposit?date=${date}`},
    { name: 'Occupancy Report',             url: '/hotels/'+ hotelSlug + '/v2/reports/occupancy'},
    { name: 'Occupancy Report Legacy',      url: '/hotels/'+ hotelSlug +'/reports/occupancy'},
    { name: 'Availability Report',          url: '/hotels/'+ hotelSlug +`/reports/occupancy/availability?date=${date}`},
    { name: 'Activity Report',              url: '/hotels/'+ hotelSlug +`/reports/occupancy/activity?date=${date}`},
    { name: 'Lead Time Report',             url: '/hotels/'+ hotelSlug +`/reports/occupancy/lead-time?date=${date}`},
    { name: 'Block Report',                 url: '/hotels/'+ hotelSlug +`/reports/occupancy/block?date=${date}`},
    { name: 'Fire Safety Report',           url: '/hotels/'+ hotelSlug +`/reports/fire-safety?date=${date}`},
    { name: 'Guest Report',                 url: '/hotels/'+ hotelSlug +`/reports/guest?date=${date}`},
    { name: 'Corporation Report',           url: '/hotels/'+ hotelSlug +`/reports/corporation?date=${date}`},
    { name: 'Marketing Report',             url: '/hotels/'+ hotelSlug +`/reports/marketing?date=${date}`},
];

const filters = ['P1Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic
    describe('Hotelier Front Desk : MVP visit pages', () => {

        before('Reset Permissions',() => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/permissions')
            cy.contains('button', 'Reset Permissions').click()
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
        })

        beforeEach('Handle uncaught exceptions', () => {
            cy.on('uncaught:exception', (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
        })

        it('Visits the Trash page', () => {
            let url = '/hotels/'+ hotelSlug +'/trash'
            cy.fn_safeVisit(url)
            cy.url().should('include', url)
            cy.request({method: 'GET', url, failOnStatusCode: false}).its('status').should('equal', 403);
        })

        it('Visits the Product Rates page', () => {
            let url = '/hotels/'+ hotelSlug +'/products/rateByDate'
            cy.fn_safeVisit(url)
            cy.url().should('include', url)
            cy.request({method: 'GET', url, failOnStatusCode: false}).its('status').should('equal', 401);
        })

        it('Visits Audit Report page', () => {
            let url = '/hotels/'+ hotelSlug +`/reports/audit?date=${date}`
            cy.fn_safeVisit(url)
            cy.url().should('include', url)
            cy.request({method: 'GET', url, failOnStatusCode: false}).its('status').should('equal', 403);
        })

        pages.forEach(page => {

            it(`Visits the ${page.name} page`, () => {
                cy.fn_safeVisit(page.url)
                cy.url().should('include', page.url)
                cy.request(page.url).its('status').should('equal', 200);
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})