import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Payment Processing: Request a Payment By Link as Terminal User', () => {

        it('cannot create payment for a booking', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/bookings`)
            cy.contains('a', 'Log In').should('be.visible')
            // If a Terminal User can see the 'Login' then they are accessing this page without any logged-in privileges
            // so further tests will be as per 'public.spec.js'
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
