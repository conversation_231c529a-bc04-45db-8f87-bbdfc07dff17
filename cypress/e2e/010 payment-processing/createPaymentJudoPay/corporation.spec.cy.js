import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Payment Processing: Request a Payment By Link as a Corporation', () => {

        it('can log in', () => {
            cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
            cy.contains('button', 'Login').should('not.exist')
            cy.contains('a.button.default', 'My Account').should('be.visible')
        })

        it('cannot create payment for a booking', () => {
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/bookings`)
            cy.contains('a', 'Log In').should('be.visible')
        })

        afterEach(function () {
            cy.fn_afterEach<PERSON>ira(this.currentTest)
        })
    })
})
