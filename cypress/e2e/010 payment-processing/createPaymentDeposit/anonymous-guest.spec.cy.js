import {judopay} from '../../../fixtures/payments'
import accounts from '../../../fixtures/accounts'
import rates from '../../../fixtures/cypress_a/rates';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Payment Processing: Deposit as an anonymous Guest', () => {

        // Runs before each test in this describe() block.
        // To run it before every test, add beforeEach() to cypress/support/e2e.js.
        beforeEach(() => {
            // Clean up DB using cypress-mysql plugin:
            // Remove guest-a's stored debit cards
            cy.query(`DELETE cc
                      FROM credit_cards cc
                               JOIN hotels h ON cc.hotel_id = h.id
                               JOIN guests g ON cc.owner_id = g.id
                      WHERE cc.owner_type = 'Guest'
                        AND h.slug = '${accounts.cypress_a.slug}'
                        AND g.email = '${accounts.cypress_a.guest.email}'`)
            cy.query(`DELETE s
                            FROM stays s 
                                JOIN reservations r ON s.reservation_id = r.id
                                JOIN bookings b ON r.booking_id = b.id
                                JOIN hotels h ON b.hotel_id = h.id
                            WHERE h.slug = '${accounts.cypress_a.slug}'
                              AND b.referrer IS NULL`)
            cy.query(`DELETE r
                            FROM reservations r 
                                JOIN bookings b ON r.booking_id = b.id
                                JOIN hotels h ON b.hotel_id = h.id
                            WHERE h.slug = '${accounts.cypress_a.slug}'
                              AND b.referrer IS NULL`)
            cy.query(`DELETE b
                            FROM bookings b
                                JOIN hotels h ON b.hotel_id = h.id
                            WHERE h.slug = '${accounts.cypress_a.slug}'
                              AND b.referrer IS NULL`)
            cy.query(`DELETE dr 
                            FROM deposit_rates dr
                                JOIN hotels h ON dr.hotel_id = h.id
                            WHERE h.slug = '${accounts.cypress_a.slug}'`)
            cy.query(`INSERT INTO deposit_rates (hotel_id, 
                                        threshold, amount, amountStrategy, chargeableStrategy, algorithm)
                                    VALUES ((SELECT id FROM hotels WHERE slug = '${accounts.cypress_a.slug}' LIMIT 1), 
                                        0, 0.01, 'flat', 'firstDay', 'lowestValue')`)

        })

        it('can pay when making a booking', () => {
            // Prerequisite: empty cart
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/availability/clear-booking`)

            // Step 1
            cy.contains('body', 'Check availability').should('be.visible')

            cy.get('[data-cy="date-from"]').click();

            cy.contains('div', 'Mon').should('be.visible')

            cy.get('#' + cy.helpers.dateYMD()).click()
            cy.get('#' + cy.helpers.dateYMDfuture()).click()

            //cy.get('[data-cy="search"]').click() // TODO: enable after rebuild
            cy.get('#btnSearch').click() // TODO: delete after rebuild

            cy.get('#roomType-' + rates[0].roomTypes[0].code).find('form').first().submit()

            cy.contains('body', 'Added to booking').should('be.visible')

            cy.get('[data-cy="next"]:visible').click()


            // Step 2
            cy.contains('body', 'personal details').should('be.visible')

            cy.get('[data-cy="first-name"]').type('FirstName')
            cy.get('[data-cy="last-name"]').type('LastName')

            let email = '<EMAIL>';
            cy.get('[data-cy="email"]').type(email)
            cy.get('[data-cy="email-confirmation"]').type(email)

            cy.get('[data-cy="phone"]').type('07012345678')

            cy.get('[data-cy="address-link"]').click()

            cy.get('[data-cy="address-line-1"]').type('9 Pearl Street')
            cy.get('[data-cy="address-line-2"]').type('Dracmeadow')
            cy.get('[data-cy="city"]').type('Byhedge')
            cy.get('[data-cy="county"]').type('Bournemouth')
            cy.get('[data-cy="postcode"]').type('BH11 9BB')
            //cy.get('[data-cy="country"]').select('United Kingdom') // does not work, but it is a default value

            cy.get('[data-cy="confirm-terms"]').check({force: true})

            cy.get('[data-cy="next"]:visible').click()


            // Step 3
            cy.contains('body', 'payment details').should('be.visible')

            // Using cypress-iframe plugin:
            // This will verify that the iframe is loaded to any page other than 'about:blank'
            cy.iframe('#card-capture-for-cardUuid')
                .find('#name').type(judopay.cards.successWithoutChallenge.cardholder)
            cy.iframe('#card-capture-for-cardUuid')
                .find('#number').type(judopay.cards.successWithoutChallenge.number)
            cy.iframe('#card-capture-for-cardUuid')
                .find('#expire-month').select(judopay.cards.successWithoutChallenge.expiry.month)
            cy.iframe('#card-capture-for-cardUuid')
                .find('#expire-year').select(judopay.cards.successWithoutChallenge.expiry.year)
            cy.iframe('#card-capture-for-cardUuid')
                .find('#ccv').type(judopay.cards.successWithoutChallenge.ccv)

            cy.iframe('#card-capture-for-cardUuid')
                .find('#card-capture-form').submit()

            cy.contains('div', 'Booking confirmation', { timeout: 40 * 1000}).should('be.visible')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})