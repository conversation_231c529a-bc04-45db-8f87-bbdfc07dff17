import accounts from '../../fixtures/accounts';

import TestFilters from '../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('PMS Staff Login', () => {

        it('can log in as Staff', () => {
            cy.fn_login('staff',
                Cypress.env('staff_email'),
                Cypress.env('staff_password')
            )
            cy.get('.message.success').should('contain', 'Logged in')
            cy.location('pathname').should('eq', '/hotels')
            cy.get('.module__header').first().should('contain.text', 'Booking Search')
        })

        it('fails on incorrect Staff login', () => {
            cy.fn_login('staff', '<EMAIL>', 'password')
            cy.get('.messages.error').should('contain', 'Login failed. Username or password incorrect')
            cy.location('pathname').should('eq', '/staff/login')
            cy.get('div.title h2').should('contain.text', 'Staff Log in')
        })

        it('can log in as Hotelier', () => {
            cy.visit('/login/')
            cy.get('div.title h2')
                .should('be.visible')
                .and('contain.text', 'Hotelier Log in')
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success')
                .should('be.visible')
                .and('contain', 'Logged in')
            cy.location('pathname').should('eq', `/hotels/${accounts.cypress_a.slug}`)
            cy.get('.header__user').should('contain.text', `${accounts.cypress_a.title} Settings`)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
