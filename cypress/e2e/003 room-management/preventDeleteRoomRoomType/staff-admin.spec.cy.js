import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    const roomType = {
        'name': 'Cypress_Delete',
        'code': 'cyp_del',
        'letter': 'N',
        'color': '000000'
    }

    const booking = {
        'target': 'roomType',
        'label': 'Single',
        'day_number': 3,
        'reservation': { 'inventory': 'Basic', 'guestEmail': '<EMAIL>'}
    };

    describe('Room Management: Staff Admin cannot delete a room or room-type with an active booking', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        // @TODO Seed Bookings via the seeder

        it('can delete room type', () => {
            // cy.contains('a', 'Rooms').click({ force: true })
            // cy.get('.module__header').should('contain', 'Room Types')
            // cy.contains('.room-type-index-table td', roomType.name).selectTrashCan().click()
            // cy.get('.message.success').should('contain', `${roomType.name} deleted`)
            // Ensure RoomType is NOT visible
            // cy.get('.room-type-index-table').should('not.contain', roomType.name)
        })

        it('can delete rooms', () => {
            // cy.contains('a', /Rooms/).click({ force: true })
            // cy.contains('.room-index-table td', room.name).selectTrashCan().click()
            // cy.get('.message.success').should('contain', `Room ${room.name} deleted`)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })

    })

})
