import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

// Room codes cannot be reused when deleted, so need to be different for each role..
const rooms = [
    {   'name': 'Cy_1_SA',  'directions': 'Up stairs, on the left',     'roomTypes': ['Single']   },
    {   'name': 'Cy_2_SA',  'directions': 'Up stairs, on the right',    'roomTypes': ['Double']   },
    {   'name': 'Cy_3_SA',  'directions': 'Up stairs, end of corridor', 'roomTypes': ['Double', 'Luxury']   },
]

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Room Management: Staff Admin can create a room', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            //cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create a room', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Rooms')
            rooms.forEach(room => {
                cy.contains('a', /Create Room Number\/Name/).click()
                cy.get('form').fillCreateRoomForm(room).submit()
                cy.get('.message.success').should('contain', `Room ${room.name} created`)
                cy.url().should('contain', 'room-types')
            })
        })

        it('can delete rooms', () => {
            cy.contains('a', /Rooms/).click({ force: true })
            rooms.forEach(room => {
                cy.contains('.room-index-table td', room.name).selectTrashCan().click()
                cy.get('.message.success').should('contain', `Room ${room.name} deleted`)
            })
        })
        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
