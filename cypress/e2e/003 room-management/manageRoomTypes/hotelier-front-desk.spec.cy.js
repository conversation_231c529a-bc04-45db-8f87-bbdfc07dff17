import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

// Room Type codes cannot be reused when deleted, so need to be different for each role..
const roomTypes = [
    { 'name': 'Cypress_F', 'code': 'cyp_f', 'letter': 'F', 'color': 'dadded', 'price': 100, 'occupants': 1, 'adults': 1, 'children': 1, 'hidden': false },
    { 'name': 'Cypress_G', 'code': 'cyp_g', 'letter': 'G', 'color': 'ff7b70', 'price': 200, 'occupants': 2, 'adults': 2,                'hidden': true  }
]
const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Room Management: Hotelier Front Desk can create room types', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create a room type', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Room Types')
            roomTypes.forEach(roomType => {
                cy.contains('a', 'Create Room Type').click()
                cy.get('form').fillCreateRoomTypeForm(roomType).submit()
                cy.get('.message.success').should('contain', `${roomType.name} created`)
                cy.url().should('contain', 'room-types')
            })
        })

        it('can search room types', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Room Types')
            // Search Room Type code
            cy.get('.col1 form').fillSearchForm({
                code: roomTypes[0].code
            }).submit()
            // Confirm filtered results
            cy.get('.table__content')
                .should('contain', roomTypes[0].code)
                .should('contain', roomTypes[0].name)
                .should('not.contain', roomTypes[1].code)
                .should('not.contain', roomTypes[1].name)
            // Search Room Type name
            cy.get('.col1 form').fillSearchForm({
                title: roomTypes[0].name
            }).submit()
            // Confirm filtered results
            cy.get('.table__content')
                .should('contain', roomTypes[0].code)
                .should('contain', roomTypes[0].name)
                .should('not.contain', roomTypes[1].code)
                .should('not.contain', roomTypes[1].name)
            // Search garbage name
            cy.get('.col1 form').fillSearchForm({
                title: 'q!w"e£r$t%y^u|&i*o+p'
            }).submit()
            // Confirm filtered results
            cy.get('.table__content')
                .should('not.contain', roomTypes[0].code)
                .should('not.contain', roomTypes[0].name)
                .should('not.contain', roomTypes[1].code)
                .should('not.contain', roomTypes[1].name)
        })

        it('can delete room types', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Room Types')
            roomTypes.forEach(roomType => {
                cy.contains('.room-type-index-table td', roomType.name).selectTrashCan().click()
                cy.get('.message.success').should('contain', `${roomType.name} deleted`)
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)

        })

    })

})
