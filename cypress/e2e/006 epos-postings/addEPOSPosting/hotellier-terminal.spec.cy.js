import accounts from '../../../fixtures/accounts';
import aztec from '../../../fixtures/cypress_a/aztec';
import icr from '../../../fixtures/cypress_a/icr';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('EPOS Postings: Terminal user can post Aztec charge to a Booking', () => {

        it ('can create Aztec charges on a Booking', () => {
            // POST Charges via Aztec
            aztec.forEach(charge => {
                cy.request({
                    method: 'POST',
                    url: '/hotels/' + accounts.cypress_a.slug + '/aztec',
                    headers: {
                        'Content-Type': 'application/json',
                        'username': accounts.cypress_a.hotelier.terminal.email,
                        'password': accounts.cypress_a.hotelier.terminal.password
                    },
                    body: charge
                }).then($response => {
                    cy.log($response.body)
                    expect($response.body).to.have.property('status', 'success')
                })
            })
        })

        it ('can create ICR Charges on a Booking', () => {
            // POST to ICR
            cy.request({
                method: 'POST',
                url: '/hotels/' + accounts.cypress_a.slug+ '/icr',
                headers: {
                    'Content-Type': 'txt/plain',
                    'username': accounts.cypress_a.hotelier.terminal.email,
                    'password': accounts.cypress_a.hotelier.terminal.password
                },
                body: icr
            }).then($response => {
                cy.log($response)
                expect($response.body).to.contain('SUCCESS')
            })
        })

        it('Hotelier can login and verify postings', () => {

            // Login to PMS
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password);

            // Select Reservation
            cy.get('.col1 .dataTables_wrapper table tbody tr')
                .first()
                .find('a')
                .click()

            cy.get('li.reservations a')
                .should('be.visible')
                .should('contain', '1 Reservation')

            cy.get('li.reservations ul').should('not.be.visible')

            cy.get('.fa-expand').click()

            // Seeded Guest A: Basic : Single : Rm_1
            cy.get('li.reservations ul').should('be.visible')
                .and('contain', 'Cypress Guest A')  // Guest
                .and('contain', 'Basic')            // Rate
                .and('contain', 'Single')           // RoomType
                .and('contain', 'Rm_1')             // RoomLabel

            // Stay
            cy.get('li.reservation ul li.stays ul.list li').eq(0)
                .should('be.visible')
                .and('have.class', 'stay')
                .and('have.class', 'status')
                .and('have.class', 'active')
                .and('contain.text', 'Stay')

            // Ensure charge is visible
            aztec.forEach(charge => {
                cy.get('li.reservation ul')
                    .should('be.visible')
                    .and('contain.text', charge.items.item.description.substring(0,25))
            })

            const icrPostings = [
                {'name': 'Cypress ICR Coca-Cola', 'price': '2.99'},
                {'name': 'Cypress ICR Red Bull',  'price': '5.98'},
                {'name': 'Cypress ICR Sundry_A',  'price': '10.50'},
                {'name': 'Cypress ICR Sundry_B',  'price': '12.70'}
            ]
            icrPostings.forEach(posting => {
                cy.get('li.reservation ul')
                    .should('be.visible')
                    .and('contain.text', posting.name)
                    .and('contain.text', posting.price)
            })

            // // Auto Charge Product
            // cy.get('li.reservation ul li.stays ul.list li').eq(1)
            //     .should('be.visible')
            //     .and('have.class', 'status')
            //     .and('have.class', 'active')
            //     .and('contain.text', products[2].name)
            //
            // // (Absorbed) Auto Charge Product
            // cy.get('li.reservation ul li.stays ul.list li').eq(2)
            //     .should('be.visible')
            //     .and('have.class', 'status')
            //     .and('have.class', 'active')
            //     .and('have.class', 'absorbed')
            //     .and('contain.text', products[2].name)
        })

        // https://highlevelsoftware.visualstudio.com/HLS/_workitems/edit/47627
        it('Uses the correct VAT category for posted sundries', () => {

            // Login to PMS
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password);

            // Select Reservation
            cy.get('.col1 .dataTables_wrapper table tbody tr')
                .first()
                .find('a')
                .click()

            // Open Invoice (in the same tab)
            cy.get('.col1 .box__details .actions .print a.button')
                .invoke('removeAttr', 'target')
                .click()

            // Ensure URL is the Invoice
            cy.url().should('contain', '/print?folio=Guest')
            cy.get('table.header.box.highlight th.title')
                .should('contain.text', 'Guest Invoice')

            // Ensure Total Tax is £9.12
            cy.get('td.numeric.price.tax')
                .should('contain.text', '£9.12')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
