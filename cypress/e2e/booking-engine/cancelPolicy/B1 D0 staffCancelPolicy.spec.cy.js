import TestFilters from '../../../support/filterTests';
import {cancellationPolicyModal} from '../../../support/pageObjectModel/Components/cancellationPolicyModal';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {footerComponent} from '../../../support/pageObjectModel/Components/footer';

const hotelSlug = accounts.cypressHotel3.slug

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Show Cancellation Policy as staff', () => {
    before('log in as staff', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    })

    it('Access Cancellation Policy as staff', () => {
        yourStayPage.open(hotelSlug)
        footerComponent.clickCancellationPolicyButton()
        cancellationPolicyModal.assertContentIsCorrect(hotelSlug)
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})
