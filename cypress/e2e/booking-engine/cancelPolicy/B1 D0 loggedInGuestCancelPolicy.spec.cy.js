import TestFilters from '../../../support/filterTests';
import accounts from '../../../fixtures/accounts';
import { header } from '../../../support/pageObjectModel/Components/header';
import {cancellationPolicyModal} from '../../../support/pageObjectModel/Components/cancellationPolicyModal';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import {footerComponent} from '../../../support/pageObjectModel/Components/footer';

const hotelSlug = accounts.cypressHotel3.slug

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Show Cancellation Policy as a logged in guest', () => {
    before('log in as guest', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.clearCookies();
        cy.clearLocalStorage();

        cy.fn_login('guest', accounts.cypressHotel3.guest.email, accounts.cypressHotel3.guest.password, accounts.cypressHotel3.slug)
        //Verify user is logged in as guest 
        header.assertLoginButtonText(accounts.cypressHotel3.guest.name)
    })

    it('Access Cancellation Policy as a logged in guest', () => {
        yourStayPage.open(hotelSlug)
        footerComponent.clickCancellationPolicyButton()
        cancellationPolicyModal.assertContentIsCorrect(hotelSlug)
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})
