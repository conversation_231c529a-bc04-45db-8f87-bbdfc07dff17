import TestFilters from "../../../support/filterTests";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../support/pageObjectModel/Pages/thankYou";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { dateConverter } from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings";
import { dashboardPage } from "../../../support/pageObjectModel/Pages/dashboard";

import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../fixtures/guestDetails";
import accounts from "../../../fixtures/accounts";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const hotelSlug = accounts.cypressHotel6.slug
const roomSelector = yourStayPage.selectors.twinRoomBox
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)
const card = validJudoPayCards.frictionlessSuccess

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Create booking as logged in corporation with full deposit', () => {

    before('log in as corporation', () => {
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.fn_login('corporation', accounts.cypressHotel6.corporation.email, accounts.cypressHotel6.corporation.password, accounts.cypressHotel6.slug)
    })

    it('Create booking', () => {

    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
    yourStayPage.clickSearchButton()
                .clickOccupancySearchButton()
  
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click()
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    
    yourStayPage.clickContinueButton()
    extrasPage.clickContinueButton()

    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    payPage.assertDepositPaymentMessage('200.00')
    paymentIframe.fillJudoPayDetails(card)

    thankYouPage.assertURL()
    cy.contains(`Confirmation sent to: ${guestDetails.email}`).should('be.visible')
    cy.get(thankYouPage.selectors.bookingReferenceConfirmation).should('contain', 'Confirmation number:' )
    thankYouPage.clickBillingBreakdownChevron()
    thankYouPage.assertGuestDetails(guestDetails)

    //post booking checks 
    cy.get(thankYouPage.selectors.bookingReference).invoke('text').then(text => {
      const bookingRef = text
    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

    //verify booking is displayed on the dashboard
    cy.fn_safeVisit(`/hotels/${hotelSlug}?date=${date}`)
    dashboardPage.assertBookingListed(bookingRef)

    //verify booking is visible on the bookings page 
    bookingsPage.open(hotelSlug)
    bookingsPage.assertBookingListed(bookingRef)

    cy.contains(bookingRef).click()
    
    //confirm booking is captured and accurate on the booking hub
    bookingHubPage.assertPaymentStatus('paid')
    bookingHubPage.clickExpandBookingButton()
    cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
    .then((text) => {
      const normalizedText = text.replace(/\s+/g, ' ').trim();
      expect(normalizedText).to.include('Base Rate : Twin Room')
                            .to.include('£200.00')
                            .to.include(`${currentDateFormatted} £200.00`)
    })
    bookingHubPage.assertPaymentAmount(200.00)
                  .assertBalanceAmount(0.00)
                  .assertGuestDetails(guestDetails)
                  .assertBookingDates(date, nextDayDate)
    bookingHubPage.assertCorrectCreditCardAttached(card)
   
    cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )

    //Verify booking is displayed on the payment report 
    cy.fn_safeVisit(`/hotels/${hotelSlug}/reports/invoice/payment?date=${date}`)
    cy.contains(bookingRef).should('be.visible')

    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug)
    .assertBookingExists(bookingRef, guestDetails)

    })
    
    //Verify accurate rates grid availability 
    // gridPage.open(hotelSlug)
    //         .assertNoAvailability(gridPage.cypressHotel6Selectors.twinRoomAvailabilityCurrentDay)
    
    })

    afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
    })
  })
})
    