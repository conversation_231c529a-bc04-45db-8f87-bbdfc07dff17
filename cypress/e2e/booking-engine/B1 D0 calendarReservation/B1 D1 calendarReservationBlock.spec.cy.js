import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { basketComponent } from "../../../support/pageObjectModel/Components/basket";

import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../fixtures/guestDetails";
import { dateConverter } from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const card = validJudoPayCards.frictionlessSuccess
const hotelSlug = accounts.cypressHotel6.slug
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Create booking as staff by converting calendar block into reservation', () => {
    before('log in as staff and convert block to reservation', () => {   
    
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    
    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    calendarPage.open(hotelSlug, date)
  
    cy.get(calendarPage.selectors.firstBlockCalendarEntry).click()
    cy.get(calendarPage.selectors.convertToBookingButton).click({force : true})
    cy.get(calendarPage.selectors.okButton).click()
    cy.get(calendarPage.selectors.selectRateButton).click()
    cy.get(calendarPage.selectors.newGuestButton).click()
     })

    it('Access booking engine as staff and create booking', () => {
    guestDetailsPage.assertURL(hotelSlug)
    guestDetailsPage.assertPageHeader()
    guestDetailsPage.assertPageTitle()
    basketComponent.assertTotal('£100.00')
    cy.get(basketComponent.selectors.basketRateName).should ('contain', 'Base Rate')
    //verify correct number of guests --todo
    cy.get(basketComponent.selectors.arrivalDateDepartureDateNumNights).should('contain', '1 night')
    //verify arrival dates are displayed in correct format --todo

    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    payPage.assertDepositPaymentMessage('100.00')
    paymentIframe.fillJudoPayDetails(card)

    //post booking checks
    //confirm booking is captured and accurate on the booking hub
    cy.get(bookingHubPage.selectors.paymentStatusHeader, {timeout : 10000}).should('be.visible')
    bookingHubPage.assertPaymentStatus('paid')
    bookingHubPage.clickExpandBookingButton()
    cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
    .then((text) => {
      const normalizedText = text.replace(/\s+/g, ' ').trim();
      expect(normalizedText).to.include('Base Rate : Single Room')
                            .to.include('£100.00')
                            .to.include(`${currentDateFormatted} £100.00`)
    })
   
    bookingHubPage
        .assertPaymentAmount(100.00)
        .assertBalanceAmount(0.00)
        .assertGuestDetails(guestDetails)
        .assertBookingDates(date, nextDayDate)
   
   cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )
     

    cy.get(bookingHubPage.selectors.bookingReference).invoke('text').then((text) => {
      const bookingRef = text.replace(/\s+/g, ' ').trim();
    //verify booking is displayed on the dashboard
     cy.fn_safeVisit(`/hotels/${hotelSlug}?date=${date}`)
     cy.contains(bookingRef).should('be.visible')

    // //verify booking is visible on the bookings page 
    bookingsPage.open(hotelSlug)
                .assertBookingListed(bookingRef)
     cy.contains(bookingRef).click()

     // //Verify booking is displayed on the payment report 
    cy.fn_safeVisit(`/hotels/${hotelSlug}/reports/invoice/payment?date=${date}`)
    cy.contains(bookingRef).should('be.visible')

    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug, date)
                .assertBookingExists(bookingRef)

    })

    // //Verify accurate rates grid availability 
    // gridPage.open(hotelSlug)
    // cy.get(gridPage.cypressHotel6Selectors.singleRoomAvailabilityCurrentDay).should('contain', '1')
   
    });

    afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
  })

})
})    
