import TestFilters from "../../../support/filterTests";
import { roomsPage } from "../../../support/pageObjectModel/Pages/rooms";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import accounts from "../../../fixtures/accounts";

const hotelSlug = accounts.cypressHotel5.slug
const testRoomType = 'Test Room'

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Verify staff is able to delete a room type and room type is not displayed on the Your Stay page as staff', () => {
    before('Login as staff and create room type', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

        //verify room type is displayed on your stay page before deletion
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.testRoomBox).should('exist')

        roomsPage.open(hotelSlug)
        roomsPage.deleteRoom(testRoomType)
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', `${testRoomType} deleted`)
    })

    it('Navigate to booking engine as staff and verify room type is not displayed on Your Stay page', () => {
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.testRoomBox).should('not.exist')
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

    })
    
})