import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { updateRoomTypePage } from "../../../support/pageObjectModel/Pages/updateRoomTypePage";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";

const testRoomType = 'Double Room'
const hotelSlug = accounts.cypressHotel3.slug

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Verify staff is able to hide a room type and view room type on the Your Stay page as staff', () => {

    before('Verify double room type is visible to staff before being hidden', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.doubleRoomBox).should('be.visible')   
    })

    it('Set room type to hidden, access booking engine as staff and verify double room type is displayed on Your Stay page', () => {
        cy.fn_safeVisit(`/hotels/${hotelSlug}/room-types/dbl/edit`)
        updateRoomTypePage.setRoomTypeToHidden()
        updateRoomTypePage.submitForm()
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', `${testRoomType} updated`)

        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.doubleRoomBox).should('be.visible')
        cy.get(yourStayPage.selectors.roomBoxes).contains(testRoomType).parents(yourStayPage.selectors.roomBoxes).within(() => {
            cy.get(yourStayPage.selectors.checkAvailabilityButton).should('be.visible')
        })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

    })

})