import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { createRoomTypePage } from "../../../support/pageObjectModel/Pages/createRoomTypePage";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";

const hotelSlug = accounts.cypressHotel3.slug
const testRoomType = { 'name': 'Test Room', 'code': 'test', 'description': 'test description','letter': 'Z', 'color': 'dadded', 'price': 100, 'occupants': 1, 'adults': 1, 'children': 1, 'hidden': false }

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Verify staff is able to add a room and view room type on the Your Stay page as guest', () => {
    before('Login as staff and create room type', () => {
        
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.clearCookies();
        cy.clearLocalStorage();
        //verify room type is not displayed before addition 
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.testRoomBox).should('not.exist')

        //add room type
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        createRoomTypePage.open(hotelSlug)
        cy.get(createRoomTypePage.selectors.createRoomTypeForm).fillCreateRoomTypeForm(testRoomType).submit()
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', `${testRoomType.name} created`)
        cy.fn_logout()
    })

    it('Navigate to booking engine as guest and verify room type is displayed on Your Stay page', () => {
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.testRoomBox).should('be.visible')
        cy.get(yourStayPage.selectors.roomBoxes).contains(testRoomType.name).parents(yourStayPage.selectors.roomBoxes).within(() => {
            cy.get(yourStayPage.selectors.checkAvailabilityButton).should('be.visible')
            cy.get(yourStayPage.selectors.roomTypeName).should('contain', testRoomType.name)
            cy.get(yourStayPage.selectors.roomTypeDescription).should('contain', testRoomType.description)
            cy.get(yourStayPage.selectors.roomTypeSleepsCounter).should('contain', `Sleeps ${testRoomType.occupants}`)
        })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

    })
    
})