import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { guestDetails } from "../../../fixtures/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const hotelSlug = accounts.cypressHotel6.slug
const date = cy.helpers.dateYMD()
const checkInDate = addDaysToDate(5);
const checkOutDate = addDaysToDate(6);
const finalExpectedPrice = 200;
const paymentStatus = 'pending';
const card = validJudoPayCards.frictionlessSuccess

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe("Create booking as staff on the calendar with a new guest", function() {
    it("Create booking as staff on the calendar with a new guest", function () {

        let formatter = Intl.NumberFormat('en-GB', {style: 'currency', currency: 'GBP'});
        
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));
        
        calendarPage.open(hotelSlug, date)

        calendarPage.initCalendar();
        calendarPage.assertRoomTypeHasName('fam', 'Family Room');
        calendarPage.clickRoomNumberFutureDay(4, 5)

        let reservation = {
            inventory: 'Base Rate @ £200.00',
            roomType: 'Family',
            adults: 1,
            reservationType: 'New Guest'
        };

        cy.get(calendarPage.selectors.bookingCreationModalForm).should('be.visible')
            .fillCalendarModal(reservation);

        guestDetailsPage.assertURL(hotelSlug)

        cy.get(guestDetailsPage.selectors.yourDetailsBox).should('exist');

        cy.get(guestDetailsPage.selectors.guestDetailsForm)
            .fillBookingEngineGuestDetailsForm(guestDetails);

        guestDetailsPage.clickContinueButton();

        // Fill the card details form and confirm the booking
        payPage.assertURL()
        paymentIframe.fillJudoPayDetails(card)

        // Once redirected to the booking page, confirm that the details are correct.
        bookingHubPage.assertURL()

        bookingHubPage.assertBookingDates(checkInDate, checkOutDate);
        bookingHubPage.assertGuestDetails(guestDetails);
        bookingHubPage.assertTotalAmount(finalExpectedPrice)
        bookingHubPage.assertPaymentAmount(0.00)
        bookingHubPage.assertBalanceAmount(finalExpectedPrice);
        bookingHubPage.assertPaymentStatus(paymentStatus);

        cy.get(bookingHubPage.selectors.bookingReference).then(($element) => {
            // Ensure that the booking exists on the calendar
            let bookingReference = $element.text().replace(/\W/g,'');
            calendarPage.open(hotelSlug, date)
            calendarPage.initCalendar();
            calendarPage.assertBookingExists(bookingReference);
        });

        // Ensure that the availability appears correctly on the rates grid
        // gridPage.open(hotelSlug)
        //         .assertNoAvailability(gridPage.cypressHotel6Selectors.familyRoomAvailabilityFiveDaysAhead)
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})