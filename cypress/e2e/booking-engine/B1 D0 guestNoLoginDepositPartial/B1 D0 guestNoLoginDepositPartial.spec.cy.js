import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../support/pageObjectModel/Pages/thankYou";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";

import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../fixtures/guestDetails";
import { dateConverter} from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings";
import { dashboardPage } from "../../../support/pageObjectModel/Pages/dashboard";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const card = validJudoPayCards.frictionlessSuccess
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)
const hotelSlug = accounts.cypressHotel6.slug
const roomSelector = yourStayPage.selectors.doubleRoomBox

TestFilters(['Booking Engine'], () => {

describe('Create booking as not logged in guest with partial deposit', () => {
    before('seed hotel', () => {
        
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.clearCookies();
    cy.clearLocalStorage();
    })

    it('Access booking engine as guest and create booking', () => {
    yourStayPage.open(hotelSlug)
                .assertHeaderTitle()

    //Verify select dates button is displayed 
    cy.get(yourStayPage.selectors.dateSelector).should('contain', 'Select dates')
    //assert search button is displayed 
    cy.get(yourStayPage.selectors.searchButton).should('be.visible')
    //assert occupancy selector is displayed with 'Guests' title and icon
    cy.get(yourStayPage.selectors.occupancySelector).should('be.visible')
                                                    .should('contain', 'Guests')
    cy.get(yourStayPage.selectors.occupancyIcon).should('be.visible')

    //assert all visible room types are displayed with 'check availability' button is displayed next to each room -- needs data attribute
    cy.get(yourStayPage.selectors.roomBoxes).contains('Single Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
      cy.get(yourStayPage.selectors.checkAvailabilityButton).should('be.visible')   
    })
    cy.get(yourStayPage.selectors.roomBoxes).contains('Double Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
        cy.get(yourStayPage.selectors.checkAvailabilityButton).should('be.visible')   
      })
    cy.get(yourStayPage.selectors.roomBoxes).contains('Family Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
        cy.get(yourStayPage.selectors.checkAvailabilityButton).should('be.visible')   
    })
    cy.get(yourStayPage.selectors.roomBoxes).contains('Luxury Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
        cy.get(yourStayPage.selectors.checkAvailabilityButton).should('be.visible')   
    })
    cy.get(yourStayPage.selectors.roomBoxes).contains('Twin Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
        cy.get(yourStayPage.selectors.checkAvailabilityButton).should('be.visible')   
    })

    yourStayPage.clickSelectDates()
    //Verify select dates modal is displayed 
    cy.get(yourStayPage.selectors.selectDatesModal).should('be.visible')
    //Verify 2 month calendar is displayed 
    cy.get(yourStayPage.selectors.twoMonthCalendar).find(yourStayPage.selectors.calendarMonth).should('have.length', 2);

    //Verify select button is displayed and disabled upon opening the modal
    cy.get(yourStayPage.selectors.selectButton).should('be.visible')
    cy.get(yourStayPage.selectors.selectButton).should('have.class','disabled')
   
    yourStayPage.selectCurrentDate()
    
    //verify select button remains disabled after first date selection
    cy.get(yourStayPage.selectors.selectButton).should('have.class','disabled')

    yourStayPage.selectNextDayDate()
    //Verify select button becomes enabled after second date selection
    cy.get(yourStayPage.selectors.selectButton).should('be.visible')
                                               .should('not.be.disabled')

    //search for availability
    yourStayPage.clickSearchButton()
    yourStayPage.clickOccupancySearchButton()
    
    //add double room base rate plan
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');

    
    yourStayPage.clickContinueButton()
    extrasPage.assertHeaderTitle()
    extrasPage.assertProgressBar()
    extrasPage.clickContinueButton()

    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    //Verify partial deposit message is displayed 
    payPage.assertDepositPaymentMessage('75.00')
    paymentIframe.fillJudoPayDetails(card)

    thankYouPage.assertURL()
    thankYouPage.clickBillingBreakdownChevron()
    thankYouPage.assertGuestDetails(guestDetails)
 
    //post booking checks
    cy.get(thankYouPage.selectors.bookingReference).invoke('text').then(text => {
    const bookingRef = text
    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

    //verify booking is displayed on the dashboard
    dashboardPage.open(hotelSlug)
                 .assertBookingListed(bookingRef)
    cy.contains(bookingRef).should('be.visible')

    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug, date)
                .assertBookingExists(bookingRef)

    //verify booking is visible on the bookings page 
    bookingsPage.open(hotelSlug)
                .assertBookingListed(bookingRef)
    cy.contains(bookingRef).click()
    
    //confirm booking is captured and accurate on the booking hub
    bookingHubPage.assertPaymentStatus('part-paid')
                   .assertBalanceAmount(75.00)
                   .assertPaymentAmount(75.00)
                   .assertGuestDetails(guestDetails)
                   .assertBookingDates(date, nextDayDate)
    bookingHubPage.assertCorrectCreditCardAttached(card)
    bookingHubPage.clickExpandBookingButton()
    cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
     .then((text) => {
       const normalizedText = text.replace(/\s+/g, ' ').trim();
       expect(normalizedText).to.include('Base Rate : Double Room')
                             .to.include('£150.00')
                             .to.include(`${currentDateFormatted} £150.00`)
    })

    
    cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )

    //Verify booking is displayed on the payment report 
    cy.fn_safeVisit(`/hotels/${hotelSlug}/reports/invoice/payment?date=${date}`)
    cy.contains(bookingRef).should('be.visible')
    })

    //Verify accurate rates grid availability 
    // gridPage.open(hotelSlug)
    //         .assertNoAvailability(gridPage.cypressHotel6Selectors.doubleRoomAvailabilityCurrentDay)
    }); 

  afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
  })

})
})
    

    
