import TestFilters from "../../../../support/filterTests";
import accounts from "../../../../fixtures/accounts";
import { yourStayPage } from "../../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../../support/pageObjectModel/Pages/thankYou";
import { bookingHubPage } from "../../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../../support/pageObjectModel/Pages/calendar";

import { validJudoPayCards } from "../../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../../fixtures/guestDetails";
import { dateConverter } from "../../../../support/functions/formatDate";
import { addDaysToDate } from "../../../../support/functions/addDaysToDate";
import { bookingsPage } from "../../../../support/pageObjectModel/Pages/bookings";
import { paymentIframe } from "../../../../support/pageObjectModel/Components/paymentIFrame";

const card = validJudoPayCards.frictionlessSuccess
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const twoDaysAheadDate = addDaysToDate(2);
const twoDaysAheadDateFormatted = dateConverter(twoDaysAheadDate)
const threeDaysAheadDate = addDaysToDate(3)
const threeDaysAheadFormatted = dateConverter(threeDaysAheadDate)
const fourDaysAheadDate = addDaysToDate(4)
const fourDaysAheadDateFormatted = dateConverter(fourDaysAheadDate)
const fiveDaysAheadDate = addDaysToDate(5)
const ratePlan = "MinStay3"
const hotelSlug = accounts.cypressHotel5.slug

TestFilters(['Booking Engine'], () => {

describe('Create booking as guest with min stay rate restriction', () => {

    before('log in as staff and access booking engine as guest', () => {
        
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

    cy.clearCookies();
    cy.clearLocalStorage();
    yourStayPage.open(hotelSlug)
  })

    it('Verify rate plan with minimum stay of 3 nights is not displayed when guest searches for 2 night stay', () => {
    //Search for 2 nights stay 
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectTwoDaysAheadDate()
    yourStayPage.clickSearchButton()
    yourStayPage.clickOccupancySearchButton()

    yourStayPage.assertIsNotVisible(ratePlan)
    
    })

    it('Verify rate plan with minimum stay of 3 nights is displayed when guest searches for 3 night stay and confirm booking', () => {
    //Search for 3 night stay
    yourStayPage.clickSelectDates()
    yourStayPage.selectTwoDaysAheadDate()
    yourStayPage.selectFiveDaysAheadDate()
    
    yourStayPage.clickSearchButton()
    yourStayPage.clickOccupancySearchButton()
    yourStayPage.addRatePlanByName(ratePlan)
    yourStayPage.clickContinueButton()
    extrasPage.clickContinueButton()

    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    payPage.assertNoDepositMessage()
    paymentIframe.fillJudoPayDetails(card)

    thankYouPage.assertURL()
    thankYouPage.clickBillingBreakdownChevron()
    thankYouPage.assertGuestDetails(guestDetails)

    //post booking checks
    cy.get(thankYouPage.selectors.bookingReference).invoke('text').then(text => {
      const bookingRef = text
    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    
    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug, date)
    cy.get(calendarPage.selectors.bookingCalendarEntry).should('contain',`${guestDetails.firstName} ${guestDetails.lastName}`)

      //verify booking is visible on the bookings page
    bookingsPage.open(hotelSlug)
                 .assertBookingListed(bookingRef)
    cy.contains(bookingRef).click()
    })

    //confirm booking is captured and accurate on booking hub page
    bookingHubPage.assertPaymentStatus('pending')
                  .assertBalanceAmount(360.00)
                  .assertPaymentAmount(0.00)
                  .assertTotalAmount(360.00)
    bookingHubPage.assertGuestDetails(guestDetails)
                  .assertBookingDates(twoDaysAheadDate, fiveDaysAheadDate)
    bookingHubPage.assertCorrectCreditCardAttached(card)
    bookingHubPage.clickExpandBookingButton()
    cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
     .then((text) => {
       const normalizedText = text.replace(/\s+/g, ' ').trim();
       expect(normalizedText).to.include('MinStay3 : Double Room')
                             .to.include('£360.00')
                             .to.include(`${twoDaysAheadDateFormatted} £120.00`)
                             .to.include(`${threeDaysAheadFormatted} £120.00`)
                             .to.include(`${fourDaysAheadDateFormatted} £120.00`)
    })

    cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )
    //Verify accurate rates grid availability 
  
    // gridPage.open(hotelSlug)
    // gridPage.assertNoAvailability(gridPage.cypressHotel5Selectors.doubleRoomAvailabilityTwoDaysAhead)
    //         .assertNoAvailability(gridPage.cypressHotel5Selectors.doubleRoomAvailabilityThreeDaysAhead)
    //         .assertNoAvailability(gridPage.cypressHotel5Selectors.doubleRoomAvailabilityFourDaysAhead)   
    })

  afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
  })
    
  })
  })

    
