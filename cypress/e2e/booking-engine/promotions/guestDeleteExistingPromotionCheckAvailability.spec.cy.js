import TestFilters from '../../../support/filterTests'
import accounts from '../../../fixtures/accounts'
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay'
import {promotionsPage} from '../../../support/pageObjectModel/Pages/promotions/list'

const hotelSlug = accounts.cypressHotel5.slug
const promotionCode = 'Test1'
const rateName = 'PromoRate';

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Check if deleted promotions disappears from the availability as guest', () => {
    before('log in as staff', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.clearCookies()
        cy.clearLocalStorage()
    })

    it('Access booking engine as guest and use promotion code', () => {
        yourStayPage
            .open(hotelSlug)
            .addDiscount(promotionCode)
            .assertPromotionIsAdded()
        cy.reload()
        yourStayPage
            .clickSelectDates()
            .selectCurrentDate()
            .selectNextDayDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanListed(rateName)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));
        promotionsPage
            .open(hotelSlug)
            .delete(promotionCode)
        cy.fn_logout()

        cy.clearCookies()
        cy.clearLocalStorage()
        yourStayPage
            .open(hotelSlug)
            .addDiscount(promotionCode)

        cy.get(yourStayPage.selectors.alertMessage).should('exist')
                                                  .should('contain', `Invalid Promotion code`)
        
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})
