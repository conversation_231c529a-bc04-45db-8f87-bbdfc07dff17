import TestFilters from '../../../support/filterTests'
import accounts from '../../../fixtures/accounts'
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay'
import {promotionsPage} from '../../../support/pageObjectModel/Pages/promotions/list'

const hotelSlug = accounts.cypressHotel5.slug
const promotionCode = 'Test1'
const rateName = 'PromoRate';

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Check if deleted promotions disappears from the availability as staff', () => {

    before('log in as staff', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));
    })

    it('Access booking engine as staff and use promotion code', () => {
    
        yourStayPage
            .open(hotelSlug)
            .selectPromotionFromDropdown(promotionCode)
            .assertPromotionIsAdded()
        cy.reload()
        yourStayPage
            .clickSelectDates()
            .selectCurrentDate()
            .selectNextDayDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanListed(rateName)

        promotionsPage
            .open(hotelSlug)
            .delete(promotionCode)

        yourStayPage
            .open(hotelSlug)
        cy.get(yourStayPage.selectors.addVoucherButton).click();
        cy.get(yourStayPage.selectors.voucherDropdownField).should('not.contain', promotionCode)
        
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})
