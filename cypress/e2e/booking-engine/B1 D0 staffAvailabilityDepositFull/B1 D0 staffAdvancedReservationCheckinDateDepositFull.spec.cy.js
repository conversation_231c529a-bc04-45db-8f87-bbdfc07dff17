import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";

import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../fixtures/guestDetails";
import { dateConverter } from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings"; 
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const roomSelector = yourStayPage.selectors.twinRoomBox
const hotelSlug = accounts.cypressHotel6.slug
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)
const nextDayDateFormatted = dateConverter(nextDayDate)
const twoDaysAheadDate = addDaysToDate(2);
const card = validJudoPayCards.frictionlessSuccess

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic
describe('Create booking as staff with full deposit', () => {
    before('log in as staff', () => {   
    
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
     })

    it('Access booking engine as staff and create booking', () => {
    yourStayPage.open(hotelSlug)
    yourStayPage.clickSelectDates()
    yourStayPage.selectNextDayDate()
    yourStayPage.selectTwoDaysAheadDate()
    yourStayPage.clickSearchButton()
    yourStayPage.clickOccupancySearchButton()

    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    
    yourStayPage.clickContinueButton()
    extrasPage.clickContinueButton()

    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    payPage.assertDepositPaymentMessage('200.00')
    paymentIframe.fillJudoPayDetails(card)
  
    //post booking checks
    //confirm booking is captured and accurate on the booking hub
    cy.get(bookingHubPage.selectors.paymentStatusHeader, {timeout: 10000}).should('contain', 'Payment: paid').then(() => {
      bookingHubPage.clickExpandBookingButton()
    })
    
    cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
    .then((text) => {
      const normalizedText = text.replace(/\s+/g, ' ').trim();
      expect(normalizedText).to.include('Base Rate : Twin Room')
                            .to.include('£200.00')
                            .to.include(`${nextDayDateFormatted} £200.00`)
    })
   
    bookingHubPage.assertPaymentAmount(200.00)
                  .assertBalanceAmount(0.00)
                  .assertGuestDetails(guestDetails)
                  .assertBookingDates(nextDayDate, twoDaysAheadDate)
    bookingHubPage.assertCorrectCreditCardAttached(card)
    
   
   cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )
   

    cy.get(bookingHubPage.selectors.bookingReference).invoke('text').then((text) => {
      const bookingRef = text.replace(/\s+/g, ' ').trim();

    // //verify booking is visible on the bookings page 
    bookingsPage.open(hotelSlug)
     cy.contains(bookingRef).should('be.visible')
     cy.contains(bookingRef).click()

    // //Verify booking is displayed on the payment report 
    cy.fn_safeVisit(`/hotels/${hotelSlug}/reports/invoice/payment?date=${date}`)
    cy.contains(bookingRef).should('be.visible')

    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug, date)
    calendarPage.assertBookingExists(bookingRef)
    })

    //Verify accurate rates grid availability 
    // gridPage.open(hotelSlug)
    // cy.get(gridPage.cypressHotel6Selectors.twinRoomAvailabilityNextDay).should('contain', '0')
  });   

  afterEach(function () {
    cy.fn_afterEachJira(this.currentTest)
  })

})
})
    
