import TestFilters from '../../../support/filterTests';
import {guestDetailsPage} from '../../../support/pageObjectModel/Pages/guestDetails';
import {payPage} from '../../../support/pageObjectModel/Pages/pay';
import {bookingsPage} from '../../../support/pageObjectModel/Pages/bookings';
import {bookingHubPage} from '../../../support/pageObjectModel/Pages/bookingHub';

import {guestDetails} from '../../../fixtures/guestDetails';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {extrasPage} from '../../../support/pageObjectModel/Pages/extras';
import {calendarPage} from '../../../support/pageObjectModel/Pages/calendar';
import { validJudoPayCards } from '../../../fixtures/cards/paymentDetailsJudopay';
import {addDaysToDate} from '../../../support/functions/addDaysToDate';
import {gridPage} from '../../../support/pageObjectModel/Pages/grid';
import { paymentIframe } from '../../../support/pageObjectModel/Components/paymentIFrame';

const date = cy.helpers.dateYMD();
const tomorrow = addDaysToDate(1);
const hotelSlug = accounts.cypressHotel11.slug
let packageTitle = 'Bed and breakfast';
const card = validJudoPayCards.frictionlessSuccess

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Book a package plan as staff', () => {
    before('access as staff', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    })

    it('Access booking engine as staff and create package-based booking', () => {

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
        yourStayPage
            .assertRatePlanListed(packageTitle)
            .addRatePlanByName(packageTitle)
            .basketComponent.clickContinueButton()

        extrasPage.basketComponent.clickContinueButton()

        guestDetailsPage
            .fillInGuestBasicInfo(guestDetails)
            .clickEnterAddressManually()
            .fillInGuestAddressFields(guestDetails)
            .basketComponent.clickContinueButton()

        payPage.assertURL()
        paymentIframe.fillJudoPayDetails(card)
        
        cy.get(bookingHubPage.selectors.paymentStatusHeader, {timeout: 20000}).should('exist')
            bookingHubPage
                .assertPaymentStatus('pending')
                .assertPaymentAmount(0.00)
                .assertBalanceAmount(75.00)
                .assertGuestDetails(guestDetails)
                .assertArrivalDate(tomorrow)
                .assertCorrectCreditCardAttached(card)

        cy.get(bookingHubPage.selectors.bookingReference).invoke('text').then((text) => {
            const bookingRef = text.replace(/\s+/g, ' ').trim();
            bookingsPage
                .open(hotelSlug)
                .assertBookingListed(bookingRef)    

            calendarPage
                .open(hotelSlug, date)
                .assertBookingExists(bookingRef)
        })

            // gridPage
            //     .open(hotelSlug)
            //     .assertNoAvailability(gridPage.selectors.doubleRoomAvailabilityNextDay)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})