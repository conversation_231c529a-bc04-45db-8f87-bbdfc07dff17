import TestFilters from '../../../support/filterTests';
import accounts from '../../../fixtures/accounts';
import { guestDetails } from "../../../fixtures/guestDetails";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { validJudoPayCards } from '../../../fixtures/cards/paymentDetailsJudopay';
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { paymentIframe } from '../../../support/pageObjectModel/Components/paymentIFrame';

const hotelSlug = accounts.cypressHotel6.slug
const date = cy.helpers.dateYMD()
const expectedDepositText = '200.00';
const balanceAmount = '0.00'
const paymentStatus = 'paid'
const card = validJudoPayCards.frictionlessSuccess

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe ('Create a booking on the calendar as a staff member which uses a 100% deposit rate plan', () => {
    it('Create a booking on the calendar as a staff member which uses a 100% deposit rate plan', {}, () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));
        
        calendarPage.open(hotelSlug, date)

        calendarPage.initCalendar();

        calendarPage.assertRoomTypeHasName('twn', 'Twin Room');

        calendarPage.clickRoomNumberCurrentDay(6)

        let reservation = {
            inventory: 'Base Rate @ £200.00',
            roomType: 'Twin',
            adults: 1,
            reservationType: 'New Guest'
        };
        cy.get(calendarPage.selectors.bookingCreationModalForm).should('be.visible')
            .fillCalendarModal(reservation);


        guestDetailsPage.assertURL(hotelSlug)

        cy.get(guestDetailsPage.selectors.yourDetailsBox).should('exist');

        cy.get(guestDetailsPage.selectors.guestDetailsForm)
            .fillBookingEngineGuestDetailsForm(guestDetails);

        guestDetailsPage.clickContinueButton();

        payPage.assertURL()
        payPage.assertDepositPaymentMessage(expectedDepositText);
        paymentIframe.fillJudoPayDetails(card)

        // Once redirected to the booking page, confirm that the details are correct.
        bookingHubPage.assertURL()

        bookingHubPage.assertGuestDetails(guestDetails);
        bookingHubPage.assertTotalAmount(expectedDepositText);
        bookingHubPage.assertBalanceAmount(balanceAmount);
        bookingHubPage.assertPaymentStatus(paymentStatus)

        cy.get(bookingHubPage.selectors.bookingReference).then(($element) => {
            // Ensure that the booking exists on the calendar
            let bookingReference = $element.text().replace(/\W/g,'');
            calendarPage.open(hotelSlug, date)
            calendarPage.initCalendar();
            calendarPage.assertBookingExists(bookingReference);
        });

        // Ensure that the availability appears correctly on the rates grid
        // gridPage.open(hotelSlug)
        // cy.get(gridPage.cypressHotel6Selectors.twinRoomAvailabilityCurrentDay).should('contain', '1');
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})