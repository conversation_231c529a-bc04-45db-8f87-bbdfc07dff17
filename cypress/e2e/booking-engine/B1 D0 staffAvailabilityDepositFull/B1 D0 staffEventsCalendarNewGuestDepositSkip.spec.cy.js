import TestFilters from '../../../support/filterTests';
import accounts from '../../../fixtures/accounts';
import {guestDetailsPage} from '../../../support/pageObjectModel/Pages/guestDetails';
import {payPage} from '../../../support/pageObjectModel/Pages/pay';
import {bookingsPage} from '../../../support/pageObjectModel/Pages/bookings';
import {bookingHubPage} from '../../../support/pageObjectModel/Pages/bookingHub';
import {eventCalendarPage} from '../../../support/pageObjectModel/Pages/eventCalendar';
import {dashboardPage} from '../../../support/pageObjectModel/Pages/dashboard';
import {conferenceCreationModal} from '../../../support/pageObjectModel/Components/conferenceCreationModal';
import {guestDetails} from '../../../fixtures/guestDetails';

const date = cy.helpers.dateYMD();
const hotelSlug = accounts.cypressHotel6.slug

//skipped due to FD-6080: Booking created with card payment does not appear on the Events Calendar after submission.
// The booking is confirmed and card is attached, but not reflected in the Events Calendar UI.
//Re-enable this test after the issue is resolved and verified.

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe.skip('Create event booking as staff for new guest', () => {

    before('log in as staff', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    })

    it('Access event calendar as staff and create booking for new guest', () => {
        eventCalendarPage
            .open(hotelSlug)
            .clickToday()

        conferenceCreationModal.clickNewGuest()

        guestDetailsPage
            .fillInGuestBasicInfo(guestDetails)
            .selectPostcodeLookupButton()
            .selectFirstAddressFromDropdown()
            .basketComponent.clickContinueButton()

        payPage
            .assertSkipPaymentMessage()
            .skipPayment()

        bookingHubPage.postBookingChecks(bookingRef => {
            dashboardPage
                .open(hotelSlug)
                .assertBookingListed(bookingRef)

            bookingsPage
                .open(hotelSlug)
                .assertBookingListed(bookingRef)

            bookingHubPage
                .open(bookingRef, hotelSlug)
                .clickExpandBookingButton()
                .assertPaymentStatus('pending')
                .assertPaymentAmount(0.00)
                .assertBalanceAmount(0.00)
                .assertBookingDates(date, date)
            bookingHubPage.assertGuestDetails(guestDetails)

            eventCalendarPage
                .open(hotelSlug)
                .assertBookingListed(bookingRef, guestDetails)
        })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})