import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { basketComponent } from "../../../support/pageObjectModel/Components/basket";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { guestDetails } from "../../../fixtures/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

// Skipped due to FD-3560: Package is enabled when it should be disabled
// Re-enable this test after the issue is resolved and verified.

const hotelSlug = accounts.cardiff.slug;
const roomSelector = yourStayPage.selectors.doubleRoomBox;
const checkInDate = addDaysToDate(1);
const checkOutDate = addDaysToDate(2);
const finalExpectedPrice = 75;
const card = validJudoPayCards.frictionlessSuccess
const voucherCode = 'CYPRESS-TEST-1';
const voucherLink = `/hotels/${hotelSlug}/availability/apply-voucher/${voucherCode}`;

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe.skip("Create booking as staff through voucher link", function() {
    it("Create booking as staff through voucher link", function() {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));

        let formatter = Intl.NumberFormat('en-GB', {style: 'currency', currency: 'GBP'});

        cy.fn_safeVisit(voucherLink);

        cy.url().should('contain', yourStayPage.url);
        cy.get(pmsGeneral.selectors.toastContainer).should('contain', 'Voucher has been added to your cart');

        yourStayPage.clickSelectDates();
        yourStayPage.selectNextDayDate();
        yourStayPage.selectTwoDaysAheadDate();
        yourStayPage.clickSearchButton();
        yourStayPage.clickOccupancySearchButton();

        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();

        cy.get(yourStayPage.selectors.continueButton).should('be.visible');
        yourStayPage.clickContinueButton();

        cy.get(basketComponent.selectors.basketTotal).should('exist');
        cy.get(basketComponent.selectors.basketTotal).should('contain', formatter.format(finalExpectedPrice));

        extrasPage.clickContinueButton();

        // Fill the guest details form and move on to the pay page
        cy.get(guestDetailsPage.selectors.yourDetailsBox).should('exist');

        cy.get(guestDetailsPage.selectors.guestDetailsForm)
            .fillBookingEngineGuestDetailsForm(guestDetails);

        guestDetailsPage.clickContinueButton();

        // Fill the card details form and confirm the booking
        cy.url().should('contain', 'availability/payment');
        payPage.assertDepositPaymentMessage('75.00')
        paymentIframe.fillJudoPayDetails(card)

        // Ensure that we reach the booking hub page and it contains the correct information
        cy.url({timeout: 20000}).should('contain', '/hotels/' + hotelSlug + '/bookings/');

        bookingHubPage.assertTotalAmount(formatter.format(finalExpectedPrice).slice(1))
        bookingHubPage.assertBalanceAmount(0.00)
                      .assertPaymentAmount(75.00)
                      .assertCorrectCreditCardAttached(card)
        bookingHubPage.assertGuestDetails(guestDetails);
        bookingHubPage.assertBookingDates(checkInDate, checkOutDate);

        // Ensure that the booking exists on the calendar
        cy.get(bookingHubPage.selectors.bookingReference).then(($element) => {
            let bookingReference = $element.text().replace(/\W/g,'');
            cy.fn_safeVisit('/hotels/' + hotelSlug + '/calendar');
            calendarPage.initCalendar();
            calendarPage.assertBookingExists(bookingReference);
        });

        // // Ensure that the availability appears correctly on the rates grid
        // cy.fn_safeVisit(`/hotels/${accounts.cardiff.slug}/v2/rates/grid`);
        // cy.get(gridPage.selectors.doubleRoomAvailabilityNextDay).should('contain', '0');
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})