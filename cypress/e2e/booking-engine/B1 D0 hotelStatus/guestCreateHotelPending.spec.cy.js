import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { hotelSettingsPage } from "../../../support/pageObjectModel/Pages/hotelSettings";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";

const hotelSlug = accounts.cypressHotel2.slug

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Verify guest is unable to access the booking engine when hotel status is set to "Pending"', () => {
    before('log in as staff and esnure hotel status to pending', () => {

    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    hotelSettingsPage.open(hotelSlug)
                     .assertStatusSetToPending()
    cy.fn_logout()
    
    })

    it('verify guest is not able to access booking engine', () => {
    yourStayPage.open(hotelSlug)
    cy.contains('Page not found').should('be.visible')
    cy.contains('Sorry, this page does not exist!').should('be.visible')
        cy.request({
            url: `/hotels/${hotelSlug}/availability`,
            failOnStatusCode: false,
          }).then((response) => {
            expect(response.status).to.equal(404);
          });
    })

    afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
    })
})
})
    