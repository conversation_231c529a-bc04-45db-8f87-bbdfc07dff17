import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { hotelSettingsPage } from "../../../support/pageObjectModel/Pages/hotelSettings";

const hotelSlug = accounts.cypressHotel2.slug

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Verify staff can successfully access the booking engine when hotel status is set to "Pending"', () => {
    before('log in as staff and ensure hotel status to pending', () => {
        
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    hotelSettingsPage.open(hotelSlug)
                     .assertStatusSetToPending()
    
    })

    it('verify staff is able to access booking engine', () => {
    yourStayPage.open(hotelSlug)
                .assertHeaderTitle()
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})
    