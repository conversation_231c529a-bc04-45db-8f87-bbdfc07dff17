import TestFilters from '../../../support/filterTests';
import {promotionsPage} from '../../../support/pageObjectModel/Pages/promotions/list';
import {createPromotionPage} from '../../../support/pageObjectModel/Pages/promotions/create';
import accounts from '../../../fixtures/accounts';

const hotelSlug = accounts.cypressHotel5.slug
const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic


describe('Create promotion as staff', () => {
    before('log in as staff', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    })

    it('Verify staff is able to access promotions list and create a promotion', () => {
        const promotionCode = 'PROMO1';
        const descriptionCode = 'Description';

        promotionsPage
            .open(hotelSlug)
            .clickCreatePromotionButton()

        createPromotionPage
            .fillInCode(promotionCode)
            .fillInDescription(descriptionCode)
            .checkRatesSelectAll()
            .submitForm()

        promotionsPage
            .open(hotelSlug)
            .assertPromotionListed(promotionCode)
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})
