import TestFilters from "../../../support/filterTests";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { header } from "../../../support/pageObjectModel/Components/header";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { basketComponent } from "../../../support/pageObjectModel/Components/basket";
import { createVoucherPage } from "../../../support/pageObjectModel/Pages/createVoucherPage";
import accounts from "../../../fixtures/accounts";

const voucherCode = 'B1D1GAVCYS' + Date.now().toString().slice(-8);
const voucherName = 'B1D1-guestAddVoucherCheckYourStay';
const voucherDescription = voucherName
const hotelSlug = accounts.cypressHotel4.slug
const voucherAmount = 1000;
const bookingValue = '£250.00'
const expectedFinalValue = '£240';
const roomSelector = yourStayPage.selectors.luxuryRoomBox;

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe ('Create a voucher in the PMS and then attempt to use it on the booking engine', () => {

    it('Create a voucher and then attempt to use it', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));

        createVoucherPage.open(hotelSlug)

        cy.get(createVoucherPage.selectors.form).fillCreateVoucherForm({
            description: voucherName,
            code: voucherCode,
            amount: voucherAmount,
            strategy: 'byAmount',
            room_type_id: 'all'
        }).submit();

        cy.get(pmsGeneral.selectors.messageSuccess).should('exist').should('contain', voucherName + ' Voucher created');

        // Attempt to create a booking and use the voucher
        yourStayPage.open(hotelSlug)
                    .assertHeaderTitle()
        header.assertNoLoginButton()
        yourStayPage.clickSelectDates();
        yourStayPage.selectNextDayDate();
        yourStayPage.selectTwoDaysAheadDate();
        yourStayPage.clickSearchButton();
        yourStayPage.clickOccupancySearchButton();

        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
        cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');

        //check price before voucher 
        cy.get(basketComponent.selectors.basketTotal).should('contain', bookingValue)

        // Add the voucher
        yourStayPage.selectVoucherFromDropdown(voucherName).assertVoucherIsAdded()

        //check price after voucher
        cy.get(basketComponent.selectors.basketTotal).should('contain', expectedFinalValue);
        cy.get(basketComponent.selectors.wholeBasket).should('contain', voucherDescription)
                                                     .and('contain', 'Voucher added to your cart')
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})