import TestFilters from "../../../support/filterTests";
import { ratesPage } from "../../../support/pageObjectModel/Pages/rates";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import accounts from "../../../fixtures/accounts";

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

const hotelSlug = accounts.cypressHotel4.slug

describe('Verify staff is able to set a rate plan to unbookable and rate plan is not displayed on the Your Stay page as staff', () => {
    before('Login as staff and create room type', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

        //verify rate plan is displayed on your stay page before setting to unbookable
        yourStayPage
                    .open(hotelSlug)
                    .clickSelectDates()
                    .selectCurrentDate()
                    .selectNextDayDate()
                    .clickSearchButton()
                    .clickAdultSubtractCounter();
        yourStayPage.clickOccupancySearchButton()

        cy.get(yourStayPage.selectors.roomBoxes).contains('Single Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
            cy.get(yourStayPage.selectors.baseRate).should('be.visible')
          })
        
        ratesPage.open(hotelSlug)
        ratesPage.clickBaseRateEditPencil('sng')
        ratesPage.makeRateUnbookable()
        })

    it('Navigate to booking engine as guest and verify rate plan is not displayed on Your Stay page', () => {
        yourStayPage
                    .open(hotelSlug)
                    .clickSelectDates()
                    .selectCurrentDate()
                    .selectNextDayDate()
                    .clickSearchButton()
                    .clickAdultSubtractCounter();
        yourStayPage.clickOccupancySearchButton()

        cy.get(yourStayPage.selectors.roomBoxes).contains('Single Room').should('not.exist')
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

    })
    
})