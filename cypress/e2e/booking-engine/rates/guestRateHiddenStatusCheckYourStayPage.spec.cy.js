import TestFilters from "../../../support/filterTests";
import { ratesPage } from "../../../support/pageObjectModel/Pages/rates";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import accounts from "../../../fixtures/accounts";

const hotelSlug = accounts.cypressHotel4.slug

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Verify staff is able to disable a rate plan and rate plan is not displayed on the Your Stay page as guest', () => {

    before('Login as staff and create room type', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        //verify rate plan is displayed on your stay page as guest before disabling
        yourStayPage.open(hotelSlug)
        yourStayPage.clickSelectDates()
                    .selectCurrentDate()
                    .selectNextDayDate()
                    .clickSearchButton()
                    .clickAdultSubtractCounter();
        yourStayPage.clickOccupancySearchButton()

        cy.get(yourStayPage.selectors.roomBoxes).contains('Single Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
            cy.get(yourStayPage.selectors.baseRate).should('be.visible')
          })
        
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        ratesPage.open(hotelSlug)
        ratesPage.clickBaseRateEditPencil('sng')
        ratesPage.disableRate()
        cy.fn_logout()
    })

    it('Navigate to booking engine as guest and verify rate plan is not displayed on Your Stay page', () => {
        yourStayPage.open(hotelSlug)
        yourStayPage.clickSelectDates()
                    .selectCurrentDate()
                    .selectNextDayDate()
                    .clickSearchButton()
                    .clickAdultSubtractCounter();
        yourStayPage.clickOccupancySearchButton()

        cy.get(yourStayPage.selectors.roomBoxes).contains('Single Room').should('not.exist')
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})