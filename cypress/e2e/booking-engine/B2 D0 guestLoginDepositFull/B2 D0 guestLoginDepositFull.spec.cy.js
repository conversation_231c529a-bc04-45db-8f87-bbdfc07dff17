import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../support/pageObjectModel/Pages/thankYou";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../fixtures/guestDetails";
import { dateConverter } from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings";
import { dashboardPage } from "../../../support/pageObjectModel/Pages/dashboard";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const hotelSlug = accounts.cypressHotel6.slug
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)
const roomSelector = yourStayPage.selectors.twinRoomBox
const card = validJudoPayCards.frictionlessSuccess

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Create booking as logged in guest with full deposit', () => {

    before('log in as guest', () => {
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.fn_login('guest', accounts.cypressHotel6.guest.email, accounts.cypressHotel6.guest.password, accounts.cypressHotel6.slug)
    })

    it('Create booking', () => {
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
                .clickSearchButton()
    yourStayPage.clickOccupancySearchButton()
    
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    
    yourStayPage.clickContinueButton()
    extrasPage.clickContinueButton()

    //Verify guest cannot proceed to pay page without entering mandatory details 
    guestDetailsPage.clickContinueButton()
  
    cy.get(guestDetailsPage.selectors.firstNameField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    cy.get(guestDetailsPage.selectors.lastNameField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    cy.get(guestDetailsPage.selectors.emailField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    cy.get(guestDetailsPage.selectors.phoneNumberField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    cy.get(guestDetailsPage.selectors.postcodeField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);


    //enter mandatory guest details and continue to pay page
    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.clickEnterAddressManually()
    guestDetailsPage.fillInGuestAddressFields(guestDetails)
    guestDetailsPage.clickContinueButton()

    payPage.assertDepositPaymentMessage('200.00')
    paymentIframe.fillJudoPayDetails(card)
    cy.url({timeout: 10000}).should('contain', 'availability/confirm').then(() => {
      cy.contains(`Confirmation sent to: ${guestDetails.email}`).should('be.visible');
  });
    cy.get(thankYouPage.selectors.bookingReferenceConfirmation).should('contain', 'Confirmation number:' )
    thankYouPage.clickBillingBreakdownChevron()
    thankYouPage.assertGuestDetails(guestDetails)

    //post booking checks
    cy.get(thankYouPage.selectors.bookingReference).invoke('text').then(text => {
      const bookingRef = text
      cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

    //verify booking is displayed on the dashboard
    dashboardPage.open(hotelSlug)
                 .assertBookingListed(bookingRef)
    
    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug, date)
                .assertBookingExists(bookingRef)
            
    //verify booking is visible on the bookings page 
    bookingsPage.open(hotelSlug)
                .assertBookingListed(bookingRef)
     cy.contains(bookingRef).click()
    
     //confirm booking is captured and accurate on the booking hub
     bookingHubPage.assertPaymentStatus('paid')
                   .assertPaymentAmount(200.00)
                   .assertBalanceAmount(0.00)
                   .assertCorrectCreditCardAttached(card)
                   .assertGuestDetails(guestDetails)
                   .assertBookingDates(date, nextDayDate)

     bookingHubPage.clickExpandBookingButton()
     cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
     .then((text) => {
       const normalizedText = text.replace(/\s+/g, ' ').trim();
       expect(normalizedText).to.include('Base Rate : Twin Room')
                             .to.include('£200.00')
                             .to.include(`${currentDateFormatted} £200.00`)
     })
    
     cy.get(bookingHubPage.selectors.guestDetailsBox).should('contain', `${guestDetails.addressLineOne}`)
                                                     .should('contain', `${guestDetails.city}`)
                                                     .should('contain', `${guestDetails.county}`)
    
    cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )

    //Verify booking is displayed on the payment report 
    cy.fn_safeVisit(`/hotels/${hotelSlug}/reports/invoice/payment?date=${date}`)
    cy.contains(bookingRef).should('be.visible')
    })

    //Verify accurate rates grid availability 
    // gridPage.open(hotelSlug)
    //         .assertNoAvailability(gridPage.cypressHotel6Selectors.doubleRoomAvailabilityCurrentDay)    
  });   

  afterEach(function () {
    cy.fn_afterEachJira(this.currentTest)
  })

})

})

    