import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../support/pageObjectModel/Pages/thankYou";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";

import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../fixtures/guestDetails";
import { dateConverter } from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings";
import { dashboardPage } from "../../../support/pageObjectModel/Pages/dashboard";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const card = validJudoPayCards.frictionlessSuccess
const hotelSlug = accounts.cypressHotel6.slug
const roomSelector = yourStayPage.selectors.twinRoomBox
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Create booking as not logged in guest with full deposit', () => {

    it('Access booking engine as guest and create booking', () => {
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.clearCookies();
    cy.clearLocalStorage();
    yourStayPage.open(hotelSlug)
    //verify room counters are not visible by default
    cy.get(yourStayPage.selectors.roomCounters).should('not.exist')
    
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
    .clickSearchButton()
    yourStayPage.clickOccupancySearchButton()

    //Verify default '2 guests' and '2 Adults in 1 Room' is displayed
    cy.get(yourStayPage.selectors.occupancySelector).should('contain', '2 Guests')
    cy.get(yourStayPage.selectors.occupancyText).then(($subSelected) => {    
     cy.wrap($subSelected).contains('2 Adults ‐ 1 Room'); });
    
    //Search for occupancy with default 2 adults and verify all room types and rate plans are visible except single room
    cy.get(yourStayPage.selectors.singleRoomBox).should('not.exist')
    cy.get(yourStayPage.selectors.doubleRoomBox).should('be.visible')
      cy.get(yourStayPage.selectors.roomBoxes).contains('Double Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
        cy.get(yourStayPage.selectors.baseRate).should('be.visible')
        cy.get(yourStayPage.selectors.ratePlans).contains('Base Rate').parents(yourStayPage.selectors.ratePlans).within(() => {
            cy.get(yourStayPage.selectors.roomCounters).should('be.visible')
          }) 
      })
      
      cy.get(yourStayPage.selectors.familyRoomBox).should('be.visible')
      cy.get(yourStayPage.selectors.roomBoxes).contains('Family Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
        cy.get(yourStayPage.selectors.baseRate).should('be.visible')
        cy.get(yourStayPage.selectors.ratePlans).contains('Base Rate').parents(yourStayPage.selectors.ratePlans).within(() => {
            cy.get(yourStayPage.selectors.roomCounters).should('be.visible')
          }) 
      })

      cy.get(yourStayPage.selectors.luxuryRoomBox).should('be.visible')
      cy.get(yourStayPage.selectors.roomBoxes).contains('Luxury Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
        cy.get(yourStayPage.selectors.baseRate).should('be.visible')
        cy.get(yourStayPage.selectors.ratePlans).contains('Base Rate').parents(yourStayPage.selectors.ratePlans).within(() => {
            cy.get(yourStayPage.selectors.roomCounters).should('be.visible')
          }) 
      })

      cy.get(yourStayPage.selectors.twinRoomBox).should('be.visible')
      cy.get(yourStayPage.selectors.roomBoxes).contains('Twin Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
        cy.get(yourStayPage.selectors.baseRate).should('be.visible')
        cy.get(yourStayPage.selectors.ratePlans).contains('Base Rate').parents(yourStayPage.selectors.ratePlans).within(() => {
            cy.get(yourStayPage.selectors.roomCounters).should('be.visible')
        })
    
        })
    
    //add 'twin basic rate' rate plan
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    
    yourStayPage.clickContinueButton()
    extrasPage.clickContinueButton()

    //enter guest details
    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    //Verify full deposit message is displayed 
    payPage.assertDepositPaymentMessage('200.00')
    
    //enter valid card credentials and submit
    paymentIframe.fillJudoPayDetails(card)
    
    thankYouPage.assertURL()
    thankYouPage.assertPageHeader()
    
    //Verify thank you page booking confirmation details
    cy.contains(`Confirmation sent to: ${guestDetails.email}`).should('be.visible')
    cy.get(thankYouPage.selectors.bookingReferenceConfirmation).should('contain', 'Confirmation number:' )
    thankYouPage.clickBillingBreakdownChevron()
    thankYouPage.assertGuestDetails(guestDetails)

    //post booking checks
    cy.get(thankYouPage.selectors.bookingReference).invoke('text').then(text => {
      const bookingRef = text
      cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

    //verify booking is displayed on the dashboard
     cy.fn_safeVisit(`/hotels/${hotelSlug}?date=${date}`)
     dashboardPage.assertBookingListed(bookingRef)

    //verify booking is visible on the bookings page 
    bookingsPage.open(hotelSlug)
    bookingsPage.assertBookingListed(bookingRef)
     cy.contains(bookingRef).click()
    
     //confirm booking is captured and accurate on the booking hub
     bookingHubPage.assertPaymentStatus('paid')
     bookingHubPage.clickExpandBookingButton()
     cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
     .then((text) => {
       const normalizedText = text.replace(/\s+/g, ' ').trim();
       expect(normalizedText).to.include('Base Rate : Twin Room')
                             .to.include('£200.00')
                             .to.include(`${currentDateFormatted} £200.00`)
     })
     bookingHubPage.assertPaymentAmount(200.00)
                   .assertBalanceAmount(0.00)
                   .assertGuestDetails(guestDetails)
                   .assertBookingDates(date, nextDayDate)
    bookingHubPage.assertCorrectCreditCardAttached(card)
    
    cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )

    //Verify booking is displayed on the payment report 
    cy.fn_safeVisit(`/hotels/${hotelSlug}/reports/invoice/payment?date=${date}`)
    cy.contains(bookingRef).should('be.visible')

    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug)
    .assertBookingExists(bookingRef, guestDetails)
    })

    //Verify accurate rates grid availability 
    // gridPage.open(hotelSlug)
    //         .assertNoAvailability(gridPage.cypressHotel6Selectors.twinRoomAvailabilityCurrentDay)
    
    });

    afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
  })
  
})
})
    
