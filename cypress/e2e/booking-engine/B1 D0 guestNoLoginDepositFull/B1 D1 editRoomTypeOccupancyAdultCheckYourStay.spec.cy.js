import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { editRoomTypePage } from "../../../support/pageObjectModel/Pages/editRoomType";

const hotelSlug = accounts.cypressHotel4.slug

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Verify staff can successfully edit room type and adult occupancy and obtain correct search results', () => {
    before('log in as staff and edit family room adult occupancy to 1', () => {
    
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    cy.fn_safeVisit(`/hotels/${hotelSlug}/room-types/fam/edit`)
    cy.get(editRoomTypePage.selectors.maxAdultsField).clear()
                                                     .type('1')
    editRoomTypePage.clickSubmitButton()
    cy.fn_logout()
    cy.clearCookies();
    cy.clearLocalStorage();
    yourStayPage.open(hotelSlug)
    })

    it('verify family room is not displayed when searching with default occupancy of 2 adults as guest', () => {
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
                .clickSearchButton()
    yourStayPage.clickOccupancySearchButton()
    
    //Verify family room is not displayed 
    cy.get(yourStayPage.selectors.familyRoomBox).should('not.exist')
    })

    it('Verify family room is displayed when guest searches for availability with occupancy of 1 adult', () => {
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
                .clickSearchButton()
    //Edit occupancy to 1 adult 
    yourStayPage.clickAdultSubtractCounter()
    yourStayPage.clickOccupancySearchButton()

    //Verify double room is displayed 
    cy.get(yourStayPage.selectors.familyRoomBox).should('be.visible')
      cy.get(yourStayPage.selectors.roomBoxes).contains('Family Room').parents(yourStayPage.selectors.roomBoxes).within(() => {
        cy.get(yourStayPage.selectors.baseRate).should('be.visible')
        cy.get(yourStayPage.selectors.ratePlans).contains('Base Rate').parents(yourStayPage.selectors.ratePlans).within(() => {
            cy.get(yourStayPage.selectors.roomCounters).should('be.visible')
          }) 
      })
    })

  afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
  })

})
})
    
