import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";

import { guestDetails } from "../../../fixtures/guestDetails";

const hotelSlug = accounts.cypressHotel6.slug
const roomSelector = yourStayPage.selectors.familyRoomBox

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Verify guest is not able to continue to pay page without entering mandatory details', () => {
    before('log in as guest and navigate to guest details page', () => {

    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.clearCookies();
    cy.clearLocalStorage();

    cy.fn_login('guest', accounts.cypressHotel6.guest.email, accounts.cypressHotel6.guest.password, accounts.cypressHotel6.slug)
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
                .clickSearchButton()
    yourStayPage.clickOccupancySearchButton()
    
    //add family room base rate plan
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click()
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    yourStayPage.clickContinueButton()
    extrasPage.clickContinueButton()
    })

    beforeEach('refresh the page', () => {
        cy.reload()
    })

    it('Verify "Enter address manually" button is displayed ad clicking it displays address fields', () => {
        cy.get(guestDetailsPage.selectors.manualAddressEntryButton).should('be.visible')
                                                                   .click()
        
        cy.get(guestDetailsPage.selectors.yourAddressBox).within(() => {
            cy.get(guestDetailsPage.selectors.addressLineOneLabel).invoke('text').then((text) => {
              expect(text.trim()).to.include('Address Line 1')
                                 .to.include('(required)');
            });
            cy.get(guestDetailsPage.selectors.addressLineOneField).should('be.visible')
            cy.get(guestDetailsPage.selectors.addressLineTwoLabel).invoke('text').then((text) => {
                expect(text.trim()).to.include('Address Line 2')
                                   .to.not.include('(required)');

            });
            cy.get(guestDetailsPage.selectors.addressLineTwoField).should('be.visible')
            cy.get(guestDetailsPage.selectors.cityLabel).invoke('text').then((text) => {
                expect(text.trim()).to.include('City')
                                   .to.include('(required)');
              });
            cy.get(guestDetailsPage.selectors.cityField).should('be.visible')
            cy.get(guestDetailsPage.selectors.countyLabel).invoke('text').then((text) => {
                expect(text.trim()).to.include('County')
                                   .to.not.include('(required)');

            });
            //verify default country United Kingdom is displayed 
            cy.get(guestDetailsPage.selectors.countyField).should('be.visible')
            cy.get(guestDetailsPage.selectors.countryLabel).invoke('text').then((text) => {
                expect(text.trim()).to.include('Country')
                                   .to.include('(required)');
              });
            cy.get(guestDetailsPage.selectors.countryField).should('be.visible')
            cy.get(guestDetailsPage.selectors.manualPostcodeLabel).invoke('text').then((text) => {
                expect(text.trim()).to.include('Post code')
                                   .to.include('(required)');
              });
            cy.get(guestDetailsPage.selectors.manualPostcodeField).should('be.visible')
        })                                                         

    } )

    it('Verify guest cannot proceed to pay page without entering first name field ', () => {
    cy.get(guestDetailsPage.selectors.manualAddressEntryButton).click()
    //enter all mandatory details apart from first name field 
    cy.get(guestDetailsPage.selectors.lastNameField).type(guestDetails.lastName)
    cy.get(guestDetailsPage.selectors.emailField).type(guestDetails.email)
    cy.get(guestDetailsPage.selectors.phoneNumberField).type(guestDetails.mobileNumber)
    cy.get(guestDetailsPage.selectors.addressLineOneField).type(guestDetails.addressLineOne)
    cy.get(guestDetailsPage.selectors.cityField).type(guestDetails.city)
    cy.get(guestDetailsPage.selectors.manualPostcodeField).type(guestDetails.postcode)
    guestDetailsPage.clickContinueButton()
    
    cy.get(guestDetailsPage.selectors.firstNameField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    })
  
    it('Verify guest cannot proceed to pay page without entering last name', () => {
    cy.get(guestDetailsPage.selectors.manualAddressEntryButton).click()
    //enter all mandatory details apart from last name field 
    cy.get(guestDetailsPage.selectors.firstNameField).type(guestDetails.firstName)
    cy.get(guestDetailsPage.selectors.emailField).type(guestDetails.email)
    cy.get(guestDetailsPage.selectors.phoneNumberField).type(guestDetails.mobileNumber)
    cy.get(guestDetailsPage.selectors.addressLineOneField).type(guestDetails.addressLineOne)
    cy.get(guestDetailsPage.selectors.cityField).type(guestDetails.city)
    cy.get(guestDetailsPage.selectors.manualPostcodeField).type(guestDetails.postcode)
    guestDetailsPage.clickContinueButton()

    cy.get(guestDetailsPage.selectors.lastNameField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    })

    it('Verify guest cannot proceed to pay page without entering email field', () => {
    cy.get(guestDetailsPage.selectors.manualAddressEntryButton).click()
    //enter all mandatory details apart from email field
    cy.get(guestDetailsPage.selectors.firstNameField).type(guestDetails.firstName)
    cy.get(guestDetailsPage.selectors.lastNameField).type(guestDetails.lastName)
    cy.get(guestDetailsPage.selectors.phoneNumberField).type(guestDetails.mobileNumber)
    cy.get(guestDetailsPage.selectors.addressLineOneField).type(guestDetails.addressLineOne)
    cy.get(guestDetailsPage.selectors.cityField).type(guestDetails.city)
    cy.get(guestDetailsPage.selectors.manualPostcodeField).type(guestDetails.postcode)
    guestDetailsPage.clickContinueButton()

    cy.get(guestDetailsPage.selectors.emailField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    })

    it('Verify guest cannot proceed to pay page without entering phone number field', () => {
    cy.get(guestDetailsPage.selectors.manualAddressEntryButton).click()
    //enter all mandatory details apart from phone number field 
    cy.get(guestDetailsPage.selectors.firstNameField).type(guestDetails.firstName)
    cy.get(guestDetailsPage.selectors.lastNameField).type(guestDetails.lastName)
    cy.get(guestDetailsPage.selectors.emailField).type(guestDetails.email)
    cy.get(guestDetailsPage.selectors.addressLineOneField).type(guestDetails.addressLineOne)
    cy.get(guestDetailsPage.selectors.cityField).type(guestDetails.city)
    cy.get(guestDetailsPage.selectors.manualPostcodeField).type(guestDetails.postcode)   
    guestDetailsPage.clickContinueButton() 

    cy.get(guestDetailsPage.selectors.phoneNumberField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    })

    it('Verify guest cannot proceed to pay page without entering a postcode in the post code look up field ', () => { 
    cy.get(guestDetailsPage.selectors.firstNameField).type(guestDetails.firstName)
    cy.get(guestDetailsPage.selectors.lastNameField).type(guestDetails.lastName)
    cy.get(guestDetailsPage.selectors.emailField).type(guestDetails.email)
    cy.get(guestDetailsPage.selectors.phoneNumberField).type(guestDetails.mobileNumber)   
    guestDetailsPage.clickContinueButton() 

    cy.get(guestDetailsPage.selectors.postcodeField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    })

    it('Verify guest cannot proceed to pay page after searching for a postcode, not selecting address from the drop down and clicking continue', () => {
    cy.get(guestDetailsPage.selectors.firstNameField).type(guestDetails.firstName)
    cy.get(guestDetailsPage.selectors.lastNameField).type(guestDetails.lastName)
    cy.get(guestDetailsPage.selectors.emailField).type(guestDetails.email)
    cy.get(guestDetailsPage.selectors.phoneNumberField).type(guestDetails.mobileNumber)
    cy.get(guestDetailsPage.selectors.postcodeField).type(guestDetails.postcode)
    guestDetailsPage.selectPostcodeLookupButton()
    cy.get(guestDetailsPage.selectors.postcodeDropDownField).should('be.visible')
    guestDetailsPage.clickContinueButton() 

    cy.get(guestDetailsPage.selectors.addressLineOneField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    cy.get(guestDetailsPage.selectors.cityField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    // cy.get(guestDetailsPage.selectors.manualPostcodeField).should('contain', guestDetails.postcode) --todo
    })

    it('Verify guest cannot proceed to pay page without entering address line 1 field', () => {
    cy.get(guestDetailsPage.selectors.manualAddressEntryButton).click()
    //enter all mandatory details apart from address line 1 field 
    cy.get(guestDetailsPage.selectors.firstNameField).type(guestDetails.firstName)
    cy.get(guestDetailsPage.selectors.lastNameField).type(guestDetails.lastName)
    cy.get(guestDetailsPage.selectors.emailField).type(guestDetails.email)
    cy.get(guestDetailsPage.selectors.phoneNumberField).type(guestDetails.mobileNumber)
    cy.get(guestDetailsPage.selectors.cityField).type(guestDetails.city)
    cy.get(guestDetailsPage.selectors.manualPostcodeField).type(guestDetails.postcode)
    guestDetailsPage.clickContinueButton()

    cy.get(guestDetailsPage.selectors.addressLineOneField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    })

    it('Verify guest cannot proceed to pay page without entering text in city field', () => {
    cy.get(guestDetailsPage.selectors.manualAddressEntryButton).click()
    //enter all mandatory details apart from city field    
    cy.get(guestDetailsPage.selectors.firstNameField).type(guestDetails.firstName)
    cy.get(guestDetailsPage.selectors.lastNameField).type(guestDetails.lastName)
    cy.get(guestDetailsPage.selectors.emailField).type(guestDetails.email)
    cy.get(guestDetailsPage.selectors.phoneNumberField).type(guestDetails.mobileNumber)
    cy.get(guestDetailsPage.selectors.addressLineOneField).type(guestDetails.addressLineOne)
    cy.get(guestDetailsPage.selectors.manualPostcodeField).type(guestDetails.postcode)
    guestDetailsPage.clickContinueButton()

    cy.get(guestDetailsPage.selectors.cityField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    })

    it('verify country field has default text "United Kingdom"', () => {
    cy.get(guestDetailsPage.selectors.manualAddressEntryButton).click()
    //enter all mandatory details apart from country field 
    cy.get(guestDetailsPage.selectors.countryField).should('have.value', 'United Kingdom')
    })

    it('Verify guest cannot continue to pay page without entering text in manual postcode field', () => {
    cy.get(guestDetailsPage.selectors.manualAddressEntryButton).click()
    //enter all mandatory details apart from manual postcode field 
    cy.get(guestDetailsPage.selectors.firstNameField).type(guestDetails.firstName)
    cy.get(guestDetailsPage.selectors.lastNameField).type(guestDetails.lastName)
    cy.get(guestDetailsPage.selectors.emailField).type(guestDetails.email)
    cy.get(guestDetailsPage.selectors.phoneNumberField).type(guestDetails.mobileNumber)
    cy.get(guestDetailsPage.selectors.addressLineOneField).type(guestDetails.addressLineOne)
    cy.get(guestDetailsPage.selectors.cityField).type(guestDetails.city)
    guestDetailsPage.clickContinueButton()
    
    cy.get(guestDetailsPage.selectors.manualPostcodeField).closest(guestDetailsPage.selectors.allMandatoryFields).should('have.class', guestDetailsPage.selectors.mandatoryFieldsErrorClass);
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
    });
})
    