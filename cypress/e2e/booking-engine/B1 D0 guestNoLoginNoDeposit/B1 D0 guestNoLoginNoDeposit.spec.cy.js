import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../support/pageObjectModel/Pages/thankYou";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";

import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../fixtures/guestDetails";
import { dateConverter } from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { dashboardPage } from "../../../support/pageObjectModel/Pages/dashboard";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const hotelSlug = accounts.cypressHotel6.slug
const card = validJudoPayCards.frictionlessSuccess
const roomSelector = yourStayPage.selectors.familyRoomBox
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Create booking as not logged in guest with no deposit', () => {
    before('log in as staff', () => {
        
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

    cy.clearCookies();
    cy.clearLocalStorage();
     })
    

    it('Access booking engine as guest and create booking', () => {
    yourStayPage.open(hotelSlug)
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
                .clickSearchButton()
    yourStayPage.clickOccupancySearchButton()
    
    //add family room base rate plan
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click()
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    
    yourStayPage.clickContinueButton(); 
    extrasPage.clickContinueButton()
    
    guestDetailsPage.assertPageTitle()
    guestDetailsPage.assertPageHeader()


    //Verify Your Details box is displayed
    cy.get(guestDetailsPage.selectors.yourDetailsBox).should('be.visible')

    //assert your contact details box is displayed beneath your details box 
    cy.get(guestDetailsPage.selectors.yourDetailsBox).then(($elA) => {
        const yourDetails = $elA[0].getBoundingClientRect().top;
      
        cy.get(guestDetailsPage.selectors.yourContactDetailsBox).then(($elB) => {
          const yourContactDetails = $elB[0].getBoundingClientRect().top;
      
          expect(yourContactDetails).to.be.greaterThan(yourDetails);
        });
      });
    //assert your address box is displayed beneath your contact details box 
    cy.get(guestDetailsPage.selectors.yourContactDetailsBox).then(($elA) => {
        const yourContactDetails = $elA[0].getBoundingClientRect().top;
      
        cy.get(guestDetailsPage.selectors.yourAddressBox).then(($elB) => {
          const yourAddressDetails = $elB[0].getBoundingClientRect().top;
      
          expect(yourAddressDetails).to.be.greaterThan(yourContactDetails);
        });
      });

    //assert your stay box is displayed beneath the your address box 
    cy.get(guestDetailsPage.selectors.yourAddressBox).then(($elA) => {
        const yourAddressDetails = $elA[0].getBoundingClientRect().top;
      
        cy.get(guestDetailsPage.selectors.yourStayBox).then(($elB) => {
          const yourStayDetails = $elB[0].getBoundingClientRect().top;
      
          expect(yourStayDetails).to.be.greaterThan(yourAddressDetails);
        });
      });

    //assert who's checking in box is displayed beneath the your stay box 
    cy.get(guestDetailsPage.selectors.yourStayBox).then(($elA) => {
        const yourStayDetails = $elA[0].getBoundingClientRect().top;
      
        cy.get(guestDetailsPage.selectors.whosCheckingInBox).then(($elB) => {
          const whosCheckingIn = $elB[0].getBoundingClientRect().top;
      
          expect(whosCheckingIn).to.be.greaterThan(yourStayDetails);
        });
      });

    //enter guest details
    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    payPage.assertNoDepositMessage()
    paymentIframe.fillJudoPayDetails(card)

    thankYouPage.assertURL()
    thankYouPage.clickBillingBreakdownChevron()
    thankYouPage.assertGuestDetails(guestDetails)

    //post booking checks
    cy.get(thankYouPage.selectors.bookingReference).invoke('text').then(text => {
      const bookingRef = text
    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
  
    //verify booking is displayed on the dashboard
    cy.fn_safeVisit(`/hotels/${hotelSlug}?date=${date}`)
    dashboardPage.assertBookingListed(bookingRef)

    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug, date)
    .assertBookingExists(bookingRef)
  
    //verify booking is visible on the bookings page 
    bookingsPage.open(hotelSlug)
                .assertBookingListed(bookingRef)
    cy.contains(bookingRef).click()
      
    //confirm booking is captured and accurate on the booking hub
    bookingHubPage.assertPaymentStatus('pending')
                     .assertBalanceAmount(200.00)
                     .assertPaymentAmount(0.00)
                     .assertGuestDetails(guestDetails)
                     .assertBookingDates(date, nextDayDate)
    bookingHubPage.assertCorrectCreditCardAttached(card)
       bookingHubPage.clickExpandBookingButton()
       cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
       .then((text) => {
         const normalizedText = text.replace(/\s+/g, ' ').trim();
         expect(normalizedText).to.include('Base Rate : Family Room')
                               .to.include('£200.00')
                               .to.include(`${currentDateFormatted} £200.00`)
      })
      
      cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )
        
      })
  
      //Verify accurate rates grid availability 
      gridPage.open(hotelSlug)
              .assertNoAvailability(gridPage.cypressHotel6Selectors.familyRoomAvailabilityCurrentDay)
   });   

  afterEach(function () {
    cy.fn_afterEachJira(this.currentTest)
  })

  })
})   
    