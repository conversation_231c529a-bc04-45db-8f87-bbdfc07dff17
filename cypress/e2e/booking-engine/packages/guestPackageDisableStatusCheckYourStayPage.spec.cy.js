import TestFilters from '../../../support/filterTests';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {editPackagePage} from '../../../support/pageObjectModel/Pages/packages/edit';

const hotelSlug = accounts.cypressHotel11.slug
const packageCode = 'bed-and-breakfast';
const packageTitle = 'Bed and Breakfast';
// Skipped due to FD-5060: Package is enabled when it should be disabled
// Re-enable this test after the issue is resolved and verified.

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe.skip('Verify a disabled package plan cannot be booked as not logged in guest', () => {

    it('Access booking engine as not logged in guest and verify a disabled package is absent', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

        editPackagePage
            .open(hotelSlug, packageCode);
        cy.get(editPackagePage.selectors.statusSelect).find(editPackagePage.selectors.selectedDropdownOption).should('have.text', 'Disabled')
        cy.fn_logout()

        //assert package is not displayed on your stay page
        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
        yourStayPage.assertRatePlanNotListed(packageTitle)

    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})