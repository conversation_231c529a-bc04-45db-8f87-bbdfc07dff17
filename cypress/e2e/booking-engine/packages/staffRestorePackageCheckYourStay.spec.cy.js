import TestFilters from '../../../support/filterTests';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {trashPage} from '../../../support/pageObjectModel/Pages/trash';

const hotelSlug = accounts.cypressHotel3.slug
const packageCode = 'breakfast-only'
const packageTitle = 'Breakfast Only'

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Check if restored package appears on the availability as hotelier', () => {

    before('Verify deleted package is not displayed for staff before restoration', () => {
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));
        yourStayPage
        .open(hotelSlug)
        .clickSelectDates()
        .selectNextDayDate()
        .selectTwoDaysAheadDate()
        .clickSearchButton()
        .clickOccupancySearchButton()
        // TODO? Wait until request finishes?
        yourStayPage.assertRatePlanNotListed(packageTitle)
    })

    it('Restore package, access booking engine as staff and verify package is displayed', () => {

        trashPage
            .open(hotelSlug)
            .restorePackage(packageCode)

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanListed(packageTitle)
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})