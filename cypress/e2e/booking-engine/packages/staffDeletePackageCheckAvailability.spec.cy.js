import TestFilters from '../../../support/filterTests';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {packagesPage} from '../../../support/pageObjectModel/Pages/packages/list';

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

const hotelSlug = accounts.cypressHotel11.slug
const packageCode = 'breakfast-only'
const packageTitle = 'Breakfast Only'

describe('Check if deleted package disappears from the availability as staff', () => {

    before('Access booking engine as staff and verify package is displayed before deletion', () => {
        
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanListed(packageTitle)
    })

    it('Access booking engine as staff and search for package-based booking', () => {

        packagesPage
            .open(hotelSlug)
            .delete(packageCode)

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanNotListed(packageTitle)
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})