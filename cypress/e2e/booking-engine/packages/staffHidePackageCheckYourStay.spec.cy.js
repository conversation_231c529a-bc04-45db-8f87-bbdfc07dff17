import TestFilters from '../../../support/filterTests';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {editPackagePage} from '../../../support/pageObjectModel/Pages/packages/edit';

// Skipped due to FD-3560: Package is enabled when it should be disabled
// Re-enable this test after the issue is resolved and verified.

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Verify a hidden package plan can be viewd as staff', () => {

    it.skip('Access booking engine as staff and verify a hidden package is present', () => {
        const hotelSlug = accounts.cardiff.slug;
        const packageCode = 'Package123';
        const packageTitle = packageCode;

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        //assert package is in hidden state
        editPackagePage.open(hotelSlug, packageCode)
        cy.get(editPackagePage.selectors.visibilitySelect).find(editPackagePage.selectors.selectedDropdownOption).should('have.text', 'Hidden - Staff Only')

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton();
        yourStayPage.assertRatePlanListed(packageTitle)
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})