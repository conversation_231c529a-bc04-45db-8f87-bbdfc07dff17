import TestFilters from '../../../support/filterTests';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {trashPage} from '../../../support/pageObjectModel/Pages/trash';

const hotelSlug = accounts.cypressHotel3.slug
const packageCode = 'breakfast-only'
const packageTitle = 'Breakfast Only'

const filters = ['Booking-Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Check if restored package appears on the availability as guest', () => {

    before('Verify deleted package is not displayed for guests before restoration', () => {
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        yourStayPage
        .open(hotelSlug)
        .clickSelectDates()
        .selectNextDayDate()
        .selectTwoDaysAheadDate()
        .clickSearchButton()
        .clickOccupancySearchButton()
        // TODO? Wait until request finishes?
        yourStayPage.assertRatePlanNotListed(packageTitle)
    })

    it('Restore package, access booking engine as guest and verify package is displayed', () => {
        
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));
        trashPage
            .open(hotelSlug)
            .restorePackage(packageCode)
        cy.fn_logout()

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanListed(packageTitle)
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})

})