import TestFilters from '../../../support/filterTests';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {packagesPage} from '../../../support/pageObjectModel/Pages/packages/list';
import { pmsGeneral } from '../../../support/pageObjectModel/Components/pmsGeneral';

const hotelSlug = accounts.cypressHotel11.slug
const packageCode = 'breakfast-only'
const packageTitle = 'Breakfast Only'

const filters = ['Booking Engine'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

describe('Check if deleted package disappears from the availability as guest', () => {

    before('Access booking engine as guest and verify package is displayed before deletion', () => {
        cy.clearCookies();
        cy.clearLocalStorage();

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanListed(packageTitle)
    })

    it('Delete package and verify package is not displayed on Your Stay page as guest', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));
        packagesPage
            .open(hotelSlug)
            .delete(packageCode)
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', `${packageTitle} package deleted`)
        cy.fn_logout()

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanNotListed(packageTitle)
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})