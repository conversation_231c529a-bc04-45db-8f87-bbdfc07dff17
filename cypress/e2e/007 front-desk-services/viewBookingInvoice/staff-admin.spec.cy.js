import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Front Desk: View Booking Invoice as Staff Admin', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can view booking invoice', () => {
            cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/bookings')
            cy.get(':nth-child(1) > .sorting_1 > a').click();
            cy.get('.box.breakdown > .box__details > .actions > .print > .button')
                .invoke('removeAttr','target')
                .click();
            cy.get('body').should('contain.text', 'Guest Invoice')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
