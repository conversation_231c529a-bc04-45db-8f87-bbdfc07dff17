import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Front Desk: View Dashboard as Hotelier Manager', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can view dashboard', () => {
            cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug)
            cy.get('body').should('contain.text', 'Actions')
            cy.get('body').should('contain.text', 'Show products due')
            cy.get('body').should('contain.text', 'Show Availability')
            cy.contains('a', 'View Activity Report').should('exist')
            cy.contains('a', 'View Housekeeping').should('exist')
            cy.contains('a', 'Print Registration Forms').should('exist')
            cy.contains('a', 'Print Invoices').should('exist')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
