import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Front Desk: View Dashboard as a Corporation', () => {

        it('can log in', () => {
            cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
            cy.contains('button', 'Login').should('not.exist')
            cy.contains('a.button.default', 'My Account').should('be.visible')
        })

        it('cannot access dashboard', () => {
            cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug)
            cy.get('body').should('contain.text', 'Check availability')
            cy.contains('button', 'Login').should('be.visible')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
