import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic

    describe('Front Desk: View Dashboard as Terminal User', () => {

        it('cannot access dashboard', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
            cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug)
            cy.get('body').should('contain.text', 'Check availability')
            cy.contains('button', 'Login').should('be.visible')
            // If a Terminal User can see the 'Login' then they are accessing this page without any logged-in privileges
            // so further tests will be as per 'public.spec.js'
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
