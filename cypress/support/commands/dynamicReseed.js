// cypress/support/commands/dynamicReseed.js

/**
 * A dynamic reseeding command that uses the current baseUrl
 * This ensures that the reseeding URL is always correct, even when the ngrok URL changes
 */

/**
 * Reseed a hotel using the current baseUrl
 * @param {string} hotelSlug - The slug of the hotel to reseed
 * @param {Object} options - Additional options for the request
 * @returns {Cypress.Chainable} - The Cypress chainable
 */
Cypress.Commands.add('dynamicReseed', (hotelSlug, options = {}) => {
  // Get the current baseUrl from Cypress config
  const baseUrl = Cypress.config('baseUrl');
  
  // Combine with the default options
  const requestOptions = {
    url: `automation/tests/reseedHotel/${hotelSlug}`,
    failOnStatusCode: false,
    timeout: 180000,
    ...options
  };
  
  // Log the reseeding attempt
  cy.log(`Reseeding hotel ${hotelSlug} using baseUrl: ${baseUrl}`);
  
  // Make the request
  return cy.request(requestOptions);
});
