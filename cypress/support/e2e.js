/*Cypress.on('uncaught:exception', (err, runnable) => {
    // returning false here prevents <PERSON><PERSON> from
    // failing the test on JS errors in the PMS
    return false
})*/

import './helpers'
import './seed'

// COMMANDS
import './commands/cancelBookings'
import './commands/getIFrame'
import './commands/overwrites'
import 'cypress-iframe'
import './commands/jira'

// Add ngrok support for automation requests
Cypress.Commands.add('requestWithNgrokHeaders', (url, options = {}) => {
  const defaultOptions = {
    headers: {
      'ngrok-skip-browser-warning': 'true',
      'User-Agent': 'Cypress',
      ...options.headers
    },
    failOnStatusCode: false,
    ...options
  };

  return cy.request(defaultOptions.method || 'GET', url, defaultOptions);
});

// Override cy.request for automation routes when using ngrok
Cypress.Commands.overwrite('request', (originalFn, ...args) => {
  let url, options = {};

  if (typeof args[0] === 'string') {
    url = args[0];
    options = args[1] || {};
  } else {
    options = args[0] || {};
    url = options.url;
  }

  // Check if this is an automation route and we're using ngrok
  if (url && url.includes('automation/tests') && Cypress.config('baseUrl').includes('ngrok')) {
    const ngrokOptions = {
      ...options,
      headers: {
        'ngrok-skip-browser-warning': 'true',
        'User-Agent': 'Cypress',
        ...options.headers
      }
    };

    if (typeof args[0] === 'string') {
      return originalFn(url, ngrokOptions);
    } else {
      return originalFn(ngrokOptions);
    }
  }

  return originalFn(...args);
});

// FORMS
import './forms/fillAvailabilityForm'
import './forms/fillBaseRatesForm'
import './forms/fillBookingEngineGuestDetailsForm'
import './forms/fillCalendarModal'
import './forms/fillChangePasswordForm'
import './forms/fillCreateCategoryForm'
import './forms/fillCreateChannelForm'
import './forms/fillCreateCorporationForm'
import './forms/fillCreateGatewayForm'
import './forms/fillCreateGroupForm'
import './forms/fillCreateGuestForm'
import './forms/fillCreateHotelForm'
import './forms/fillCreateHotelierForm'
import './forms/fillCreateOutletForm'
import './forms/fillCreatePackageForm'
import './forms/fillCreateProductForm'
import './forms/fillCreateRateForm'
import './forms/fillCreateRateHurdleForm'
import './forms/fillCreateRoomForm'
import './forms/fillCreateRoomTypeForm'
import './forms/fillCreateVenueForm'
import './forms/fillCreateVoucherForm'
import './forms/fillDeleteForm'
import './forms/fillDerivationForm'
import './forms/fillLoginForm'
import './forms/fillMultiCheckboxByLabels'
import './forms/fillRateInclusionForm'
import './forms/fillRateRestrictionCheckbox'
import './forms/fillRateRestrictionInput'
import './forms/fillRatesBulkUpdateForm'
import './forms/fillRatesPerDateForm'
import './forms/fillReactDaysOfWeek'
import './forms/fillReactDropdown'
import './forms/fillRoomTypeRate'
import './forms/fillSearchForm'
import './forms/fillUpdateBooking'
import './forms/fillUpdateBookingStatus'
import './forms/fillUpdateGatewayForm'
import './forms/fillUpdateHotelForm'
import './forms/fillUpdateHotelierForm'
import './forms/fillUpdatePackageForm'
import './forms/fillUpdateRateForm'
import './forms/fillUpdateReservationForm'
import './forms/fillUpdateVoucherForm'
import './forms/findCheckboxByLabel'
import './forms/findCheckboxByName'
import './forms/selectCheckbox'
import './forms/selectContaining'
import './forms/selectPrimaryGateway'
import './forms/selectRoom'
import './forms/selectReactCheckbox'
import './forms/fillReactDropdown'
import './forms/selectReactDropdown'
import './forms/selectReactDropdownOption'
import './forms/selectEditPencil'
import './forms/selectTrashCan'
import './forms/submitChangeOfDate'
import './forms/submitDiscountCodeForm'

// FUNCTIONS
import './functions/login'
import './functions/safeVisit'
import './functions/rand'
import './functions/afterEachJira'
import './functions/createString'

const mysql = require('cypress-mysql');
mysql.addCommands();