# PocketFlow GitHub Integration Guide

## Introduction

PocketFlow Tutorial Generator supports direct integration with GitHub repositories, allowing you to generate comprehensive tutorials for any public GitHub repository without having to clone it manually. This guide explains how to use this feature effectively.

> **Key Insight**: Our experiments show that removing the `--max-abstractions` limit produces significantly more comprehensive tutorials. While the default limit of 10 abstractions works well for quick overviews, unlimited abstractions provide deeper, more detailed documentation with more granular breakdowns of concepts.

## Table of Contents

1. [Basic Usage](#basic-usage)
2. [Advanced Options](#advanced-options)
3. [Authentication for Private Repositories](#authentication-for-private-repositories)
4. [Optimizing for Large Repositories](#optimizing-for-large-repositories)
5. [Focusing on Specific Components](#focusing-on-specific-components)
6. [Combining with Repository Scanning](#combining-with-repository-scanning)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Basic Usage

To generate a tutorial for a GitHub repository, use the `--repo` argument followed by the repository URL:

```bash
python main.py --repo https://github.com/username/repository
```

This will:
1. Clone the repository to a temporary directory
2. Analyze the codebase
3. Generate a tutorial in the `output` directory

## Advanced Options

### Filtering Files

You can specify which files to include or exclude using the `--include` and `--exclude` arguments:

```bash
python main.py --repo https://github.com/username/repository \
  --include "*.js" "*.ts" "*.jsx" "*.tsx" \
  --exclude "node_modules/*" "tests/*" "*.test.js"
```

### Limiting File Size

To avoid processing very large files, use the `--max-size` argument:

```bash
python main.py --repo https://github.com/username/repository \
  --max-size 50000
```

This will skip files larger than 50KB.

### Controlling the Number of Abstractions

To limit the number of abstractions (chapters) in the tutorial:

```bash
python main.py --repo https://github.com/username/repository \
  --max-abstractions 5
```

### Specifying Output Directory

To change the output directory:

```bash
python main.py --repo https://github.com/username/repository \
  --output my-tutorials
```

### Disabling Caching

To disable LLM response caching:

```bash
python main.py --repo https://github.com/username/repository \
  --no-cache
```

## Authentication for Private Repositories

To access private repositories, you need to provide a GitHub personal access token:

```bash
python main.py --repo https://github.com/username/private-repository \
  --token YOUR_GITHUB_TOKEN
```

Alternatively, you can set the `GITHUB_TOKEN` environment variable:

```bash
export GITHUB_TOKEN=YOUR_GITHUB_TOKEN
python main.py --repo https://github.com/username/private-repository
```

## Optimizing for Large Repositories

Large repositories can be challenging to process. Here are some strategies:

### 1. Focus on Specific Directories

Instead of processing the entire repository, focus on specific directories:

```bash
# Clone the repository manually
git clone https://github.com/username/large-repository
cd large-repository

# Generate tutorial for a specific directory
python /path/to/pocketflow/main.py --dir ./src/core \
  --include "*.js" "*.ts" \
  --max-size 50000
```

### 2. Process in Batches

Process different parts of the repository in separate runs:

```bash
# First batch: Core components
python main.py --repo https://github.com/username/repository \
  --include "src/core/*" \
  --max-abstractions 3

# Second batch: UI components
python main.py --repo https://github.com/username/repository \
  --include "src/ui/*" \
  --max-abstractions 3
```

### 3. Increase Cache Size

If you're running into memory issues, try increasing the cache size:

```bash
# Set environment variable for larger cache
export POCKETFLOW_CACHE_SIZE=1000

# Run PocketFlow
python main.py --repo https://github.com/username/repository
```

### 4. Unlimited Abstractions with Focused Scope

For comprehensive documentation of a large repository, combine unlimited abstractions with a focused file scope:

```bash
# Generate comprehensive documentation for a specific component
python main.py --repo https://github.com/username/large-repository \
  --include "src/payment-processing/**/*.js" \
  --exclude "**/*.test.js" "**/*.spec.js" \
  --max-size 100000
```

This approach removes the abstraction limit while keeping the file scope narrow enough to be manageable.

## Focusing on Specific Components

For test automation frameworks like Cypress, you might want to focus on specific components:

### Example: Cypress Page Objects

```bash
# Quick overview with limited abstractions
python main.py --repo https://github.com/username/test-automation \
  --include "cypress/support/pageObjectModel/**/*.js" \
  --exclude "node_modules/*" \
  --max-abstractions 3

# Comprehensive documentation without abstraction limits
python main.py --repo https://github.com/username/test-automation \
  --include "cypress/support/pageObjectModel/**/*.js" \
  --exclude "node_modules/*" \
  --max-size 100000
```

### Example: Payment Processing Components

```bash
# Quick overview with limited abstractions
python main.py --repo https://github.com/username/test-automation \
  --include "**/*payment*.js" "**/*card*.js" "**/*checkout*.js" \
  --exclude "node_modules/*" \
  --max-abstractions 3

# Comprehensive documentation without abstraction limits
python main.py --repo https://github.com/username/test-automation \
  --include "**/*payment*.js" "**/*card*.js" "**/*checkout*.js" "**/*gateway*.js" "**/*3ds*.js" "**/*iframe*.js" \
  --exclude "node_modules/*" \
  --max-size 100000
```

### Example: Barclays Payment Gateway Specifically

```bash
python main.py --repo https://github.com/username/test-automation \
  --include "**/*barclays*.js" "**/*payment*/*barclays*.js" "**/*gateway*/*barclays*.js" \
  --exclude "node_modules/*" "**/*.test.js" "**/*.spec.js" \
  --max-size 100000
```

### Example: 3D Secure Authentication

```bash
python main.py --repo https://github.com/username/test-automation \
  --include "**/*3d*.js" "**/*3ds*.js" "**/*secure*.js" "**/*challenge*.js" "**/*authentication*.js" \
  --exclude "node_modules/*" "**/*.test.js" "**/*.spec.js" \
  --max-size 100000
```

## Combining with Repository Scanning

For a more comprehensive analysis, combine PocketFlow with custom repository scanning:

1. **First, scan the repository** to identify key components:

```bash
# Clone the repository
git clone https://github.com/username/repository
cd repository

# Run custom scanner
python /path/to/repo_scanner.py --dir . \
  --include "*.js" "*.ts" \
  --exclude "node_modules/*" \
  --output repo_scan_results.json \
  --summary repo_scan_summary.md
```

2. **Then, use PocketFlow** to generate tutorials for the key components identified in the scan:

```bash
# Generate tutorial for the most important component
python /path/to/pocketflow/main.py --dir ./src/important-component \
  --include "*.js" "*.ts" \
  --max-abstractions 3
```

3. **Finally, combine the insights** from both tools to create a comprehensive overview.

## Best Practices

1. **Start Small**: Begin with a small, focused part of the repository
2. **Use Specific Include Patterns**: Target specific file types and directories
3. **Exclude Irrelevant Files**: Skip tests, build artifacts, and other non-essential files
4. **Consider Abstraction Limits**:
   - For quick results or initial exploration, limit abstractions with `--max-abstractions 3-5`
   - For comprehensive documentation, remove the abstraction limit entirely
5. **Combine Approaches**: Use both PocketFlow and custom scanning for comprehensive analysis
6. **Iterate**: Generate tutorials for different parts of the repository and combine the insights
7. **Experiment with Different Configurations**: Try with and without abstraction limits to see which produces better results for your specific codebase

## Troubleshooting

### Common Issues

1. **Rate Limiting**: If you hit GitHub API rate limits, use a personal access token
2. **Memory Issues**: For large repositories, focus on specific directories or increase cache size
3. **Timeout Issues**: If the API calls timeout, try processing smaller chunks of the repository
4. **Incomplete Tutorials**: If the tutorial generation is interrupted, check the output directory for partial results

### Error: "Repository not found"

Make sure:
- The repository URL is correct
- The repository is public or you've provided a valid token
- You have the necessary permissions to access the repository

### Error: "Token authentication failed"

Make sure:
- Your token has the necessary permissions (at least `repo` scope)
- The token is valid and not expired
- You're using the correct token format

## Conclusion

The GitHub integration feature of PocketFlow makes it easy to generate tutorials for any public repository. By using the strategies outlined in this guide, you can effectively analyze even large and complex repositories to create comprehensive, beginner-friendly tutorials.
