const { defineConfig } = require('cypress');
const seed = require('./cypress/support/seed');
const mysql = require('cypress-mysql');

module.exports = defineConfig({
  e2e: {
  //  retries: 2,
    viewportWidth: 1200,
    viewportHeight: 800,

    // Prevent browser context being lost between tests
    testIsolation: false,

    // Enables the 'Run All' option in the GUI
    experimentalRunAllSpecs: true,

    // Required for the `before:run` option, used for seeding
    experimentalInteractiveRunEvents: true,

    // Prevent it kicking off every time something changes...
    watchForFileChanges: false,

    // Paths.
    specPattern:        'cypress/e2e/**/*.cy.js',
    supportFile:        'cypress/support/e2e.js',
    // 'pluginsFile':     'tests/cypress/plugins/index.js',

    // Directory paths
    downloadsFolder:    'cypress/downloads',

    // Enable cross-domain iframe access
    chromeWebSecurity: false,

    // Skip ngrok warning in localhost
    userAgent: 'Cypress',

    'env': {
      'db': {
        'host': 'localhost',
        'port': '3000',
        'user': 'root',
        'password': 'secret',
        'database': 'local_pms'
      }
    },

    defaultCommandTimeout: 90000,
    requestTimeout: 90000,
    responseTimeout: 90000,
    // 'fileServerFolder':     'tests/cypress',
    // 'fixturesFolder':       'tests/cypress/fixtures',
    // 'integrationFolder':    'tests/cypress/integration',
    // 'screenshotsFolder':    'tests/cypress/screenshots',
    // 'videosFolder':         'tests/cypress/videos',

    // Options
    // "modifyObstructiveCode": false,
    // "slowTestThreshold": 4000,
    // "requestTimeout": 20000,
    // "__retries": 2,

    setupNodeEvents(on, config) {
      mysql.configurePlugin(on);
      // Seed DB with `Cypress Hotel`
      /*on('before:run', (config) => {
          console.log('Cypress hotels are being seeded')
          return seed(config).then((response) => {
              console.log('Cypress hotels have been seeded')
          }).catch((error) => {
              console.log('Cypress hotels failed to seed', {error})
              throw new Error('Failed to seed cypress data')
          });
      });*/

      // Get & Set Config from Environment file
      const environmentName = config.env.environmentName || 'local'
      const environmentFilename = `./cypress/env/${environmentName}.json`
      const settings = require(environmentFilename)
      if (settings.baseUrl) {
        config.baseUrl = settings.baseUrl
      }
      if (settings.env) {
        config.env = {
          ...config.env,
          ...settings.env,
        }
      }
      return config
    },
  },
});
