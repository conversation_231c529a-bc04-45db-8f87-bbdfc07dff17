# PocketFlow Tutorial Generator Guide

## Introduction

This guide explains how to use the PocketFlow Tutorial Generator to create comprehensive, beginner-friendly tutorials for codebases. PocketFlow uses AI to analyze a codebase and generate structured tutorials that explain the key concepts and components.

## Table of Contents

1. [Installation](#installation)
2. [Basic Usage](#basic-usage)
3. [Advanced Options](#advanced-options)
4. [Generating Tutorials for the Test-Automation Repository](#generating-tutorials-for-the-test-automation-repository)
5. [Understanding the Output](#understanding-the-output)
6. [Troubleshooting](#troubleshooting)

## Installation

To install and set up the PocketFlow Tutorial Generator:

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/PocketFlow-Tutorial-Codebase-Knowledge.git
   cd PocketFlow-Tutorial-Codebase-Knowledge
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up your API key:
   ```bash
   export OPENAI_API_KEY=your_api_key_here
   # or
   export ANTHROPIC_API_KEY=your_api_key_here
   # or
   export GEMINI_API_KEY=your_api_key_here
   ```

## Basic Usage

The basic command to generate a tutorial is:

```bash
python main.py --dir /path/to/codebase --include "*.js" "*.py" --exclude "node_modules/*" "*.test.js"
```

This will:
1. Analyze the codebase at `/path/to/codebase`
2. Include only JavaScript and Python files
3. Exclude files in the `node_modules` directory and test files
4. Generate a tutorial in the `output` directory

## Advanced Options

PocketFlow offers several advanced options to customize the tutorial generation:

### Limiting File Size

To limit the maximum size of files to analyze:

```bash
python main.py --dir /path/to/codebase --max-size 50000
```

This will only analyze files smaller than 50KB.

### Controlling the Number of Abstractions

To limit the number of abstractions (chapters) in the tutorial:

```bash
python main.py --dir /path/to/codebase --max-abstractions 5
```

This will generate a tutorial with at most 5 chapters.

### Focusing on Specific Directories

To focus on specific directories or files:

```bash
python main.py --dir /path/to/codebase/specific/subdirectory
```

### Choosing the AI Model

To specify which AI model to use:

```bash
python main.py --dir /path/to/codebase --model gemini
```

Available options include:
- `openai` (default): Uses OpenAI's GPT models
- `anthropic`: Uses Anthropic's Claude models
- `gemini`: Uses Google's Gemini models

## Generating Tutorials for the Test-Automation Repository

To generate a tutorial for the Cypress part of the test-automation repository:

```bash
python main.py --dir /var/www/test-automation/cypress --include "*.js" "*.json" --exclude "node_modules/*" --max-size 50000
```

For a more focused tutorial on just the payment components:

```bash
python main.py --dir /var/www/test-automation/cypress/support/pageObjectModel/Components --include "paymentIFrame.js" --max-size 20000
```

For a tutorial on the page objects:

```bash
python main.py --dir /var/www/test-automation/cypress/support/pageObjectModel/Pages --include "*Page.js" --max-size 20000
```

## Troubleshooting

### Memory Issues

If you encounter memory issues when processing large codebases:

1. Reduce the scope with `--include` and `--exclude` options
2. Limit file size with `--max-size`
3. Focus on specific directories
4. Limit the number of abstractions with `--max-abstractions`

### Timeout Issues

If the API calls timeout:

1. Increase the timeout value:
   ```bash
   python main.py --dir /path/to/codebase --timeout 120
   ```

2. Process smaller chunks of the codebase at a time

### Incomplete Tutorials

If the tutorial generation is interrupted or incomplete:

1. Check the `output` directory for partial results
2. Focus on generating one chapter at a time:
   ```bash
   python main.py --dir /path/to/codebase --max-abstractions 1
   ```

3. Increase the model's context window if available:
   ```bash
   python main.py --dir /path/to/codebase --model anthropic --context-window 100000
   ```

## Understanding the Output

When PocketFlow successfully generates a tutorial, it creates a structured set of files in the `output` directory. Here's what you'll find:

### Output Directory Structure

```
output/
└── [Module Name]/
    ├── index.md                  # Main index file with overview and chapter links
    ├── 01_[concept_name].md      # Chapter 1
    ├── 02_[concept_name].md      # Chapter 2
    ├── 03_[concept_name].md      # Chapter 3
    └── ...                       # Additional chapters
```

### Main Index File

The `index.md` file contains:

1. **Overview**: A concise summary of the module's purpose and key concepts
2. **Mermaid Diagram**: A visual representation of how concepts relate to each other
3. **Chapter List**: Links to all the chapter files

Example from a Components module tutorial:

```markdown
# Tutorial: Components

This project provides a structured way to write automated tests for a website's user interface.
It breaks down the website into reusable **UI Component Objects**, like virtual remote controls
for different sections (e.g., header, shopping basket)...

## Chapters

1. [UI Component Objects](01_ui_component_objects_.md)
2. [Selector-Driven Interactions](02_selector_driven_interactions_.md)
3. [Modal Dialog Handlers](03_modal_dialog_handlers_.md)
```

### Chapter Files

Each chapter file (`01_[concept_name].md`, etc.) contains:

1. **Concept Introduction**: Explains what the concept is and why it's important
2. **Beginner-Friendly Analogies**: Uses real-world comparisons to make concepts easier to understand
3. **Code Examples**: Shows how the concept is implemented in the codebase
4. **Best Practices**: Highlights recommended approaches for using the concept

The chapters use a conversational, tutorial-style approach with clear explanations suitable for beginners.

## Conclusion

The PocketFlow Tutorial Generator is a powerful tool for creating comprehensive tutorials for codebases. By using the options described in this guide, you can customize the tutorial generation process to suit your needs and overcome any limitations you might encounter.

For more information, refer to the [PocketFlow GitHub repository](https://github.com/yourusername/PocketFlow-Tutorial-Codebase-Knowledge).
