# Payment Gateway Testing Tutorial

## Introduction

This tutorial provides a comprehensive guide to testing payment gateways in the Cypress test automation framework. It focuses on handling complex payment scenarios, including iframe interactions and 3D Secure authentication challenges.

## Table of Contents

1. [Payment Component Architecture](#payment-component-architecture)
2. [Payment Gateway Support](#payment-gateway-support)
3. [Handling Iframes](#handling-iframes)
4. [3D Secure Authentication](#3d-secure-authentication)
5. [Testing Payment Success and Failure](#testing-payment-success-and-failure)
6. [Best Practices](#best-practices)

## Payment Component Architecture

The payment testing framework consists of three main components:

1. **Payment Page Objects**: These represent the payment pages in the application
   - `payByLinkPage.js`: Handles the Pay by Link payment page
   - `pay.js`: Handles the standard payment page during booking
   - `paymentRequestForm.js`: Handles the payment request form

2. **Payment Iframe Component**: This handles interactions with payment gateway iframes
   - `paymentIFrame.js`: Contains methods for interacting with different payment gateway iframes

3. **Test Data**: Contains test card data for different payment scenarios
   - `cards/paymentDetailsBarclays.js`: Test cards for Barclays gateway
   - `cards/paymentDetailsJudoPay.js`: Test cards for JudoPay gateway

## Payment Gateway Support

The framework supports multiple payment gateways:

### Barclays Gateway

```javascript
// Example from paymentIFrame.js
fillBarclaysDetails: (card) => {
    // Wait for the iframe to be fully loaded
    cy.wait(3000);
    
    // Handle nested iframe scenario
    cy.get('iframe#card-capture-for-cardUuid').then($outerIframe => {
        cy.iframe('#card-capture-for-cardUuid').then($outerIframeBody => {
            // Check if there's a nested iframe with ID 'barclays-iframe'
            const nestedIframe = $outerIframeBody.find('#barclays-iframe');
            
            if (nestedIframe.length > 0) {
                // Use jQuery to access and fill the nested iframe
                cy.window().then(win => {
                    const $nestedIframe = win.jQuery('#barclays-iframe');
                    
                    // Fill in the form fields
                    const $nameField = win.jQuery($nestedIframe[0]).contents().find('#cardholderName');
                    const $cardField = win.jQuery($nestedIframe[0]).contents().find('#cardNumber');
                    const $monthSelect = win.jQuery($nestedIframe[0]).contents().find('#expiryMonth');
                    const $yearSelect = win.jQuery($nestedIframe[0]).contents().find('#expiryYear');
                    const $securityField = win.jQuery($nestedIframe[0]).contents().find('#csc');
                    
                    $nameField.val(card.cardHolderName).trigger('input');
                    $cardField.val(card.cardNumber).trigger('input');
                    $monthSelect.val(card.expirationMonth).trigger('change');
                    $yearSelect.val(card.expirationYear).trigger('change');
                    $securityField.val(card.ccv).trigger('input');
                    
                    // Submit the form
                    const $submitButton = win.jQuery($nestedIframe[0]).contents().find('button[type="submit"]');
                    $submitButton.click();
                });
            }
        });
    });
}
```

### JudoPay Gateway

```javascript
// Example from paymentIFrame.js
fillJudoPayDetails: (card) => {
    // Wait for the iframe to be fully loaded
    cy.get(paymentIframe.selectors.iframe, { timeout: 20000 }).should('be.visible');
    
    // Try to find and interact with the iframe
    cy.get('body').then($body => {
        // Check if we can find the iframe using the standard selector
        if ($body.find(paymentIframe.selectors.iframe).length > 0) {
            // Use the enter command for better iframe interaction
            cy.enter(paymentIframe.selectors.iframe).then(getBody => {
                // Fill in the card details
                getBody().find(paymentIframe.selectors.judoPayCardholderName)
                    .should('be.visible')
                    .clear()
                    .type(card.cardHolderName, { delay: 100 });

                getBody().find(paymentIframe.selectors.judoPayCardNumber)
                    .should('be.visible')
                    .clear()
                    .type(card.cardNumber.replace(/\s+/g, ''), { delay: 100 });

                getBody().find(paymentIframe.selectors.judoPayExpiryMonth)
                    .should('be.visible')
                    .select(card.expirationMonth);

                getBody().find(paymentIframe.selectors.judoPayExpiryYear)
                    .should('be.visible')
                    .select(card.expirationYear);

                getBody().find(paymentIframe.selectors.judoPayCcv)
                    .should('be.visible')
                    .clear()
                    .type(card.ccv, { delay: 100 });

                // Submit the form
                getBody().find(paymentIframe.selectors.cardCaptureForm).submit();
            });
        }
    });
}
```

## Handling Iframes

The framework uses several approaches to handle iframes, which are commonly used in payment forms:

### 1. Using the Cypress iframe plugin

```javascript
cy.iframe('#card-capture-for-cardUuid').find('#cardholderName').type('Test User');
```

### 2. Using jQuery for nested iframes

```javascript
cy.window().then(win => {
    const $nestedIframe = win.jQuery('#barclays-iframe');
    const $nameField = win.jQuery($nestedIframe[0]).contents().find('#cardholderName');
    $nameField.val(card.cardHolderName).trigger('input');
});
```

### 3. Using the Cypress enter command

```javascript
cy.enter(paymentIframe.selectors.iframe).then(getBody => {
    getBody().find(paymentIframe.selectors.judoPayCardholderName).type(card.cardHolderName);
});
```

### 4. Fallback approaches

The framework includes multiple fallback approaches for when standard selectors don't work:

```javascript
// Try different selectors for form fields
const selectors = [
    // ID selectors
    {
        name: '#cardholderName',
        card: '#cardNumber',
        month: '#expiryMonth',
        year: '#expiryYear',
        security: '#csc',
        submit: 'button[type="submit"]'
    },
    // Placeholder selectors
    {
        name: 'input[placeholder="Cardholder Name"]',
        card: 'input[placeholder="Card Number"]',
        month: 'select[name="month"]',
        year: 'select[name="year"]',
        security: 'input[placeholder="Security Code"]',
        submit: 'button:contains("SUBMIT")'
    },
    // Generic selectors
    {
        name: 'input[id*="name"], input[name*="name"], input[placeholder*="name"]',
        card: 'input[id*="card"], input[name*="card"], input[placeholder*="card"]',
        month: 'select[id*="month"], select[name*="month"]',
        year: 'select[id*="year"], select[name*="year"]',
        security: 'input[id*="security"], input[id*="cvv"], input[id*="cvc"]',
        submit: 'button[type="submit"], input[type="submit"], button'
    }
];
```

## 3D Secure Authentication

The framework includes specialized handling for 3D Secure authentication challenges:

```javascript
handle3DSChallenge: (card) => {
    // Wait for the 3DS iframe to load
    cy.wait(3000);

    // Define possible iframe selectors for different payment providers
    const possibleIframeSelectors = [
        '#challengeFrame',                // Standard 3DS iframe
        '#Cardinal-CCA-IFrame',           // Cardinal Commerce iframe
        'iframe[name*="3ds"]',            // Any iframe with '3ds' in the name
        'iframe[id*="challenge"]'         // Any iframe with 'challenge' in the ID
    ];

    // Define possible selectors for password field and submit button
    const possiblePasswordSelectors = [
        '#password',                      // Standard password field
        'input[type="password"]',         // Any password input
        'input[name*="password"]'         // Input with 'password' in name
    ];

    const possibleSubmitSelectors = [
        '#txtButton',                     // Standard submit button
        'button[type="submit"]',          // Any submit button
        'button:contains("Submit")'       // Button with text "Submit"
    ];

    // Try each possible iframe selector
    for (const iframeSelector of possibleIframeSelectors) {
        cy.get('body').then($body => {
            if ($body.find(iframeSelector).length > 0) {
                // Found a matching iframe, try to interact with it
                cy.iframe(iframeSelector).then($iframe => {
                    // Try each possible password field selector
                    for (const passwordSelector of possiblePasswordSelectors) {
                        if ($iframe.find(passwordSelector).length > 0) {
                            // Enter password
                            cy.iframe(iframeSelector).find(passwordSelector)
                                .type(card.challengePassword);
                            
                            // Try each possible submit button selector
                            for (const submitSelector of possibleSubmitSelectors) {
                                if ($iframe.find(submitSelector).length > 0) {
                                    cy.iframe(iframeSelector).find(submitSelector)
                                        .click();
                                    return;
                                }
                            }
                        }
                    }
                });
            }
        });
    }
    
    // Wait for the challenge to complete
    cy.wait(5000);
}
```

## Testing Payment Success and Failure

The framework includes methods to verify payment success and failure:

### Verifying Payment Success

```javascript
// From payByLinkPage.js
assertSuccessfulPayment(paymentAmount) {
    // Add a longer wait for the payment to complete
    cy.wait(10000);

    // Look for success indicators with a longer timeout
    cy.get('body', { timeout: 30000 }).then($body => {
        // Check for the tick icon
        const hasTickIcon = $body.find(payByLinkPage.selectors.tickIcon).length > 0;
        
        // Check for success message with various formats
        const successPhrases = [
            'payment was successful',
            'payment successful',
            'successfully paid',
            'payment complete',
            'thank you for your payment'
        ];

        // Check if any success phrase is present in the page text
        const bodyText = $body.text().toLowerCase();
        let hasSuccessMessage = false;
        
        for (const phrase of successPhrases) {
            if (bodyText.includes(phrase)) {
                hasSuccessMessage = true;
                break;
            }
        }
        
        // Assert that either a tick icon or success message is present
        expect(hasTickIcon || hasSuccessMessage).to.be.true;
    });
}
```

### Verifying Payment Failure

```javascript
// From payByLinkPage.js
assertUnsuccessfulPayment(paymentAmount) {
    cy.get(payByLinkPage.selectors.crossIcon, {timeout: 10000}).should('exist')
    cy.get(payByLinkPage.selectors.headerTitle).should('have.text', 'Payment failure!')
    cy.get(payByLinkPage.selectors.paymentResultMessage)
        .should('have.text', `We failed to take a payment of ${paymentAmount}.`)
}
```

## Best Practices

1. **Use Robust Selectors**: The framework uses multiple selector strategies to handle different payment gateway implementations.

2. **Handle Nested Iframes**: Payment forms often use nested iframes for security. Use the appropriate approach based on the iframe structure.

3. **Add Proper Waits**: Payment processing can take time. Add appropriate waits to ensure the payment has time to process.

4. **Implement Fallback Approaches**: Always have fallback approaches for when standard selectors don't work.

5. **Handle 3D Secure Challenges**: Implement specialized handling for 3D Secure authentication challenges.

6. **Verify Payment Results**: Always verify that the payment was successful or failed as expected.

7. **Use Test Cards**: Use test cards provided by the payment gateway for testing different scenarios.

8. **Log Detailed Information**: Add detailed logging to help debug payment issues.

## Conclusion

Testing payment gateways is one of the most complex aspects of web application testing due to iframes, security measures, and 3D Secure authentication. The Cypress framework provides robust tools and approaches to handle these complexities, making it possible to create reliable automated tests for payment flows.
