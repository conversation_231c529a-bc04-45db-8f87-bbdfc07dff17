package booking;

import base.BaseTest;
import org.json.simple.JSONObject;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;
import pages.booking.BookingHubPage;
import pages.booking.BookingsPage;
import pages.booking.CalendarPage;
import pages.booking.ExtrasPage;
import pages.booking.GuestDetailsPage;
import pages.booking.PayPage;
import pages.booking.YourStayPage;
import pages.root.StaffLoginPage;
import utils.Data;
import utils.GenericMethods;
import utils.Report;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * Test class for creating a booking as staff with full deposit
 * Similar to Cypress test: B1 D0 staffAvailabilityDepositFull.spec.cy.js
 */
public class StaffAvailabilityDepositFullTest extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Data data;
    Report report;
    
    // Page objects
    StaffLoginPage staffLoginPage;
    YourStayPage yourStayPage;
    ExtrasPage extrasPage;
    GuestDetailsPage guestDetailsPage;
    PayPage payPage;
    BookingHubPage bookingHubPage;
    BookingsPage bookingsPage;
    CalendarPage calendarPage;
    
    // Test data
    String hotelSlug = "cardiff"; // Using cardiff as it has Barclays payment gateway
    String staffEmail = "<EMAIL>";
    String staffPassword = "Password123?";
    String guestDetailsPath;
    String cardDetailsPath;
    
    // Date variables
    String currentDate;
    String nextDayDate;
    
    @Parameters({"browserToUse", "headless", "environment"})
    @BeforeMethod(alwaysRun=true)
    public void setup(@Optional("chrome") String browserToUse, 
                     @Optional("true") String headless, 
                     @Optional("http://localhost:58000") String environment) {
        // Set parameters
        setBrowserToUse(browserToUse);
        setHeadless(headless);
        setEnvironment(environment);
        
        // Initialize WebDriver
        if (getBrowserToUse().equals("firefox")) {
            SetupFirefoxDriver();
        } else {
            SetupChromeDriver();
        }
        
        // Initialize report
        report = new Report();
        report.setLocation("reports/StaffAvailabilityDepositFullTest.txt");
        report.appendExport("\n *****\n * Start of test: Staff Availability Deposit Full Test\n *****");
        
        // Initialize required objects
        genericMethods = new GenericMethods(report);
        data = new Data();
        
        // Set test data paths
        guestDetailsPath = "src/test/resources/booking/guestDetails.json";
        cardDetailsPath = "src/test/resources/booking/cardDetails.json";
        
        // Set dates
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        currentDate = dateFormat.format(cal.getTime());
        
        cal.add(Calendar.DATE, 1);
        nextDayDate = dateFormat.format(cal.getTime());
        
        report.appendExport("INFO: Current date: " + currentDate);
        report.appendExport("INFO: Next day date: " + nextDayDate);
    }
    
    @Test(groups={"bookingEngine"})
    public void createBookingAsStaffWithFullDeposit() throws IOException {
        try {
            // Login as staff
            report.appendExport("STEP: Login as staff");
            driver.get(getEnvironment() + "/staff/login");
            
            staffLoginPage = new StaffLoginPage(driver, report, getEnvironment());
            staffLoginPage.validatePage();
            staffLoginPage.typeEmail(staffEmail);
            staffLoginPage.typePassword(staffPassword);
            staffLoginPage.submitLogin();
            
            takeDebugScreenshot("after_login");
            
            // Access booking engine as staff
            report.appendExport("STEP: Access booking engine as staff");
            yourStayPage = new YourStayPage(driver, report, getEnvironment());
            yourStayPage.open(hotelSlug);
            
            takeDebugScreenshot("your_stay_page");
            
            // Select dates
            report.appendExport("STEP: Select dates");
            yourStayPage.clickSelectDates();
            yourStayPage.selectCurrentDate();
            yourStayPage.selectNextDayDate();
            yourStayPage.clickSearchButton();
            yourStayPage.clickOccupancySearchButton();
            
            takeDebugScreenshot("after_search");
            
            // Select room
            report.appendExport("STEP: Select room");
            yourStayPage.selectTwinRoom();
            
            takeDebugScreenshot("after_room_selection");
            
            // Continue to extras page
            report.appendExport("STEP: Continue to extras page");
            yourStayPage.clickContinueButton();
            
            // Skip extras
            report.appendExport("STEP: Skip extras");
            extrasPage = new ExtrasPage(driver, report, getEnvironment());
            extrasPage.clickContinueButton();
            
            takeDebugScreenshot("extras_page");
            
            // Fill in guest details
            report.appendExport("STEP: Fill in guest details");
            guestDetailsPage = new GuestDetailsPage(driver, report, getEnvironment());
            guestDetailsPage.fillGuestDetails(guestDetailsPath);
            guestDetailsPage.selectPostcodeLookupButton();
            guestDetailsPage.selectFirstAddressFromDropdown();
            guestDetailsPage.clickContinueButton();
            
            takeDebugScreenshot("guest_details_page");
            
            // Make payment
            report.appendExport("STEP: Make payment");
            payPage = new PayPage(driver, report, getEnvironment());
            payPage.assertDepositPaymentMessage("200.00");
            payPage.fillPaymentDetails(cardDetailsPath);
            
            takeDebugScreenshot("payment_page");
            
            // Verify booking hub
            report.appendExport("STEP: Verify booking hub");
            bookingHubPage = new BookingHubPage(driver, report, getEnvironment());
            bookingHubPage.assertPaymentStatus("paid");
            bookingHubPage.clickExpandBookingButton();
            bookingHubPage.assertReservationDetails("Twin Room", "200.00");
            bookingHubPage.assertPaymentAmount(200.00);
            bookingHubPage.assertBalanceAmount(0.00);
            bookingHubPage.assertGuestDetails(guestDetailsPath);
            bookingHubPage.assertBookingDates(currentDate, nextDayDate);
            
            takeDebugScreenshot("booking_hub_page");
            
            // Get booking reference
            String bookingRef = bookingHubPage.getBookingReference();
            report.appendExport("INFO: Booking reference: " + bookingRef);
            
            // Verify booking on dashboard
            report.appendExport("STEP: Verify booking on dashboard");
            driver.get(getEnvironment() + "/hotels/" + hotelSlug + "?date=" + currentDate);
            genericMethods.waitForElementWithText(bookingRef);
            
            takeDebugScreenshot("dashboard_page");
            
            // Verify booking on bookings page
            report.appendExport("STEP: Verify booking on bookings page");
            bookingsPage = new BookingsPage(driver, report, getEnvironment());
            bookingsPage.open(hotelSlug);
            genericMethods.waitForElementWithText(bookingRef);
            
            takeDebugScreenshot("bookings_page");
            
            // Verify booking on payment report
            report.appendExport("STEP: Verify booking on payment report");
            driver.get(getEnvironment() + "/hotels/" + hotelSlug + "/reports/invoice/payment?date=" + currentDate);
            genericMethods.waitForElementWithText(bookingRef);
            
            takeDebugScreenshot("payment_report_page");
            
            // Verify booking on calendar
            report.appendExport("STEP: Verify booking on calendar");
            calendarPage = new CalendarPage(driver, report, getEnvironment());
            calendarPage.open(hotelSlug, currentDate);
            calendarPage.assertBookingExists(bookingRef);
            
            takeDebugScreenshot("calendar_page");
            
            report.appendExport("TEST PASSED: Successfully created booking as staff with full deposit");
        } catch (Exception e) {
            report.appendExport("TEST FAILED: " + e.getMessage());
            takeDebugScreenshot("test_failure");
            throw e;
        }
    }
    
    @AfterMethod(alwaysRun=true)
    public void tearDown() {
        report.appendExport("\n *****\n * End of test\n *****");
        report.exportReport();
        if (driver != null) {
            driver.quit();
        }
    }
    
    /**
     * Helper method to take screenshots for debugging
     */
    private void takeDebugScreenshot(String name) throws IOException {
        File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
        Path directory = Paths.get("reports/screenshots");
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }
        Path destination = Paths.get("reports/screenshots/" + name + "_" + System.currentTimeMillis() + ".png");
        Files.copy(screenshot.toPath(), destination);
        report.appendExport("INFO: Screenshot saved to: " + destination.toAbsolutePath());
    }
}
