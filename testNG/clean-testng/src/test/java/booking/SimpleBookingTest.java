package booking;

import base.BaseTest;
import org.openqa.selenium.By;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;
import utils.Report;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * Simple test class for demonstrating a booking flow
 */
public class SimpleBookingTest extends BaseTest {
    // Required classes
    Report report;
    WebDriverWait wait;
    
    // Test data
    String hotelSlug = "cardiff"; // Using cardiff as it has Barclays payment gateway
    String staffEmail = "<EMAIL>";
    String staffPassword = "Password123?";
    
    // Date variables
    String currentDate;
    String nextDayDate;
    
    @Parameters({"browserToUse", "headless", "environment"})
    @BeforeMethod(alwaysRun=true)
    public void setup(@Optional("chrome") String browserToUse, 
                     @Optional("true") String headless, 
                     @Optional("http://localhost:58000") String environment) {
        // Set parameters
        setBrowserToUse(browserToUse);
        setHeadless(headless);
        setEnvironment(environment);
        
        // Initialize WebDriver
        if (getBrowserToUse().equals("firefox")) {
            SetupFirefoxDriver();
        } else {
            SetupChromeDriver();
        }
        
        // Initialize report
        report = new Report();
        report.setLocation("reports/SimpleBookingTest.txt");
        report.appendExport("\n *****\n * Start of test: Simple Booking Test\n *****");
        
        // Initialize wait
        wait = new WebDriverWait(driver, Duration.ofSeconds(30));
        
        // Set dates
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        currentDate = dateFormat.format(cal.getTime());
        
        cal.add(Calendar.DATE, 1);
        nextDayDate = dateFormat.format(cal.getTime());
        
        report.appendExport("INFO: Current date: " + currentDate);
        report.appendExport("INFO: Next day date: " + nextDayDate);
    }
    
    @Test(groups={"bookingEngine"})
    public void simpleBookingTest() throws IOException {
        try {
            // Login as staff
            report.appendExport("STEP: Login as staff");
            driver.get(getEnvironment() + "/staff/login");
            
            // Fill in login form
            WebElement emailField = wait.until(ExpectedConditions.visibilityOfElementLocated(By.id("email")));
            WebElement passwordField = wait.until(ExpectedConditions.visibilityOfElementLocated(By.id("password")));
            WebElement loginButton = wait.until(ExpectedConditions.elementToBeClickable(By.cssSelector("button[type='submit']")));
            
            emailField.sendKeys(staffEmail);
            passwordField.sendKeys(staffPassword);
            loginButton.click();
            
            takeDebugScreenshot("after_login");
            
            // Wait for dashboard to load
            wait.until(ExpectedConditions.urlContains("/hotels"));
            
            report.appendExport("INFO: Successfully logged in as staff");
            
            // Navigate to booking engine
            report.appendExport("STEP: Navigate to booking engine");
            driver.get(getEnvironment() + "/booking/" + hotelSlug);
            
            takeDebugScreenshot("booking_engine");
            
            report.appendExport("TEST PASSED: Successfully navigated to booking engine");
        } catch (Exception e) {
            report.appendExport("TEST FAILED: " + e.getMessage());
            takeDebugScreenshot("test_failure");
            throw e;
        }
    }
    
    @AfterMethod(alwaysRun=true)
    public void tearDown() {
        report.appendExport("\n *****\n * End of test\n *****");
        report.exportReport();
        if (driver != null) {
            driver.quit();
        }
    }
    
    /**
     * Helper method to take screenshots for debugging
     */
    private void takeDebugScreenshot(String name) throws IOException {
        File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
        Path directory = Paths.get("reports/screenshots");
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }
        Path destination = Paths.get("reports/screenshots/" + name + "_" + System.currentTimeMillis() + ".png");
        Files.copy(screenshot.toPath(), destination);
        report.appendExport("INFO: Screenshot saved to: " + destination.toAbsolutePath());
    }
}
