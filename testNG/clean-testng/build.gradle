plugins {
    id 'java'
}

group 'com.hls'
version '1.0-SNAPSHOT'

sourceCompatibility = 11
targetCompatibility = 11

repositories {
    mavenCentral()
}

dependencies {
    // TestNG
    implementation 'org.testng:testng:7.7.1'
    
    // Selenium
    implementation 'org.seleniumhq.selenium:selenium-java:4.10.0'
    
    // WebDriverManager
    implementation 'io.github.bonigarcia:webdrivermanager:5.3.3'
    
    // JSON
    implementation 'com.googlecode.json-simple:json-simple:1.1.1'
    
    // Apache Commons
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'commons-io:commons-io:2.11.0'
    
    // Logging
    implementation 'org.slf4j:slf4j-api:2.0.7'
    implementation 'org.slf4j:slf4j-simple:2.0.7'
}

test {
    useTestNG() {
        // Use the suite defined by the system property 'suite'
        if (System.getProperty('suite')) {
            suites System.getProperty('suite')
        } else {
            // Default suite if none specified
            suites 'src/test/resources/suites/simple-booking-test.xml'
        }
        
        // Output directory for reports
        outputDirectory = file("$buildDir/reports/testng")
        
        // Preserve the order of tests
        preserveOrder true
        
        // Show standard streams
        testLogging.showStandardStreams = true
    }
    
    // Pass system properties to the test
    systemProperties System.getProperties()
    
    // Always run tests
    ignoreFailures = true
}

// Task to run a specific test suite
task runSuite(type: Test) {
    useTestNG() {
        // Use the suite defined by the system property 'suite'
        if (System.getProperty('suite')) {
            suites System.getProperty('suite')
        } else {
            // Default suite if none specified
            suites 'src/test/resources/suites/simple-booking-test.xml'
        }
        
        // Output directory for reports
        outputDirectory = file("$buildDir/reports/testng")
        
        // Preserve the order of tests
        preserveOrder true
        
        // Show standard streams
        testLogging.showStandardStreams = true
    }
    
    // Pass system properties to the test
    systemProperties System.getProperties()
}

// Task to clean reports directory
task cleanReports(type: Delete) {
    delete 'reports'
}

// Task to create reports directory
task createReportsDir {
    doLast {
        mkdir 'reports'
        mkdir 'reports/screenshots'
    }
}

// Run createReportsDir before tests
test.dependsOn createReportsDir
runSuite.dependsOn createReportsDir
