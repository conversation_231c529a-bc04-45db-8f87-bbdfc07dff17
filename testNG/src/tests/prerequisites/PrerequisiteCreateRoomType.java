import org.json.simple.JSONArray;
import org.testng.annotations.Test;

/**
 *
 * This covers the Creation of Room Types, as a pre-requisite test.
 *
 * Scenarios:
 * SC1: A Staff Admin user creating a new Room Type
 *
 */
public class PrerequisiteCreateRoomType extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    CurrentData currentData;
    Login login;
    Export export;
    Assertion assertion;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    RoomTypesCreatePage roomTypesCreatePage;
    RoomTypesPage roomTypesPage;

    String testDataLoginPath = "";
    String testDataExportPath = "";
    String testDataHotelPath = "";

    // Variables used throughout test
    String hotelName = "";
    String hotelSlug = "";
    int roomTypes = 0;

    String createdRoomTypeType = "";
    String createdRoomTypeCode = "";
    String createdRoomTypeTitle = "";
    String createdRoomTypeDescription = "";
    String createdRoomTypeLetter = "";
    String createdRoomTypePrice = "";
    String createdRoomTypePoints = "";
    String createdRoomTypeOccupants = "";
    String createdRoomTypeAdults = "";
    String createdRoomTypeChildren = "";

    /**
     *
     * Prerequisite Create Room Type
     *
     * Covers scenarios of:
     * SC1: A Staff Admin user creating a new Room Type
     *
     */
    @Test(groups = {"prerequisiteCreateRoomType"})
    public void prerequisiteCreateRoomType() {
        // Set test case name
        setTestCaseName("prerequisiteCreateRoomType");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";
        testDataHotelPath = getTestDataPath() + "Hotel.json";

        // Set the data for the hotel
        setDataHotel();
        setDataForCreatingRoomType();

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Room Type\n" +
                        " * Covers scenarios of:\n" +
                        " * SC1: A Staff Admin user creating a new Room Type\n" +
                        " *****");

        // Start the test
        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        hotelsPage.typeHotelNameInSearch(hotelName);
        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        dashboardPage.validatePage();

        // Retrieve room types count at last possible moment. Needed due to change in behaviour when a room type already exists for a hotel
        roomTypes =  currentData.getRoomTypeCount(testDataHotelPath);
        getReport().appendExport("INFO: Room Types count: " + roomTypes);
        if(roomTypes > 0) {
            roomTypesPage = dashboardPage.horizontalOptionsPage.navigateToRoomTypesPage();
            roomTypesPage.validatePage();

            // Check the fields to insert room type data exist
            assertPageFieldsForRoomTypePage();

            // Enter room type details
            typeDataForRoomTypePage();

            // Check that the values inserted are actually inserted correctly
            assertValuesInsertedForRoomTypePage();

            // Create the room type
            roomTypesPage = roomTypesPage.clickSubmitCreateRoomType();

        } else {
            roomTypesCreatePage = dashboardPage.horizontalOptionsPage.navigateToRoomTypesCreatePageRedirect();
            roomTypesCreatePage.validatePage();

            // Check the fields to insert room type data exist
            assertPageFieldsForRoomTypeCreatePage();

            // Enter room type details
            typeDataForRoomTypeCreatePage();

            // Check that the values inserted are actually inserted correctly
            assertValuesInsertedForRoomTypeCreatePage();

            // Create the room type
            roomTypesPage = roomTypesCreatePage.clickSubmit();
        }

        roomTypesPage.validatePage();

        // Add the data set to an export file
        addDataToExportFileForCreatedRoomType();

        // Export room types to the areas needed (ie next test suite)
        export.exportFile(testDataExportPath);

        // Export room type to the path current data
        export.exportFileCurrentData(getPathCurrentDataHotels() + hotelSlug + "/roomTypes/" + createdRoomTypeCode);

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        } else {
            getReport().appendExport("ERROR: Incorrect user type to create staff user.");
        }
    }

    public void setDataHotel() {
        String[] hotelValues = new String[]{"slug", "name"};
        JSONArray hotelDetails = data.returnJSONArray(testDataHotelPath, hotelValues);
        hotelSlug = hotelDetails.get(0).toString();
        hotelName = hotelDetails.get(1).toString();
    }

    public void setDataForCreatingRoomType() {
        if (getDataToUse().equals("random")) {
            getReport().appendExport("INFO: Using random data.");
            createdRoomTypeType = data.getRandomName("roomTypeName") + " " + data.getRandomString(2, "lowercase");
            createdRoomTypeCode = data.getRandomString(5, "lowercase");
            createdRoomTypeTitle = createdRoomTypeType;
            createdRoomTypeDescription = "Room: " + createdRoomTypeType + " " + data.getRandomString(30, "lowercase");
            createdRoomTypeLetter = data.getRandomString(1, "uppercase");
            createdRoomTypePrice = data.getRandomString(3, "numeric");
            createdRoomTypePoints = data.getRandomString(1, "numeric");
            boolean setValidOccupantNumbers = false;
            while(!setValidOccupantNumbers) {
                createdRoomTypeOccupants = data.getRandomString(1, "numeric");
                if(Integer.parseInt(createdRoomTypeOccupants)>0) {
                    setValidOccupantNumbers = true;
                }
            }
            setValidOccupantNumbers = false;
            while(!setValidOccupantNumbers) {
                createdRoomTypeAdults = data.getRandomString(1, "numeric");
                if(Integer.parseInt(createdRoomTypeAdults)<=Integer.parseInt(createdRoomTypeOccupants) && Integer.parseInt(createdRoomTypeAdults)>0) {
                    setValidOccupantNumbers = true;
                }
            }
            setValidOccupantNumbers = false;
            while(!setValidOccupantNumbers) {
                createdRoomTypeChildren = data.getRandomString(1, "numeric");
                if(Integer.parseInt(createdRoomTypeChildren)<=Integer.parseInt(createdRoomTypeOccupants)) {
                    setValidOccupantNumbers = true;
                }
            }
        }
        else {
            String[] createRoomTypeValues = new String[]{"type", "code", "title", "description", "letter", "price", "points", "occupants", "adults", "children"};
            getReport().appendExport("INFO: Using data in file: " + getTestDataPath() + getDataToUse());
            JSONArray createRoomTypeDetails = data.returnJSONArray(getTestDataPath() + getDataToUse(), createRoomTypeValues);
            createdRoomTypeType = createRoomTypeDetails.get(0).toString();
            createdRoomTypeCode = createRoomTypeDetails.get(1).toString();
            createdRoomTypeTitle = createRoomTypeDetails.get(2).toString();
            createdRoomTypeDescription = createRoomTypeDetails.get(3).toString();
            createdRoomTypeLetter = createRoomTypeDetails.get(4).toString();
            createdRoomTypePrice = createRoomTypeDetails.get(5).toString();
            createdRoomTypePoints = createRoomTypeDetails.get(6).toString();
            createdRoomTypeOccupants = createRoomTypeDetails.get(7).toString();
            createdRoomTypeAdults = createRoomTypeDetails.get(8).toString();
            createdRoomTypeChildren = createRoomTypeDetails.get(9).toString();
        }
    }

    public void assertPageFieldsForRoomTypePage() {
        assertion.assertElementExists(driver,roomTypesPage.typeLocator);
        assertion.assertElementExists(driver,roomTypesPage.codeLocator);
        assertion.assertElementExists(driver,roomTypesPage.titleLocator);
        assertion.assertElementExists(driver,roomTypesPage.descriptionLocator);
        assertion.assertElementExists(driver,roomTypesPage.letterLocator);
        assertion.assertElementExists(driver,roomTypesPage.priceLocator);
        assertion.assertElementExists(driver,roomTypesPage.pointsLocator);
        assertion.assertElementExists(driver,roomTypesPage.maxNumOccupantsLocator);
        assertion.assertElementExists(driver,roomTypesPage.maxNumAdultsLocator);
        assertion.assertElementExists(driver,roomTypesPage.maxNumChildrenLocator);
        assertion.assertElementExists(driver,roomTypesPage.submitButtonLocator);
    }

    public void typeDataForRoomTypePage() {
        roomTypesPage.enterTypeCreateRoomType(createdRoomTypeType);
        roomTypesPage.enterCodeCreateRoomType(createdRoomTypeCode);
        roomTypesPage.enterTitleCreateRoomType(createdRoomTypeTitle);
        roomTypesPage.enterDescriptionCreateRoomType(createdRoomTypeDescription);
        roomTypesPage.enterLetterCreateRoomType(createdRoomTypeLetter);
        roomTypesPage.enterPriceCreateRoomType(createdRoomTypePrice);
        roomTypesPage.enterPointsCreateRoomType(createdRoomTypePoints);
        roomTypesPage.enterMaxNumOccupantsCreateRoomType(createdRoomTypeOccupants);
        roomTypesPage.enterMaxNumAdultsCreateRoomType(createdRoomTypeAdults);
        roomTypesPage.enterMaxNumChildrenCreateRoomType(createdRoomTypeChildren);
    }

    public void assertValuesInsertedForRoomTypePage() {
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.typeLocator, createdRoomTypeType);
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.codeLocator, createdRoomTypeCode);
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.titleLocator, createdRoomTypeTitle);
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.descriptionLocator, createdRoomTypeDescription);
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.letterLocator, createdRoomTypeLetter);
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.priceLocator, createdRoomTypePrice);
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.pointsLocator, createdRoomTypePoints);
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.maxNumOccupantsLocator, createdRoomTypeOccupants);
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.maxNumAdultsLocator, createdRoomTypeAdults);
        assertion.assertValueInFieldFromWebElement(driver.findElement(roomTypesPage.createRoomTypeSectionLocator), roomTypesPage.maxNumChildrenLocator, createdRoomTypeChildren);
    }

    public void assertPageFieldsForRoomTypeCreatePage() {
        assertion.assertElementExists(driver,roomTypesCreatePage.typeLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.codeLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.titleLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.descriptionLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.letterLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.priceLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.pointsLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.maxNumOccupantsLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.maxNumAdultsLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.maxNumChildrenLocator);
        assertion.assertElementExists(driver,roomTypesCreatePage.submitButtonLocator);
    }

    public void typeDataForRoomTypeCreatePage() {
        roomTypesCreatePage.typeType(createdRoomTypeType);
        roomTypesCreatePage.typeCode(createdRoomTypeCode);
        roomTypesCreatePage.typeTitle(createdRoomTypeTitle);
        roomTypesCreatePage.typeDescription(createdRoomTypeDescription);
        roomTypesCreatePage.typeLetter(createdRoomTypeLetter);
        roomTypesCreatePage.typePrice(createdRoomTypePrice);
        roomTypesCreatePage.typePoints(createdRoomTypePoints);
        roomTypesCreatePage.typeMaxNumOccupants(createdRoomTypeOccupants);
        roomTypesCreatePage.typeMaxNumAdults(createdRoomTypeAdults);
        roomTypesCreatePage.typeMaxNumChildren(createdRoomTypeChildren);
    }

    public void assertValuesInsertedForRoomTypeCreatePage() {
        assertion.assertValueInField(driver, roomTypesCreatePage.typeLocator, createdRoomTypeType);
        assertion.assertValueInField(driver, roomTypesCreatePage.codeLocator, createdRoomTypeCode);
        assertion.assertValueInField(driver, roomTypesCreatePage.titleLocator, createdRoomTypeTitle);
        assertion.assertValueInField(driver, roomTypesCreatePage.descriptionLocator, createdRoomTypeDescription);
        assertion.assertValueInField(driver, roomTypesCreatePage.letterLocator, createdRoomTypeLetter);
        assertion.assertValueInField(driver, roomTypesCreatePage.priceLocator, createdRoomTypePrice);
        assertion.assertValueInField(driver, roomTypesCreatePage.pointsLocator, createdRoomTypePoints);
        assertion.assertValueInField(driver, roomTypesCreatePage.maxNumOccupantsLocator, createdRoomTypeOccupants);
        assertion.assertValueInField(driver, roomTypesCreatePage.maxNumAdultsLocator, createdRoomTypeAdults);
        assertion.assertValueInField(driver, roomTypesCreatePage.maxNumChildrenLocator, createdRoomTypeChildren);
    }

    public void addDataToExportFileForCreatedRoomType() {
        export.putExportData("type", createdRoomTypeType);
        export.putExportData("code", createdRoomTypeCode);
        export.putExportData("title", createdRoomTypeTitle);
        export.putExportData("description", createdRoomTypeDescription);
        export.putExportData("letter", createdRoomTypeLetter);
        export.putExportData("price", createdRoomTypePrice);
        export.putExportData("points", createdRoomTypePoints);
        export.putExportData("occupants", createdRoomTypeOccupants);
        export.putExportData("adults", createdRoomTypeAdults);
        export.putExportData("children", createdRoomTypeChildren);
    }
}