import org.json.simple.JSONArray;
import org.testng.annotations.Test;

    /**
     *
     * This covers the Creation of Staff Users, as a pre-requisite test.
     *
     * Scenarios:
     * SC1: A Staff Admin user creating a new Staff Admin user
     * SC2: A Staff Admin user creating a new Staff Support user
     * SC3: A Staff Admin user creating a new Staff Manager user
     * SC4: A Staff Admin user creating a new Staff Sales user
     *
     */
public class PrerequisiteCreateStaffUser extends BaseTest {

    // Required classes
    GenericMethods genericMethods;
    Login login;
    Export export;
    Assertion assertion;
    HotelsPage hotelsPage;
    StaffPage staffPage;
    StaffCreatePage staffCreatePage;
    StaffEditPage staffEditPage;

    String testDataLoginPath = "";
    String testDataExportPath = "";
    String testDataRolePath = "";

    // Variables used throughout test
    String createdStaffUserName = "";
    String createdStaffUserEmail = "";
    String createdStaffUserPhone = "";
    String createdStaffUserRole = "";
    String createdStaffUserUuid = "";
    String createdStaffUserPassword = "";

    /**
     *
     * Prerequisite Create Staff User
     *
     * Covers scenarios of:
     * SC1: A Staff Admin user creating a new Staff Admin user
     * SC2: A Staff Admin user creating a new Staff Support user
     * SC3: A Staff Admin user creating a new Staff Manager user
     * SC4: A Staff Admin user creating a new Staff Sales user
     *
     */
    @Test(groups={"prerequisiteCreateStaffUser"})
    public void prerequisiteCreateStaffUser() {
        // Set test case name
        setTestCaseName("prerequisiteCreateStaffUser");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";
        testDataRolePath = getTestDataPath() + "Role.json";

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(),getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Staff User\n" +
                        " * Covers scenarios of:\n" +
                        " * SC1: A Staff Admin user creating a new Staff Admin user\n" +
                        " * SC2: A Staff Admin user creating a new Staff Support user\n" +
                        " * SC3: A Staff Admin user creating a new Staff Manager user\n" +
                        " * SC4: A Staff Admin user creating a new Staff Sales user\n" +
                        " *****");

        // Start the test
        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Validate the Staff page that the user has landed on
        staffPage.validatePage();

        // Navigate to Staff Create page
        staffCreatePage = staffPage.clickCreate();
        staffCreatePage.validatePage();

        // Set the data for creating the staff user
        setDataForCreatingStaffUser();
        // Insert the data into the page for creating a staff user
        typeDataForStaffCreatePage();
        // Assert that the values inserted are actually there in the page for creating a staff user
        assertValuesInsertedForStaffCreatePage();

        // Create the staff user
        // User is navigated back to the Staff page
        staffPage = staffCreatePage.clickSubmit();
        staffPage.validatePage();

        // Set the createdStaffUserUuid
        setDataForCreatedStaffUserUuid();

        // Add the data set to an export file
        addDataToExportFileForCreatedStaffUser();

        // Export created staff user to the path current data staff.
        // Needed here anyway, as the user has been created
        export.exportFileCurrentData(getPathCurrentDataStaff() + createdStaffUserRole + "_" + createdStaffUserUuid);

        // Edit Staff User steps
        // Navigate to Staff Edit page
        staffEditPage = staffPage.clickStaffName(createdStaffUserName, createdStaffUserUuid);
        staffEditPage.validatePage();

        // Set the password for the edited staff user
        setDataPasswordForCreatedStaffUser();

        // Insert the password into the staff edit page
        staffEditPage.editPassword(createdStaffUserPassword);
        staffEditPage.editPasswordConfirmation(createdStaffUserPassword);

        // Assert that the values inserted are actually there in the page for editing a staff user
        assertValuesEditedForStaffEditPage();

        // Edit the staff user
        // User is navigated back to the Staff page
        staffPage = staffEditPage.clickSubmit();
        staffPage.validateAfterEditPage(createdStaffUserUuid);

        // Add the data set to an export file
        addDataToExportFileForEditedStaffUser();

        // Export edited staff user to the areas needed (ie next test suite)
        export.exportFile(testDataExportPath);

        // Export edited staff user to the path current data staff
        export.exportFileCurrentData(getPathCurrentDataStaff() + createdStaffUserRole + "_" + createdStaffUserUuid);

        setPassedReportLocation();
    }


    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        } else {
            getReport().appendExport("ERROR: Incorrect user type to create staff user.");
        }
        getReport().appendExport("STEP: Directly navigate to the Staff page, by entering /staff into the url bar, after the environment path.");
        driver.get(getEnvironment() + "/staff");
        getReport().appendExport("INFO: User should be on the Staff page.");
        staffPage = new StaffPage(driver, getReport(), getEnvironment());
    }

    /*
     * Set the data to use to be random data, data from a file or data in a chain for a chain of tests
     *
     */
    public void setDataForCreatingStaffUser() {
        if (getDataToUse().equals("random")) {
            createdStaffUserName = data.getRandomName("firstName") + " " + data.getRandomName("lastName") + " " + data.getRandomString(4,"any");
            createdStaffUserEmail = data.getRandomString(10, "lowercase") + "@high-level-software.com";
            createdStaffUserPhone = data.getRandomString(10, "numeric");
            getReport().appendExport("INFO: Using random data.");
            // Parse the role details
            String[] roleValues = new String[]{"role"};
            JSONArray roleDetails = data.returnJSONArray(testDataRolePath, roleValues);
            createdStaffUserRole = roleDetails.get(0).toString();
        }
        else {
            String[] createStaffValues = new String[]{"name", "email", "phone", "role"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray createStaffDetails = data.returnJSONArray(getDataToUse(), createStaffValues);
            createdStaffUserName = createStaffDetails.get(0).toString();
            createdStaffUserEmail = createStaffDetails.get(1).toString();
            createdStaffUserPhone = createStaffDetails.get(2).toString();
            createdStaffUserRole = createStaffDetails.get(3).toString();
        }
    }

    public void typeDataForStaffCreatePage() {
        staffCreatePage.typeName(createdStaffUserName);
        staffCreatePage.typeEmail(createdStaffUserEmail);
        staffCreatePage.typePhone(createdStaffUserPhone);
        staffCreatePage.typeRole(createdStaffUserRole);
    }

    public void assertValuesInsertedForStaffCreatePage() {
        assertion.assertValueInField(driver, staffCreatePage.nameLocator, createdStaffUserName);
        assertion.assertValueInField(driver, staffCreatePage.emailLocator, createdStaffUserEmail);
        assertion.assertValueInField(driver, staffCreatePage.phoneLocator, createdStaffUserPhone);
        assertion.assertTextInDropDownField(driver, staffCreatePage.roleLocator, createdStaffUserRole);
    }


    public void setDataForCreatedStaffUserUuid() {
        createdStaffUserUuid = staffPage.returnStaffUuidFromName(createdStaffUserName);
    }

    public void addDataToExportFileForCreatedStaffUser() {
        export.putExportData("name", createdStaffUserName);
        export.putExportData("email", createdStaffUserEmail);
        export.putExportData("phone", createdStaffUserPhone);
        export.putExportData("role", createdStaffUserRole);
        switch (createdStaffUserRole) {
            case "Administrator":
                export.putExportData("user", "staff-admin");
                break;
            case "Support":
                export.putExportData("user", "staff-support");
                break;
            case "Manager":
                export.putExportData("user", "staff-manager");
                break;
            case "Sales":
                export.putExportData("user", "staff-sales");
                break;
        }
        export.putExportData("uuid", createdStaffUserUuid);
    }

    public void setDataPasswordForCreatedStaffUser() {
        if (getDataToUse().equals("random")) {
            createdStaffUserPassword = genericMethods.getRandomPassword();
        }
        else {
            String[] editStaffValues = new String[]{"password"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editStaffDetails = data.returnJSONArray(getDataToUse(), editStaffValues);
            createdStaffUserPassword = editStaffDetails.get(0).toString();
        }
    }

    public void assertValuesEditedForStaffEditPage() {
        assertion.assertValueInField(driver, staffEditPage.nameLocator, createdStaffUserName);
        assertion.assertValueInField(driver, staffEditPage.emailLocator, createdStaffUserEmail);
        assertion.assertValueInField(driver, staffEditPage.passwordLocator, createdStaffUserPassword);
        assertion.assertValueInField(driver, staffEditPage.passwordConfirmationLocator, createdStaffUserPassword);
        assertion.assertValueInField(driver, staffEditPage.phoneLocator, createdStaffUserPhone);
        assertion.assertTextInDropDownField(driver, staffEditPage.roleLocator, createdStaffUserRole);
    }

    public void addDataToExportFileForEditedStaffUser() {
        export.putExportData("name", createdStaffUserName);
        export.putExportData("email", createdStaffUserEmail);
        export.putExportData("phone", createdStaffUserPhone);
        export.putExportData("password", createdStaffUserPassword);
        export.putExportData("role", createdStaffUserRole);
        switch (createdStaffUserRole) {
            case "Administrator":
                export.putExportData("user", "staff-admin");
                break;
            case "Support":
                export.putExportData("user", "staff-support");
                break;
            case "Manager":
                export.putExportData("user", "staff-manager");
                break;
            case "Sales":
                export.putExportData("user", "staff-sales");
                break;
        }
        export.putExportData("uuid", createdStaffUserUuid);
    }
}