import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import org.testng.annotations.Test;

import java.io.File;
import java.util.ArrayList;

/**
     *
     * This covers the Creation of Groups, as a pre-requisite test.
     *
     * Scenarios:
     * SC1: A Staff Admin user creating a new Group
     *
     */
public class PrerequisiteCreateGroup extends BaseTest {

    // Required classes
    GenericMethods genericMethods;
    Login login;
    Export export;
    Assertion assertion;
    Commands commands;
    CurrentData currentData;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    SettingsPage settingsPage;
    GroupsOverviewPage groupsOverviewPage;
    CreateGroupPage createGroupPage;
    StaffLoginPage staffLoginPage;
    GroupAccountActivationPage groupAccountActivationPage;
    GroupAdminLoginPage groupAdminLoginPage;

    String testDataLoginPath = "";
    String testDataExportPath = "";

    // Variables used throughout test
    String createdGroupName;
    String createdGroupDescription;
    String createdGroupHotelName;
    String groupSlug;
    ArrayList<String> createdGroupHotelUuids;
    ArrayList<String> createdGroupHotelSlugs;
    ArrayList<String> createdGroupHotelNames;
    ArrayList<JSONObject> wholeFileJsonObjectArrayHotels;
    ArrayList<JSONObject> wholeFileJsonObjectArrayManagers;
    ArrayList<String> groupAdminUuidAndTokens;
    String createdGroupAdminUserPassword;
    ArrayList<String> allGroupManagersPathsToBeRemoved;

    /**
     *
     * Prerequisite Create Group
     *
     * Covers scenarios of:
     * SC1: A Staff Admin user creating a new Group
     *
     */
    @Test(groups={"prerequisiteCreateGroup"})
    public void prerequisiteCreateGroup() {
        // Set test case name
        setTestCaseName("prerequisiteCreateGroup");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());
        commands = new Commands(getReport(), getAwsProfile());

        commands.sqsPurgeQueue();

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Group\n" +
                        " * Covers scenarios of:\n" +
                        " * SC1: A Staff Admin user creating a new Group\n" +
                        " *****");

        // Start the test
        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Set the data for creating the Group
        setDataGroupPage();

        for(int i=0; i<createdGroupHotelSlugs.size(); i++) {
            hotelsPage.typeHotelNameInSearch(createdGroupHotelNames.get(i));
            hotelsPage = hotelsPage.clickSearch();
            hotelsPage.validatePage();
            dashboardPage = hotelsPage.selectHotel(createdGroupHotelSlugs.get(i), createdGroupHotelNames.get(i));
            dashboardPage.validatePage();
            settingsPage = dashboardPage.navigateToHotelSettingsPage();
            String newHotelName = createdGroupHotelNames.get(i) + " " + createdGroupHotelName;
            settingsPage.editName(newHotelName);
            createdGroupHotelNames.set(i, newHotelName);
            dashboardPage = settingsPage.clickSubmit();
            hotelsPage = dashboardPage.horizontalOptionsPage.navigateToHotelsPage();
        }

        // Navigate to the Group Overview page
        groupsOverviewPage = hotelsPage.clickCreateGroupLink();

        // Validate the Group Overview page that the user has landed on
        groupsOverviewPage.validatePage();

        // Navigate to Group Create page
        createGroupPage = groupsOverviewPage.clickCreateNewGroup();
        createGroupPage.validatePage();

        createGroupPage.typeGroupName(createdGroupName);
        createGroupPage.typeGroupDescription(createdGroupDescription);

        createGroupPage.typeHotelsFilterTextBox(createdGroupHotelName);
        createGroupPage.clickHotelsDoubleRightArrow();

        createGroupPage.scrollToViewHoteliersSection();
        createGroupPage.clickHoteliersDoubleRightArrow();

        createGroupPage.scrollToViewSaveButton();
        groupsOverviewPage = createGroupPage.clickSaveButton();
        groupsOverviewPage.validatePage();

        groupsOverviewPage.clickLogoutLink(login.getLoginUser());
        logoutRouting(groupsOverviewPage.logout);

        exportToExistingHotels();

        addDataToExportFileForCreatedGroup();

        // Export created group user to the areas needed (ie next test suite)
        export.exportFile(testDataExportPath);

        // Export created group to the path current data groups
        export.exportFileCurrentData(getPathCurrentDataGroups() + groupSlug +"/" + "group_" + groupSlug);

        // Group Admin stuff. Got hoteliers data in array. Now just to loop through and sort the password
        createGroupAdminPassword();

        for(int i=0; i<groupAdminUuidAndTokens.size(); i++) {
            setDataForCreatedGroupAdminPassword();
            driver.get(getEnvironment() + "/account/" + groupAdminUuidAndTokens.get(i));
            groupAccountActivationPage = new GroupAccountActivationPage(getDriver(), getReport(), getEnvironment(), "Test", groupAdminUuidAndTokens.get(i));
            groupAccountActivationPage.validatePage();
            groupAccountActivationPage.typePassword(createdGroupAdminUserPassword);
            groupAccountActivationPage.typePasswordConfirmation(createdGroupAdminUserPassword);
            groupAdminLoginPage = groupAccountActivationPage.clickActivateAccount();
            groupAdminLoginPage.validatePage();
            JSONObject groupAdmin = wholeFileJsonObjectArrayManagers.get(i);
            groupAdmin.remove("password");
            groupAdmin.put("password", createdGroupAdminUserPassword);
            groupAdmin.remove("user");
            groupAdmin.put("user", "group-admin");
            groupAdmin.remove("role");
            groupAdmin.put("role", "Group Admin");

            groupAdmin.put("groupSlug", groupSlug);
            groupAdmin.put("groupName", createdGroupName);

            groupAdmin.remove("hotels");
            export.mainExportJsonObject = groupAdmin;
            for(int j=0; j<createdGroupHotelUuids.size(); j++) {
                export.putToTempJsonObject("uuid", createdGroupHotelUuids.get(j));
                export.putToTempJsonObject("slug", createdGroupHotelSlugs.get(j));
                export.putToTempJsonObject("name", createdGroupHotelNames.get(j));
                export.addToTempJsonArray();
            }
            export.putArrayToMainJsonObject("hotels");

            // Set the export jsonObject to be the groupAdmin object
            export.exportFileCurrentData(getPathCurrentDataGroups() + groupSlug +"/" + "groupAdmin_" + groupAdmin.get("uuid"));

            // Export file to other tests
            export.exportFile(getTestDataPath() + "ExportLocationGroupAdmin.json");

            // Delete the Group Manager from pre-existing hotels
            for(int j=0; j<allGroupManagersPathsToBeRemoved.size(); j++) {
                report.appendExport("INFO: Removing the following files" + allGroupManagersPathsToBeRemoved.get(i));
                currentData.deleteHotelier(allGroupManagersPathsToBeRemoved.get(i));
            }
        }
        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        } else {
            getReport().appendExport("ERROR: Incorrect user type to create staff user.");
        }
    }

    public void logoutRouting(Logout logout) {
        if (login.getLoginUser().equals("staff-admin")) {
            staffLoginPage = logout.staffLoginPage;
            staffLoginPage.validatePage();
        }
    }

    public void setDataGroupPage() {
        createdGroupHotelName = "Group " + data.getRandomString(2, "lowercase");

        if (getDataToUse().equals("random")) {
            createdGroupName = createdGroupHotelName + " " + data.getRandomString(5, "lowercase");
            createdGroupDescription = data.getRandomString(15, "lowercase");
        }
        else {
            String[] createGroupValues = new String[]{"groupName", "groupDescription"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
        }
        createdGroupHotelUuids = new ArrayList<>();
        createdGroupHotelSlugs = new ArrayList<>();
        createdGroupHotelNames = new ArrayList<>();
        wholeFileJsonObjectArrayHotels = new ArrayList<>();
        wholeFileJsonObjectArrayManagers = new ArrayList<>();
        allGroupManagersPathsToBeRemoved = new ArrayList<>();

        // Loop through files here, get the hotel uuids needed and set it against the array
        File[] filesArray = new File( System.getProperty("user.dir") + "/src/testdata/" + getIteration() + "/" + getTestSuiteName() + "/" + getTestCaseName() + "/").listFiles();
        int hotelMatchForGroup = 0;
        int managerMatchForGroup = 0;
        for(int i=0;i<filesArray.length;i++) {
            if(filesArray[i].getName().startsWith(getTestData() + "Hotel")) {
                report.appendExport("INFO: HOTEL " + i);
                String tempPath = "src/" + filesArray[i].getPath().split("/src/")[1];
                wholeFileJsonObjectArrayHotels.add(data.returnJSONObject(tempPath));
                createdGroupHotelUuids.add(wholeFileJsonObjectArrayHotels.get(hotelMatchForGroup).get("uuid").toString());
                createdGroupHotelSlugs.add(wholeFileJsonObjectArrayHotels.get(hotelMatchForGroup).get("slug").toString());
                createdGroupHotelNames.add(wholeFileJsonObjectArrayHotels.get(hotelMatchForGroup).get("name").toString());
                hotelMatchForGroup = hotelMatchForGroup + 1;
            }
            if(filesArray[i].getName().startsWith(getTestData() + "Manager")) {
                report.appendExport("INFO: MANAGER " + i);
                String tempPath = "src/" + filesArray[i].getPath().split("/src/")[1];
                wholeFileJsonObjectArrayManagers.add(data.returnJSONObject(tempPath));
                managerMatchForGroup = managerMatchForGroup + 1;

                currentData.findHotelier("src/" + filesArray[i].getPath().split("/src/")[1]);
                // class jsonArray
                for(int j=0; j<currentData.getHotelPaths().size(); j++) {
                    allGroupManagersPathsToBeRemoved.add(currentData.getHotelPaths().get(j).toString());
                }
            }
        }
    }

    public void exportToExistingHotels() {
        // Get (group) slug
        AuthenticationRequests authenticationRequests = new AuthenticationRequests(report, getEnvironment());
        JSONObject token = authenticationRequests.postLoginUser(login.getLoginEmail(), login.getLoginPassword(), login.getLoginUser());
        JSONObject tokenResult = (JSONObject) token.get("result");
        String jwt = tokenResult.get("access_token").toString();

        HydraRequests hydraRequests = new HydraRequests(report, getEnvironment());
        JSONObject groups = hydraRequests.postSearchGroups(jwt, createdGroupName);
        JSONArray groupsResult = (JSONArray) groups.get("results");
        JSONObject group = (JSONObject) groupsResult.get(0);
        groupSlug = (String) group.get("slug");

        int hotelMatchForGroup = 0; // think this is not needed. i variable should just be fine
        for(int i=0; i<wholeFileJsonObjectArrayHotels.size();i++) {
            JSONObject tempWholeFileJsonObject = wholeFileJsonObjectArrayHotels.get(i);
            String newHotelName = createdGroupHotelNames.get(hotelMatchForGroup);
            tempWholeFileJsonObject.remove("name");
            tempWholeFileJsonObject.put("name", newHotelName);
            tempWholeFileJsonObject.put("groupName", createdGroupName);
            tempWholeFileJsonObject.put("groupSlug", groupSlug);
            export.setJsonObject(tempWholeFileJsonObject);
            export.exportFileCurrentData(getPathCurrentDataHotels() + createdGroupHotelSlugs.get(hotelMatchForGroup) + "/hotel_" + createdGroupHotelSlugs.get(hotelMatchForGroup));
            hotelMatchForGroup = hotelMatchForGroup + 1;
        }
        export.setJsonObject(new JSONObject());
    }

    public void addDataToExportFileForCreatedGroup() {
        export.putExportData("name", createdGroupName);
        export.putExportData("description", createdGroupDescription);
        export.putExportData("slug", groupSlug);
        for(int i=0; i< createdGroupHotelUuids.size(); i++) {
            export.putToTempJsonObject("uuid", createdGroupHotelUuids.get(i));
            export.putToTempJsonObject("slug", createdGroupHotelSlugs.get(i));
            export.putToTempJsonObject("name", createdGroupHotelNames.get(i));
            export.addToTempJsonArray();
        }
        export.putArrayToMainJsonObject("hotels");
    }

    public void createGroupAdminPassword() {
        groupAdminUuidAndTokens = new ArrayList<>();
        JSONArray messages = (JSONArray) commands.sqsReceiveMessage().get("Messages");;
        for(int i=0; i<messages.size(); i++) {
            JSONObject messagesObject = (JSONObject) messages.get(i);
            String body = messagesObject.get("Body").toString().replace("=\r\n", "");
            for(int j=0; j<wholeFileJsonObjectArrayManagers.size(); j++) {
                if((body.contains("Subject: Group Manager Authentication")) && (body.contains("To: " + wholeFileJsonObjectArrayManagers.get(j).get("name").toString() + " <" + wholeFileJsonObjectArrayManagers.get(j).get("email").toString() + ">"))) {
                    body = body.split(getEnvironment() + "/account/")[1];
                    // Add accountuuid/token to String array
                    groupAdminUuidAndTokens.add(body.split("\"")[0]);
                }
            }
        }
    }

    public void setDataForCreatedGroupAdminPassword() {
        if (getDataToUse().equals("random")) {
            createdGroupAdminUserPassword = genericMethods.getRandomPassword();
        }
        else {
            String[] editGroupAdminValues = new String[]{"password"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editGroupAdminDetails = data.returnJSONArray(getDataToUse(), editGroupAdminValues);
            createdGroupAdminUserPassword = editGroupAdminDetails.get(0).toString();
        }
    }
}
