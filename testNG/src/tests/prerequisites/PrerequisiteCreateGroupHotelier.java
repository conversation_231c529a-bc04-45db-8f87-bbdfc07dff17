import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import org.testng.annotations.Test;

import java.io.File;
import java.util.ArrayList;

/**
 *
 * This covers the Creation of Group Hotelier Users, as a pre-requisite test.
 *
 * Scenarios:
 * SC1: A Staff Admin user creating a new Group Hotelier
 *
 */
public class PrerequisiteCreateGroupHotelier extends BaseTest {

    // Required classes
    GenericMethods genericMethods;
    Login login;
    Export export;
    Assertion assertion;
    Commands commands;
    CurrentData currentData;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    GroupRatePlansPage groupRatePlansPage;
    GroupUsersPage groupUsersPage;
    GroupUsersCreatePage groupUsersCreatePage;
    HoteliersPage hoteliersPage;
    HoteliersEditPage hoteliersEditPage;

    String testDataLoginPath = "";
    String testDataExportPath = "";
//    String testDataHotelPath = "";
    String testDataRolePath = "";
    String testDataGroupPath = "";

    // Variables used throughout test
    String hotelName = "";
    String hotelSlug = "";
    String hotelUuid = "";
    String groupSlug = "";
    String groupName = "";
    JSONArray groupHotels;

    int hoteliers = 0;
    String createdHotelierUserName = "";
    String createdHotelierUserEmail = "";
    String createdHotelierUserRole = "";
    String createdHotelierUserUuid = "";
    String createdHotelierUserPassword = "";

    /**
     *
     * Prerequisite Create Group Hotelier Users
     *
     * Covers scenarios of:
     * SC1: A Staff Admin user creating a new Group Hotelier
     *
     */
    @Test(groups={"prerequisiteCreateGroupHotelier"})
    public void prerequisiteCreateGroupHotelier() {
        // Set test case name
        setTestCaseName("prerequisiteCreateGroupHotelier");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";
        testDataRolePath = getTestDataPath() + "Role.json";
        testDataGroupPath = getTestDataPath() + "Group.json";

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(),getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());
        commands = new Commands(getReport(), getAwsProfile());

        // Get the latest data that is needed for the test
        testDataGroupPath = currentData.getGroup(testDataGroupPath);

        // Set the data for the hotel
        setDataGroup();
        setDataHotel();

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Group Hotelier Users\n" +
                        " * Covers scenarios of:\n" +
                        " * SC1: A Staff Admin user creating a new Group Hotelier\n" +
                        " *****");

        // Start the test
        // Log in
        login.loginRouting();
        startOfTestPageRouting();
        // search for hotel
        hotelsPage.typeHotelNameInSearch(hotelName);
        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();

        // go to dashboard
        dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        dashboardPage.validatePage();

        // click group link
        groupRatePlansPage = dashboardPage.clickGroupPortalLink(groupSlug, groupName, hotelUuid);
        groupRatePlansPage.validatePage();

        // click staff, then click users
        groupRatePlansPage.clickStaffLink();
        groupUsersPage = groupRatePlansPage.clickUsersLink();
        groupUsersPage.validatePage();

        // click create new user
        groupUsersCreatePage = groupUsersPage.clickCreateNewUserButton();
        groupUsersCreatePage.validatePage();

        // Assert the page fields of the Group Users Create page
        assertPageFieldsForGroupUsersCreatePage();

        // Set the data for creating the hotelier user
        setDataForCreatingHotelierUser();
        // Insert the data into the page for creating a hotelier user
        typeDataForGroupUsersCreatePage();
        // Assert that the values inserted are actually there in the page for creating a hotelier user
        assertValuesInsertedForGroupsCreatePage();

        // Select all hotels
        groupUsersCreatePage.clickHotelsDoubleRightArrow();

        // Create the hotelier user
        // User is navigated back to the Hoteliers page
        groupUsersPage = groupUsersCreatePage.clickSave();
        groupUsersPage.validatePage();

        // Click drop down in top left
        // Loop through, check the name of the hotel
        // When found, click the hyperlink
        // Switch window (on dashboard)
        groupUsersPage.clickGroupViewDropDown();
        dashboardPage = groupUsersPage.clickHotelGoToProperty(hotelSlug, hotelName);
        hoteliersPage = dashboardPage.horizontalOptionsPage.navigateToHoteliersPage();
        hoteliersPage.validatePage();

        // Set the createdHotelierUserUuid
        setDataForCreatedHotelierUserUuid();

        // Add the data set to an export file
        addDataToExportFileForCreatedHotelierUser();

        // Export created staff user to the path current data staff.
        // Needed here anyway, as the user has been created
        for(int i=0; i<groupHotels.size(); i++) {
            JSONObject groupHotel = (JSONObject) groupHotels.get(i);
            export.exportFileCurrentData(getPathCurrentDataHotels() + groupHotel.get("slug").toString() + "/hoteliers/" + createdHotelierUserRole + "_" + createdHotelierUserUuid);
        }

        hoteliersEditPage = hoteliersPage.clickHotelierName(createdHotelierUserName, createdHotelierUserUuid);
        hoteliersEditPage.validatePage();

        // Edit the data for the hotelier. Just password for now
        setDataForCreatedHotelierPassword();

        // Insert the password
        hoteliersEditPage.editHotelierPassword(createdHotelierUserPassword);
        hoteliersEditPage.editHotelierPasswordConfirmation(createdHotelierUserPassword);

        // Assert that the values inserted are actually there
        assertValuesEditedForHoteliersEditPage();

        // Navigate to the hoteliers page
        hoteliersPage = hoteliersEditPage.clickSubmitButton();
        hoteliersPage.validateAfterEditPage(createdHotelierUserUuid);

        // Add the data set to an export file
        addDataToExportFileForEditedHotelierUser();

        // Export edited hotelier user to the areas needed (ie next test suite)
        export.exportFile(testDataExportPath);

        // Export edited hotelier user to the path current data staff
        for(int i=0; i<groupHotels.size(); i++) {
            JSONObject groupHotel = (JSONObject) groupHotels.get(i);
            export.exportFileCurrentData(getPathCurrentDataHotels() + groupHotel.get("slug").toString() + "/hoteliers/" + createdHotelierUserRole + "_" + createdHotelierUserUuid);
        }

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        } else {
            getReport().appendExport("ERROR: Incorrect user type to create staff user.");
        }
    }

    public void setDataGroup() {
        String[] groupValues = new String[]{"name", "slug", "hotels"};
        JSONArray groupDetails = data.returnJSONArray(testDataGroupPath, groupValues);
        groupName = groupDetails.get(0).toString();
        groupSlug = groupDetails.get(1).toString();
        groupHotels = (JSONArray) groupDetails.get(2);
    }

    public void setDataHotel() {
        JSONObject firstHotel = (JSONObject) groupHotels.get(0);
        hotelSlug = firstHotel.get("slug").toString();
        hotelName = firstHotel.get("name").toString();
        hotelUuid = firstHotel.get("uuid").toString();
    }

    public void assertPageFieldsForGroupUsersCreatePage() {
        assertion.assertElementExists(driver,groupUsersCreatePage.nameLocator);
        assertion.assertElementExists(driver,groupUsersCreatePage.emailLocator);
        assertion.assertElementExists(driver,groupUsersCreatePage.confirmEmailLocator);
        assertion.assertElementExists(driver,groupUsersCreatePage.roleLocator);
        assertion.assertElementExists(driver,groupUsersCreatePage.saveButtonLocator);
    }

    public void setDataForCreatingHotelierUser() {
        if (getDataToUse().equals("random")) {
            createdHotelierUserName = data.getRandomName("firstName") + " " + data.getRandomName("lastName") + " " + data.getRandomString(4,"any");
            createdHotelierUserEmail = data.getRandomString(10, "lowercase") + "@high-level-software.com";
            getReport().appendExport("INFO: Using random data.");
            // Parse the role details
            String[] roleValues = new String[]{"role"};
            JSONArray roleDetails = data.returnJSONArray(testDataRolePath, roleValues);
            createdHotelierUserRole = roleDetails.get(0).toString();
        }
        else {
            String[] createHotelierValues = new String[]{"name", "email", "role"};
            getReport().appendExport("INFO: Using data in file: " + getTestDataPath() + getDataToUse());
            JSONArray createHotelierDetails = data.returnJSONArray(getTestDataPath() + getDataToUse(), createHotelierValues);
            createdHotelierUserName = createHotelierDetails.get(0).toString();
            createdHotelierUserEmail = createHotelierDetails.get(1).toString();
            createdHotelierUserRole = createHotelierDetails.get(2).toString();
        }
    }

    public void typeDataForGroupUsersCreatePage() {
        groupUsersCreatePage.typeHotelierName(createdHotelierUserName);
        groupUsersCreatePage.typeHotelierRole(createdHotelierUserRole);
        groupUsersCreatePage.typeHotelierEmail(createdHotelierUserEmail);
        groupUsersCreatePage.typeHotelierConfirmationEmail(createdHotelierUserEmail);
    }

    public void assertValuesInsertedForGroupsCreatePage() {
        assertion.assertValueInField(driver, groupUsersCreatePage.nameLocator, createdHotelierUserName);
        assertion.assertAttributeTextInField(driver, groupUsersCreatePage.roleLocator, createdHotelierUserRole);
        assertion.assertValueInField(driver, groupUsersCreatePage.emailLocator, createdHotelierUserEmail);
        assertion.assertValueInField(driver, groupUsersCreatePage.confirmEmailLocator, createdHotelierUserEmail);
    }

    public void setDataForCreatedHotelierUserUuid() {
        createdHotelierUserUuid = hoteliersPage.returnHotelierUuidFromName(createdHotelierUserName);
    }

    public void addDataToExportFileForCreatedHotelierUser() {
        export.putExportData("name", createdHotelierUserName);
        export.putExportData("email", createdHotelierUserEmail);
        export.putExportData("role", createdHotelierUserRole);
        switch (createdHotelierUserRole) {
            case "Manager":
                export.putExportData("user", "hotelier-manager");
                break;
            case "Front Desk":
                export.putExportData("user", "hotelier-front-desk");
                break;
            case "Terminal":
                export.putExportData("user", "hotelier-terminal");
                break;
            case "Group Manager":
                export.putExportData("user", "group-manager");
                break;
        }
        export.putExportData("uuid", createdHotelierUserUuid);
        for(int i=0; i<groupHotels.size(); i++) {
            JSONObject groupHotel = (JSONObject) groupHotels.get(i);
            export.putToTempJsonObject("slug", groupHotel.get("slug").toString());
            export.putToTempJsonObject("name", groupHotel.get("name").toString());
            export.addToTempJsonArray();
        }
        export.putArrayToMainJsonObject("hotels");
    }

    public void setDataForCreatedHotelierPassword() {
        if (getDataToUse().equals("random")) {
            createdHotelierUserPassword = genericMethods.getRandomPassword();
        }
        else {
            String[] editStaffValues = new String[]{"password"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editStaffDetails = data.returnJSONArray(getDataToUse(), editStaffValues);
            createdHotelierUserPassword = editStaffDetails.get(0).toString();
        }
    }

    public void assertValuesEditedForHoteliersEditPage() {
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierNameLocator, createdHotelierUserName);
        if(!createdHotelierUserRole.equals("Group Manager")) {
            assertion.assertTextInDropDownField(driver, hoteliersEditPage.hotelierRoleLocator, createdHotelierUserRole);
        }
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierEmailLocator, createdHotelierUserEmail);
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierConfirmationEmailLocator, createdHotelierUserEmail);
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierPasswordLocator, createdHotelierUserPassword);
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierPasswordConfirmationLocator, createdHotelierUserPassword);
    }

    public void addDataToExportFileForEditedHotelierUser() {
        export.putExportData("name", createdHotelierUserName);
        export.putExportData("email", createdHotelierUserEmail);
        export.putExportData("password", createdHotelierUserPassword);
        export.putExportData("role", createdHotelierUserRole);
        switch (createdHotelierUserRole) {
            case "Manager":
                export.putExportData("user", "hotelier-manager");
                break;
            case "Front Desk":
                export.putExportData("user", "hotelier-front-desk");
                break;
            case "Terminal":
                export.putExportData("user", "hotelier-terminal");
                break;
            case "Group Manager":
                export.putExportData("user", "group-manager");
                break;
        }
        export.putExportData("uuid", createdHotelierUserUuid);
        for(int i=0; i<groupHotels.size(); i++) {
            JSONObject groupHotel = (JSONObject) groupHotels.get(i);
            export.putToTempJsonObject("slug", groupHotel.get("slug").toString());
            export.putToTempJsonObject("name", groupHotel.get("name").toString());
            export.addToTempJsonArray();
        }
        export.putArrayToMainJsonObject("hotels");
    }
}
