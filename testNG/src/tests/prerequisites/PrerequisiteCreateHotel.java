import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.testng.annotations.Test;

    /**
     *
     * This covers the Creation of Hotels, as a pre-requisite test.
     *
     * Scenarios:
     * SC1: A Staff Admin user creating a new hotel
     *
     */
public class PrerequisiteCreateHotel extends BaseTest {

    // Required classes
    GenericMethods genericMethods;
    Login login;
    Export exportCreatedHotel;
    Export exportCreatedHotelOptions;
    Export exportCreatedHotelCategories;
    Assertion assertion;
    HotelsPage hotelsPage;
    CreateHotelPage createHotelPage;
    DashboardPage dashboardPage;

    String testDataLoginPath = "";
    String testDataExportPathCreatedHotel = "";
    String testDataExportPathCreatedHotelOptions = "";
    String testDataExportPathCreatedHotelCategories = "";

    // Variables used throughout test
    String status = "";
    String slug = "";
    String name = "";
    String address = "";
    String postcode = "";
    String country = "";
    String phone = "";
    String email = "";
    String description = "";
    String privacyLink = "";
    String termsLink = "";
    String cancellationLink = "";
    String checkinFrom = "";
    String checkoutBy = "";
    String taxRate = "";
    String taxNumber = "";
    String hotelUuid = "";
    String hotelId = "";

        /**
     *
     * Prerequisite Create Hotel.
     *
     * Covers scenarios of:
     * SC1: A Staff Admin user creating a new hotel
     *
     */
    @Test(groups={"prerequisiteCreateHotel"})
    public void prerequisiteCreateHotel() {
        // Set test case name
        setTestCaseName("prerequisiteCreateHotel");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPathCreatedHotel = getTestDataPath() + "ExportPathCreatedHotel.json";
        testDataExportPathCreatedHotelOptions = getTestDataPath() + "ExportPathCreatedHotelOptions.json";
        testDataExportPathCreatedHotelCategories = getTestDataPath() + "ExportPathCreatedHotelCategories.json";

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        exportCreatedHotel = new Export();
        exportCreatedHotelOptions = new Export();
        exportCreatedHotelCategories = new Export();
        assertion = new Assertion(getReport());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Hotel\n" +
                        " * Covers scenarios of:\n" +
                        " * SC1: A Staff Admin user creating a new hotel\n" +
                        " *****");

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Navigate to Create Hotel page
        createHotelPage = hotelsPage.clickCreateHotelLink();
        createHotelPage.validatePage();

        // Set the data for the test
        setDataCreateHotelPage();
        // Insert the data into page for the test
        typeDataCreateHotelPage();
        // Assert that the values inserted are actually there
        assertCreateHotelPageValuesInserted();

        // Navigate to Dashboard page
        dashboardPage = createHotelPage.clickSubmit();
        dashboardPage.validatePage();

        // Get the hotel uuid and set it in a variable
        setHotelUuid();

        // Add the data set to an export file
        addDataToExportCreatedHotelFile();
        addDataToExportCreatedHotelOptionsFile();
        addDataToExportCreatedHotelCategoriesFile();

        // Export the file
        exportCreatedHotel.exportFile(testDataExportPathCreatedHotel);
        exportCreatedHotelOptions.exportFile(testDataExportPathCreatedHotelOptions);
        exportCreatedHotelCategories.exportFile(testDataExportPathCreatedHotelCategories);

        // Export current data file
        exportCreatedHotel.exportFileCurrentData(getPathCurrentDataHotels() + slug + "/hotel_" + slug);
        exportCreatedHotelOptions.exportFileCurrentData(getPathCurrentDataHotels() + slug + "/options_" + slug);
        exportCreatedHotelCategories.exportFileCurrentData(getPathCurrentDataHotels() + slug + "/categories_" + slug);

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        } else {
            getReport().appendExport("ERROR: Incorrect user type to create hotel.");
        }
    }

    /*
     * Set the data to use to be random data, data from a file or data in a chain for a chain of tests
     *
     */
    public void setDataCreateHotelPage() {
        if (getDataToUse().equals("random")) {
            status = "Live";
            slug = data.getRandomString(10, "lowercase");
            name = data.getRandomName("hotelName") + "_" + data.getRandomString(5, "numeric");
            JSONObject randomAddress = data.getRandomAddress();
            address = randomAddress.get("line1").toString();
            postcode = randomAddress.get("postcode").toString();
            country = randomAddress.get("country").toString();
            phone = data.getRandomString(10, "numeric");
            email = data.getRandomString(10, "lowercase") + "@high-level-software.com";
            description = data.getRandomString(50, "any");
            privacyLink = "https://www.high-level-software.com";
            termsLink = data.getRandomString(50, "any");
            cancellationLink = data.getRandomString(50, "any");
            checkinFrom = data.getRandomMinutes("AM");
            checkoutBy = data.getRandomMinutes("PM");
            taxRate = "20";
            taxNumber = data.getRandomString(5, "numeric");
        }
        else {
            String[] createHotelValues = new String[]{"status", "slug", "name", "address", "postcode","country", "phone", "email", "description",
                    "privacyLink", "termsLink", "cancellationLink", "checkinFrom", "checkoutBy",
                    "taxRate", "taxNumber"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray createHotelDetails = data.returnJSONArray(getDataToUse(), createHotelValues);
            status = createHotelDetails.get(0).toString();
            slug = createHotelDetails.get(1).toString();
            name = createHotelDetails.get(2).toString();
            address = createHotelDetails.get(3).toString();
            postcode = createHotelDetails.get(4).toString();
            country = createHotelDetails.get(5).toString();
            phone = createHotelDetails.get(6).toString();
            email = createHotelDetails.get(7).toString();
            description = createHotelDetails.get(8).toString();
            privacyLink = createHotelDetails.get(9).toString();
            termsLink = createHotelDetails.get(10).toString();
            cancellationLink = createHotelDetails.get(11).toString();
            checkinFrom = createHotelDetails.get(12).toString();
            checkoutBy = createHotelDetails.get(13).toString();
            taxRate = createHotelDetails.get(14).toString();
            taxNumber = createHotelDetails.get(15).toString();
        }
    }

    public void addDataToExportCreatedHotelFile() {
        exportCreatedHotel.putExportData("status", status);
        exportCreatedHotel.putExportData("slug", slug);
        exportCreatedHotel.putExportData("uuid", hotelUuid);
        exportCreatedHotel.putExportData("id", hotelId);
        exportCreatedHotel.putExportData("name", name);
        exportCreatedHotel.putExportData("address", address);
        exportCreatedHotel.putExportData("postcode", postcode);
        exportCreatedHotel.putExportData("country", country);
        exportCreatedHotel.putExportData("phone", phone);
        exportCreatedHotel.putExportData("email", email);
        exportCreatedHotel.putExportData("description", description);
        exportCreatedHotel.putExportData("privacyLink", privacyLink);
        exportCreatedHotel.putExportData("termsLink", termsLink);
        exportCreatedHotel.putExportData("cancellationLink", cancellationLink);
        exportCreatedHotel.putExportData("checkinFrom", checkinFrom);
        exportCreatedHotel.putExportData("checkoutBy", checkoutBy);
        exportCreatedHotel.putExportData("taxRate", taxRate);
        exportCreatedHotel.putExportData("taxNumber", taxNumber);
//        exportCreatedHotel.putArrayToMainJsonObject("hoteliers");
    }

    public void addDataToExportCreatedHotelOptionsFile() {
        exportCreatedHotelOptions.putToTempJsonObject("name", "availability.occupancy.max");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "availability.rates.noToggle");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "booking.regform");
        exportCreatedHotelOptions.putToTempJsonObject("value", "Passport Number\nCar Registration");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "booking.regform.prices");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "calendar.display");
        exportCreatedHotelOptions.putToTempJsonObject("value", "ALL_TYPES_WITH_PRIMARY_ROOMS");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "invoice.corporate.full-breakdown");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "invoice.guest.full-breakdown");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.booking.confirmation.log");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.booking.invoice.log");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.guest.booking.confirmation");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.hotel.booking.confirmation");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.rdx.guest.booking.confirmation");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.rdx.hotel.booking.confirmation");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putArrayToMainJsonObject("options");
    }

    public void addDataToExportCreatedHotelCategoriesFile() {
        exportCreatedHotelCategories.putToTempJsonObject("code", "food");
        exportCreatedHotelCategories.putToTempJsonObject("title", "Food");
        exportCreatedHotelCategories.putToTempJsonObject("color", "FF6961");
        exportCreatedHotelCategories.addToTempJsonArray();
        exportCreatedHotelCategories.putToTempJsonObject("code", "drink");
        exportCreatedHotelCategories.putToTempJsonObject("title", "Drink");
        exportCreatedHotelCategories.putToTempJsonObject("color", "77DD77");
        exportCreatedHotelCategories.addToTempJsonArray();
        exportCreatedHotelCategories.putToTempJsonObject("code", "miscellaneous");
        exportCreatedHotelCategories.putToTempJsonObject("title", "Miscellaneous");
        exportCreatedHotelCategories.putToTempJsonObject("color", "AEC6CF");
        exportCreatedHotelCategories.addToTempJsonArray();
        exportCreatedHotelCategories.putToTempJsonObject("code", "accommodation");
        exportCreatedHotelCategories.putToTempJsonObject("title", "Accommodation");
        exportCreatedHotelCategories.putToTempJsonObject("color", "FDFD96");
        exportCreatedHotelCategories.addToTempJsonArray();
        exportCreatedHotelCategories.putArrayToMainJsonObject("categories");
    }

    public void typeDataCreateHotelPage() {
        createHotelPage.selectStatus(status);
        createHotelPage.typeSlug(slug);
        createHotelPage.typeName(name);
        createHotelPage.typeAddress(address);
        createHotelPage.typePostcode(postcode);
        createHotelPage.typeCountry(country);
        createHotelPage.typePhone(phone);
        createHotelPage.typeEmail(email);
        createHotelPage.typeDescription(description);
        createHotelPage.typePrivacyLink(privacyLink);
        createHotelPage.typeTermsLink(termsLink);
        createHotelPage.typeCancellationLink(cancellationLink);
        createHotelPage.clearCheckinFrom();
        createHotelPage.typeCheckinFrom(checkinFrom);
        createHotelPage.clearCheckoutBy();
        createHotelPage.typeCheckoutBy(checkoutBy);
        createHotelPage.typeTaxRate(taxRate);
        createHotelPage.typeTaxNumber(taxNumber);
    }

    public void assertCreateHotelPageValuesInserted() {
        assertion.assertValueInField(driver, createHotelPage.statusLocator, status.toLowerCase());
        assertion.assertValueInField(driver, createHotelPage.slugLocator, slug);
        assertion.assertValueInField(driver, createHotelPage.nameLocator, name);
        assertion.assertValueInField(driver, createHotelPage.addressLocator, address);
        assertion.assertValueInField(driver, createHotelPage.postcodeLocator, postcode);
        assertion.assertValueInField(driver, createHotelPage.countryLocator, country);
        assertion.assertValueInField(driver, createHotelPage.phoneLocator, phone);
        assertion.assertValueInField(driver, createHotelPage.emailLocator, email);
        assertion.assertValueInField(driver, createHotelPage.descriptionLocator, description);
        assertion.assertValueInField(driver, createHotelPage.privacyLinkLocator, privacyLink);
        assertion.assertValueInField(driver, createHotelPage.termsLocator, termsLink);
        assertion.assertValueInField(driver, createHotelPage.cancellationLocator, cancellationLink);
        assertion.assertValueInField(driver, createHotelPage.checkinFromLocator, checkinFrom);
        assertion.assertValueInField(driver, createHotelPage.checkoutByLocator, checkoutBy);
        assertion.assertValueInField(driver, createHotelPage.taxRateLocator, taxRate);
        assertion.assertValueInField(driver, createHotelPage.taxNumberLocator, taxNumber);
    }

    public void setHotelUuid() {
        AuthenticationRequests authenticationRequests = new AuthenticationRequests(report, getEnvironment());
        JSONObject token = authenticationRequests.postLoginUser(login.getLoginEmail(), login.getLoginPassword(), login.getLoginUser());
        JSONObject tokenResult = (JSONObject) token.get("result");
        String jwt = tokenResult.get("access_token").toString();

        HydraRequests hydraRequests = new HydraRequests(report, getEnvironment());
        JSONObject hotels = hydraRequests.postHotelsSearch(jwt, name);
        JSONArray hotelsResult = (JSONArray) hotels.get("results");
        for(int i=0; i<hotelsResult.size(); i++) {
            JSONObject hotel;
            hotel = (JSONObject) hotelsResult.get(i);
            String hotelSlug = (String) hotel.get("slug");
            if(hotelSlug.equals(slug)) {
                hotelUuid = (String) hotel.get("uuid");
                hotelId = hotel.get("id").toString();
            }
        }
    }
}