import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.junit.Assert;
import org.testng.annotations.Test;

import java.io.File;
import java.util.ArrayList;

/**
 *
 * This covers the Creation of Rooms, as a pre-requisite test.
 *
 * Scenarios:
 * SC1: A Staff Admin user creating a new Room
 *
 */
public class PrerequisiteCreateRoom extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    CurrentData currentData;
    Login login;
    Export export;
    Assertion assertion;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    RoomTypesPage roomTypesPage;
    RoomsCreatePage roomsCreatePage;

    String testDataLoginPath = "";
    String testDataExportPath = "";
    String testDataHotelPath = "";
//    String testDataRoomTypePath = "";

    // Variables used throughout test
    String hotelName = "";
    String hotelSlug = "";
    int roomTypes = 0;

    String createdRoomName = "";
    String createdRoomDirections = "";
    ArrayList<String> createdRoomRoomType;
    ArrayList<String> createdRoomRoomTypeFileName;
    ArrayList<JSONObject> wholeFileJsonObjectArrayRoomType;

    /**
     *
     * Prerequisite Create Room
     *
     * Covers scenarios of:
     * SC1: A Staff Admin user creating a new Room
     *
     */
    @Test(groups = {"prerequisiteCreateRoom"})
    public void prerequisiteCreateRoom() throws InterruptedException {
        // Set test case name
        setTestCaseName("prerequisiteCreateRoom");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";
        testDataHotelPath = getTestDataPath() + "Hotel.json";
//        testDataRoomTypePath = getTestDataPath() + "RoomType.json";

        // Set the data for the hotel
        setDataHotel();
        setDataForCreatingRoom();

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Room Type\n" +
                        " * Covers scenarios of:\n" +
                        " * SC1: A Staff Admin user creating a new Room\n" +
                        " *****");

        // Start the test
        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        hotelsPage.typeHotelNameInSearch(hotelName);
        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        dashboardPage.validatePage();

        // Retrieve room types count at last possible moment. Needed due to change in behaviour when a room type already exists for a hotel
        roomTypes =  currentData.getRoomTypeCount(testDataHotelPath);
        getReport().appendExport("INFO: Room Types count: " + roomTypes);
        if(roomTypes == 0) {
            report.appendExport("INFO: No room types.");
            Assert.fail();

        }

        roomTypesPage = dashboardPage.horizontalOptionsPage.navigateToRoomTypesPage();
        roomTypesPage.validatePage();

        roomsCreatePage = roomTypesPage.clickAddRoom();
        roomsCreatePage.validatePage();

        // Check the fields to insert room type data exist
        assertPageFieldsForRoomPage();

        // Enter room type details
        typeDataForRoomPage();

        // Drag first room type to top
        dragFirstRoomTypeToTop();

        // Check that the values inserted are actually inserted correctly
        assertValuesInsertedForRoomPage();

        // Create the room type
        roomTypesPage = roomsCreatePage.clickSubmit();


        roomTypesPage.validatePage();

        // Add the data set to an export file
        addDataToExportFileForCreatedRoom();

        // Export room types to the areas needed (ie next test suite)
        export.exportFile(testDataExportPath);

        // Export room type to the path current data
        export.exportFileCurrentData(getPathCurrentDataHotels() + hotelSlug + "/rooms/" + createdRoomName);
        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        } else {
            getReport().appendExport("ERROR: Incorrect user type to create staff user.");
        }
    }

    public void setDataHotel() {
        String[] hotelValues = new String[]{"slug", "name"};
        JSONArray hotelDetails = data.returnJSONArray(testDataHotelPath, hotelValues);
        hotelSlug = hotelDetails.get(0).toString();
        hotelName = hotelDetails.get(1).toString();
    }

    public void setDataForCreatingRoom() {
        if (getDataToUse().equals("random")) {
            getReport().appendExport("INFO: Using random data.");
            createdRoomName = data.getRandomString(2, "numeric");
            createdRoomDirections = "Find room: " + createdRoomName + " over there! " + data.getRandomString(3, "lowercase");

            // Add all roomTypes here
            createdRoomRoomType = new ArrayList<>();
            createdRoomRoomTypeFileName = new ArrayList<>();
            wholeFileJsonObjectArrayRoomType = new ArrayList<>();

            File[] filesArray = new File( System.getProperty("user.dir") + "/src/testdata/" + getIteration() + "/" + getTestSuiteName() + "/" + getTestCaseName() + "/").listFiles();
            int roomTypeMatchForRoom = 0;
            for(int i=0;i<filesArray.length;i++) {
                if(filesArray[i].getName().startsWith(getTestData() + "RoomType")) {
                    report.appendExport("INFO: HOTEL " + i);
                    String tempPath = "src/" + filesArray[i].getPath().split("/src/")[1];
                    wholeFileJsonObjectArrayRoomType.add(data.returnJSONObject(tempPath));
                    createdRoomRoomTypeFileName.add(filesArray[i].getName());
                    createdRoomRoomType.add(wholeFileJsonObjectArrayRoomType.get(roomTypeMatchForRoom).get("title").toString());
                    roomTypeMatchForRoom = roomTypeMatchForRoom + 1;
                }
            }
        }
        else {
            String[] createRoomValues = new String[]{"name", "directions", "title"};
            getReport().appendExport("INFO: Using data in file: " + getTestDataPath() + getDataToUse());
            JSONArray createRoomDetails = data.returnJSONArray(getTestDataPath() + getDataToUse(), createRoomValues);
            createdRoomName = createRoomDetails.get(0).toString();
            createdRoomDirections = createRoomDetails.get(1).toString();
            createdRoomRoomType.add(createRoomDetails.get(2).toString()); // Might be best a loop here..
        }
    }

    public void assertPageFieldsForRoomPage() {
        assertion.assertElementExists(driver,roomsCreatePage.roomNoLocator);
        assertion.assertElementExists(driver,roomsCreatePage.directionsLocator);
        assertion.assertElementExists(driver,roomsCreatePage.listOfRoomTypeOptions);

    }

    public void typeDataForRoomPage() {
        roomsCreatePage.typeRoomNo(createdRoomName);
        roomsCreatePage.typeDirections(createdRoomDirections);
        for(int i=0; i<createdRoomRoomType.size(); i++) {
            roomsCreatePage.selectRoomType(createdRoomRoomType.get(i));
        }
    }

    public void dragFirstRoomTypeToTop() {
        int firstRoomType=0;
        for(int i=0; i<createdRoomRoomTypeFileName.size(); i++) {
            if(createdRoomRoomTypeFileName.get(i).contains("RoomType-1.")) {
                firstRoomType=i;
                break;
            }
        }

        // Only move first room type to the top
//        for(int i=createdRoomRoomType.size()-1; i>=0; i--) {
        roomsCreatePage.dragRoomTypeToTop(createdRoomRoomType.get(firstRoomType));
//        }
    }

    public void assertValuesInsertedForRoomPage() {
        assertion.assertValueInField(driver, roomsCreatePage.roomNoLocator, createdRoomName);
        assertion.assertValueInField(driver, roomsCreatePage.directionsLocator, createdRoomDirections);
        for(int i=0; i< createdRoomRoomType.size(); i++) {
            assertion.assertTickBoxStateByWebElement(roomsCreatePage.getRoomTypeCheckBox(createdRoomRoomType.get(i)), true);
        }
    }

    public void addDataToExportFileForCreatedRoom() {
        export.putExportData("name", createdRoomName);
        export.putExportData("directions", createdRoomDirections);
        for(int i=0; i< createdRoomRoomType.size(); i++) {
            export.putToTempJsonObject("title", createdRoomRoomType.get(i));
            export.addToTempJsonArray();
        }
        export.putArrayToMainJsonObject("titles");
    }
}