import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.testng.annotations.Test;

public class PrerequisiteCreateCorporation extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    CurrentData currentData;
    Login login;
    Export export;
    Assertion assertion;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    CorporationsPage corporationsPage;
    CorporationsCreatePage corporationsCreatePage;
    CorporationsProfilePage corporationsProfilePage;
    CorporationsEditPage corporationsEditPage;

    String testDataLoginPath = "";
    String testDataExportPath = "";
    String testDataHotelPath = "";

    // Variables used throughout test
    String hotelName = "";
    String hotelSlug = "";
    String createdCorporationUserName = "";
    String createdCorporationContactName = "";
    String createdCorporationAddress = "";
    String createdCorporationPostcode = "";
    String createdCorporationCountry = "";
    String createdCorporationUserPhone = "";
    String createdCorporationUserEmail = "";
    String createdCorporationNumDaysGrace = "";
    String createdCorporationUserNotes = "";
    String createdCorporationUserMarketingEmail = "";
    boolean createdCorporationUserMarketingEmailState = false;
    String createdCorporationUserMarketingMail = "";
    boolean createdCorporationUserMarketingMailState = false;
    String createdCorporationUserMarketingSms = "";
    boolean createdCorporationUserMarketingSmsState = false;
    String createdCorporationUserUuid = "";
    String createdCorporationUserPassword = "";

    @Test(groups = {"prerequisiteCreateCorporation"})
    public void prerequisiteCreateCorporation() {
        // Set test case name
        setTestCaseName("prerequisiteCreateCorporation");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";
        testDataHotelPath = getTestDataPath() + "Hotel.json";

        // Set the data for the hotel
        setDataHotel();

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Corporation User\n" +
                        " * Covers AC of:\n" +
                        " * AC1: A Staff Admin user must be able to create a new Corporation user\n" +
                        " *****");

        // Start the test
        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        hotelsPage.typeHotelNameInSearch(hotelName);
        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        dashboardPage.validatePage();

        // Navigate to corporations page
        corporationsPage = dashboardPage.horizontalOptionsPage.navigateToCorporationsPage();
        corporationsPage.validatePage();

        // Navigate to create corporation page
        corporationsCreatePage = corporationsPage.clickCreateCorporation();
        corporationsCreatePage.validatePage();

        // Assert the page fields of the Corporations Create page
        assertPageFieldsForCorporationsCreatePage();

        // Set the data for creating the corporation user
        setDataForCreatingCorporationUser();
        // Insert the data into the page for creating a corporation user
        typeDataForCorporationsCreatePage();
        // Assert that the values inserted are actually there in the page for creating a corporation user
        assertValuesInsertedForCorporationsCreatePage();

        corporationsPage = corporationsCreatePage.clickCorporationSubmit();
        corporationsPage.validatePage();

        // Set the createdCorporationUserUuid
        setDataForCreatedCorporationUserUuid();

        // Add the data set to an export file
        addDataToExportFileForCreatedCorporationUser();

        // Export created corporation user to the path current data corporation.
        // Needed here anyway, as the user has been created
        export.exportFileCurrentData(getPathCurrentDataHotels() + hotelSlug + "/corporations/" + "corporation" + "_" + createdCorporationUserUuid);

        corporationsProfilePage = corporationsPage.clickCorporationName(createdCorporationUserName, createdCorporationContactName, createdCorporationUserUuid);
        corporationsProfilePage.validatePage();

        corporationsEditPage = corporationsProfilePage.clickEditCorporation();
        corporationsEditPage.validatePage();

        // Edit the data for the corporation. Just password for now
        setDataForCreatedCorporationPassword();

        // Insert the password
        corporationsEditPage.typeCorporationPassword(createdCorporationUserPassword);
        corporationsEditPage.typeCorporationPasswordConfirmation(createdCorporationUserPassword);

        // Assert that the values inserted are actually there
        assertValuesEditedForCorporationsEditPage();

        // Navigate to the corporation profile page
        corporationsProfilePage = corporationsEditPage.clickCorporationSubmit();
        corporationsProfilePage.validatePage();

        // Add the data set to an export file
        addDataToExportFileForEditedCorporationUser();

        // Export edited corporation user to the areas needed (ie next test suite)
        export.exportFile(testDataExportPath);

        // Export edited corporation user to the path current data corporation
        export.exportFileCurrentData(getPathCurrentDataHotels() + hotelSlug + "/corporations/" + "corporation" + "_" + createdCorporationUserUuid);

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        } else {
            getReport().appendExport("ERROR: Incorrect user type to create corporation user.");
        }
    }

    public void setDataHotel() {
        String[] hotelValues = new String[]{"slug", "name"};
        JSONArray hotelDetails = data.returnJSONArray(testDataHotelPath, hotelValues);
        hotelSlug = hotelDetails.get(0).toString();
        hotelName = hotelDetails.get(1).toString();
    }

    public void assertPageFieldsForCorporationsCreatePage() {
        assertion.assertElementExists(driver,corporationsCreatePage.corporationNameLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationContactNameLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationAddressLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationPostcodeLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationCountryDropdownLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationPhoneLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationEmailLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationEmailConfirmationLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationNumDaysGraceLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationNotesLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationMarketingConsentEmailLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationMarketingConsentMailLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationMarketingConsentSmsLocator);
        assertion.assertElementExists(driver,corporationsCreatePage.corporationSubmitButtonLocator);
    }

    public void setDataForCreatingCorporationUser() {
        if (getDataToUse().equals("random")) {
            createdCorporationUserName = "Corporation" + " " + data.getRandomName("lastName") + " " + data.getRandomString(4,"any");
            createdCorporationContactName = data.getRandomName("firstName") + " " + data.getRandomName("lastName") + " " + data.getRandomString(4,"any");
            JSONObject randomAddress = data.getRandomAddress();
            createdCorporationAddress = randomAddress.get("line1").toString();
            createdCorporationPostcode = randomAddress.get("postcode").toString();
            createdCorporationCountry = randomAddress.get("country").toString();
            createdCorporationUserPhone = data.getRandomString(10, "numeric");
            createdCorporationUserEmail = data.getRandomString(10, "lowercase") + "@high-level-software.com";
            createdCorporationNumDaysGrace = data.getRandomString(1, "numeric");
            createdCorporationUserNotes = data.getRandomString(20, "any");
            createdCorporationUserMarketingEmail = "true";
            createdCorporationUserMarketingMail = "false";
            createdCorporationUserMarketingSms = "false";
            getReport().appendExport("INFO: Using random data.");
        }
        else {
            String[] createCorporationValues = new String[]{"name", "contactName", "address", "postcode", "country", "phone", "email", "numDaysGrace", "notes", "marketingEmail", "marketingMail", "marketingSms"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray createCorporationDetails = data.returnJSONArray(getDataToUse(), createCorporationValues);
            createdCorporationUserName = createCorporationDetails.get(0).toString();
            createdCorporationContactName = createCorporationDetails.get(1).toString();
            createdCorporationAddress = createCorporationDetails.get(2).toString();
            createdCorporationPostcode = createCorporationDetails.get(3).toString();
            createdCorporationCountry = createCorporationDetails.get(4).toString();
            createdCorporationUserPhone = createCorporationDetails.get(5).toString();
            createdCorporationUserEmail = createCorporationDetails.get(6).toString();
            createdCorporationNumDaysGrace = createCorporationDetails.get(7).toString();
            createdCorporationUserNotes = createCorporationDetails.get(8).toString();
            createdCorporationUserMarketingEmail = createCorporationDetails.get(9).toString();
            createdCorporationUserMarketingMail = createCorporationDetails.get(10).toString();
            createdCorporationUserMarketingSms = createCorporationDetails.get(11).toString();
        }
    }

    public void typeDataForCorporationsCreatePage() {
        corporationsCreatePage.typeCorporationName(createdCorporationUserName);
        corporationsCreatePage.typeCorporationContactName(createdCorporationContactName);
        corporationsCreatePage.typeCorporationAddress(createdCorporationAddress);
        corporationsCreatePage.typeCorporationPostcode(createdCorporationPostcode);
        corporationsCreatePage.selectCorporationCountry(createdCorporationCountry);
        corporationsCreatePage.typeCorporationPhone(createdCorporationUserPhone);
        corporationsCreatePage.typeCorporationEmail(createdCorporationUserEmail);
        corporationsCreatePage.typeCorporationEmailConfirmation(createdCorporationUserEmail);
        corporationsCreatePage.typeCorporationNumDaysGrace(createdCorporationNumDaysGrace);
        corporationsCreatePage.typeCorporationNotes(createdCorporationUserNotes);
        if(createdCorporationUserMarketingEmail.equals("true")) {
            createdCorporationUserMarketingEmailState = true;
            corporationsCreatePage.tickCorporationEmail();
        }
        if(createdCorporationUserMarketingMail.equals("true")) {
            createdCorporationUserMarketingMailState = true;
            corporationsCreatePage.tickCorporationMail();
        }
        if(createdCorporationUserMarketingSms.equals("true")) {
            createdCorporationUserMarketingSmsState = true;
            corporationsCreatePage.tickCorporationtSms();
        }
    }

    public void assertValuesInsertedForCorporationsCreatePage() {
        assertion.assertValueInField(driver, corporationsCreatePage.corporationNameLocator, createdCorporationUserName);
        assertion.assertValueInField(driver, corporationsCreatePage.corporationContactNameLocator, createdCorporationContactName);
        assertion.assertValueInField(driver, corporationsCreatePage.corporationAddressLocator, createdCorporationAddress);
        assertion.assertValueInField(driver, corporationsCreatePage.corporationPostcodeLocator, createdCorporationPostcode);
        assertion.assertValueInField(driver, corporationsCreatePage.corporationCountryDropdownLocator, createdCorporationCountry);
        assertion.assertValueInField(driver, corporationsCreatePage.corporationPhoneLocator, createdCorporationUserPhone);
        assertion.assertValueInField(driver, corporationsCreatePage.corporationEmailLocator, createdCorporationUserEmail);
        assertion.assertValueInField(driver, corporationsCreatePage.corporationEmailConfirmationLocator, createdCorporationUserEmail);
        assertion.assertValueInField(driver, corporationsCreatePage.corporationNumDaysGraceLocator, createdCorporationNumDaysGrace);
        assertion.assertValueInField(driver, corporationsCreatePage.corporationNotesLocator, createdCorporationUserNotes);
        assertion.assertTickBoxState(driver, corporationsCreatePage.corporationMarketingConsentEmailLocator, createdCorporationUserMarketingEmailState);
        assertion.assertTickBoxState(driver, corporationsCreatePage.corporationMarketingConsentMailLocator, createdCorporationUserMarketingMailState);
        assertion.assertTickBoxState(driver, corporationsCreatePage.corporationMarketingConsentSmsLocator, createdCorporationUserMarketingSmsState);
    }

    public void setDataForCreatedCorporationUserUuid() {
        createdCorporationUserUuid = corporationsPage.returnCorporationUuidFromName(createdCorporationUserName);
    }

    public void addDataToExportFileForCreatedCorporationUser() {
        export.putExportData("name", createdCorporationUserName);
        export.putExportData("contactName", createdCorporationContactName);
        export.putExportData("address", createdCorporationAddress);
        export.putExportData("postcode", createdCorporationPostcode);
        export.putExportData("country", createdCorporationCountry);
        export.putExportData("phone", createdCorporationUserPhone);
        export.putExportData("email", createdCorporationUserEmail);
        export.putExportData("numDaysGrace", createdCorporationNumDaysGrace);
        export.putExportData("notes", createdCorporationUserNotes);
        export.putExportData("marketingEmail", createdCorporationUserMarketingEmail);
        export.putExportData("marketingMail", createdCorporationUserMarketingMail);
        export.putExportData("marketingSms", createdCorporationUserMarketingSms);
        export.putExportData("user", "corporation");
        export.putExportData("uuid", createdCorporationUserUuid);
        export.putToTempJsonObject("slug", hotelSlug);
        export.putToTempJsonObject("name", hotelName);
        export.addToTempJsonArray();
        export.putArrayToMainJsonObject("hotels");
    }

    public void setDataForCreatedCorporationPassword() {
        if (getDataToUse().equals("random")) {
            createdCorporationUserPassword = genericMethods.getRandomPassword();
        }
        else {
            String[] editCorporationValues = new String[]{"password"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editCorporationDetails = data.returnJSONArray(getDataToUse(), editCorporationValues);
            createdCorporationUserPassword = editCorporationDetails.get(0).toString();
        }
    }

    public void assertValuesEditedForCorporationsEditPage() {
        assertion.assertValueInField(driver, corporationsEditPage.corporationNameLocator, createdCorporationUserName);
        assertion.assertValueInField(driver, corporationsEditPage.corporationContactNameLocator, createdCorporationContactName);
        assertion.assertValueInField(driver, corporationsEditPage.corporationAddressLocator, createdCorporationAddress);
        assertion.assertValueInField(driver, corporationsEditPage.corporationPostcodeLocator, createdCorporationPostcode);
        assertion.assertValueInField(driver, corporationsEditPage.corporationCountryDropdownLocator, createdCorporationCountry);
        assertion.assertValueInField(driver, corporationsEditPage.corporationPhoneLocator, createdCorporationUserPhone);
        assertion.assertValueInField(driver, corporationsEditPage.corporationEmailLocator, createdCorporationUserEmail);
        assertion.assertValueInField(driver, corporationsEditPage.corporationEmailConfirmationLocator, createdCorporationUserEmail);
        assertion.assertValueInField(driver, corporationsEditPage.corporationPasswordLocator, createdCorporationUserPassword);
        assertion.assertValueInField(driver, corporationsEditPage.corporationPasswordConfirmationLocator, createdCorporationUserPassword);
        assertion.assertValueInField(driver, corporationsEditPage.corporationNumDaysGraceLocator, createdCorporationNumDaysGrace);
        assertion.assertValueInField(driver, corporationsEditPage.corporationNotesLocator, createdCorporationUserNotes);
        assertion.assertTickBoxState(driver, corporationsEditPage.corporationMarketingConsentEmailLocator, createdCorporationUserMarketingEmailState);
        assertion.assertTickBoxState(driver, corporationsEditPage.corporationMarketingConsentMailLocator, createdCorporationUserMarketingMailState);
        assertion.assertTickBoxState(driver, corporationsEditPage.corporationMarketingConsentSmsLocator, createdCorporationUserMarketingSmsState);
    }

    public void addDataToExportFileForEditedCorporationUser() {
        export.putExportData("name", createdCorporationUserName);
        export.putExportData("contactName", createdCorporationContactName);
        export.putExportData("address", createdCorporationAddress);
        export.putExportData("postcode", createdCorporationPostcode);
        export.putExportData("country", createdCorporationCountry);
        export.putExportData("phone", createdCorporationUserPhone);
        export.putExportData("email", createdCorporationUserEmail);
        export.putExportData("password", createdCorporationUserPassword);
        export.putExportData("numDaysGrace", createdCorporationNumDaysGrace);
        export.putExportData("notes", createdCorporationUserNotes);
        export.putExportData("marketingEmail", createdCorporationUserMarketingEmail);
        export.putExportData("marketingMail", createdCorporationUserMarketingMail);
        export.putExportData("marketingSms", createdCorporationUserMarketingSms);
        export.putExportData("user", "corporation");
        export.putExportData("uuid", createdCorporationUserUuid);
        export.putToTempJsonObject("slug", hotelSlug);
        export.putToTempJsonObject("name", hotelName);
        export.addToTempJsonArray();
        export.putArrayToMainJsonObject("hotels");
    }
}