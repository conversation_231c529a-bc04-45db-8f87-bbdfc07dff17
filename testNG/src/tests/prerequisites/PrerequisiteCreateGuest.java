import org.json.simple.JSONArray;
import org.testng.annotations.Test;

public class PrerequisiteCreateGuest extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    CurrentData currentData;
    Login login;
    Export export;
    Assertion assertion;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    GuestsPage guestsPage;
    GuestsCreatePage guestsCreatePage;
    GuestsProfilePage guestsProfilePage;
    GuestsEditPage guestsEditPage;

    String testDataLoginPath = "";
    String testDataExportPath = "";
    String testDataHotelPath = "";

    // Variables used throughout test
    String hotelName = "";
    String hotelSlug = "";
    String createdGuestUserName = "";
    String createdGuestUserEmail = "";
    String createdGuestUserPhone = "";
    String createdGuestUserNotes = "";
    String createdGuestUserMarketingEmail = "";
    boolean createdGuestUserMarketingEmailState = false;
    String createdGuestUserMarketingMail = "";
    boolean createdGuestUserMarketingMailState = false;
    String createdGuestUserMarketingSms = "";
    boolean createdGuestUserMarketingSmsState = false;
    String createdGuestUserUuid = "";
    String createdGuestUserPassword = "";

    @Test(groups = {"prerequisiteCreateGuest"})
    public void prerequisiteCreateGuest() {
        // Set test case name
        setTestCaseName("prerequisiteCreateGuest");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";
        testDataHotelPath = getTestDataPath() + "Hotel.json";

        // Set the data for the hotel
        setDataHotel();

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Guest User\n" +
                        " * Covers AC of:\n" +
                        " * AC1: A Staff Admin user must be able to create a new Guest user\n" +
                        " *****");

        // Start the test
        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        hotelsPage.typeHotelNameInSearch(hotelName);
        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        dashboardPage.validatePage();

        // Navigate to guests page
        guestsPage = dashboardPage.horizontalOptionsPage.navigateToGuestsPage();
        guestsPage.validatePage();

        // Navigate to create guest page
        guestsCreatePage = guestsPage.clickCreateGuest();
        guestsCreatePage.validatePage();

        // Assert the page fields of the Guests Create page
        assertPageFieldsForGuestsCreatePage();

        // Set the data for creating the guest user
        setDataForCreatingGuestUser();
        // Insert the data into the page for creating a guest user
        typeDataForGuestsCreatePage();
        // Assert that the values inserted are actually there in the page for creating a guest user
        assertValuesInsertedForGuestsCreatePage();

        guestsPage = guestsCreatePage.clickGuestSubmit();
        guestsPage.validatePage();

        // Set the createdGuestUserUuid
        setDataForCreatedGuestUserUuid();

        // Add the data set to an export file
        addDataToExportFileForCreatedGuestUser();

        // Export created staff user to the path current data staff.
        // Needed here anyway, as the user has been created
        export.exportFileCurrentData(getPathCurrentDataHotels() + hotelSlug + "/guests/" + "guest" + "_" + createdGuestUserUuid);

        guestsProfilePage = guestsPage.clickGuestName(createdGuestUserName, createdGuestUserUuid);
        guestsProfilePage.validatePage();

        guestsEditPage = guestsProfilePage.clickEditGuest();
        guestsEditPage.validatePage();

        // Edit the data for the guest. Just password for now
        setDataForCreatedGuestPassword();

        // Insert the password
        guestsEditPage.typeGuestPassword(createdGuestUserPassword);
        guestsEditPage.typeGuestPasswordConfirmation(createdGuestUserPassword);

        // Assert that the values inserted are actually there
        assertValuesEditedForGuestsEditPage();

        // Navigate to the guest profile page
        guestsProfilePage = guestsEditPage.clickGuestSubmit();
        guestsProfilePage.validatePage();

        // Add the data set to an export file
        addDataToExportFileForEditedGuestUser();

        // Export edited guest user to the areas needed (ie next test suite)
        export.exportFile(testDataExportPath);

        // Export edited guest user to the path current data guest
        export.exportFileCurrentData(getPathCurrentDataHotels() + hotelSlug + "/guests/" + "guest" + "_" + createdGuestUserUuid);

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        } else {
            getReport().appendExport("ERROR: Incorrect user type to create guest user.");
        }
    }

    public void setDataHotel() {
        String[] hotelValues = new String[]{"slug", "name"};
        JSONArray hotelDetails = data.returnJSONArray(testDataHotelPath, hotelValues);
        hotelSlug = hotelDetails.get(0).toString();
        hotelName = hotelDetails.get(1).toString();
    }

    public void assertPageFieldsForGuestsCreatePage() {
        assertion.assertElementExists(driver,guestsCreatePage.guestNameLocator);
        assertion.assertElementExists(driver,guestsCreatePage.guestEmailLocator);
        assertion.assertElementExists(driver,guestsCreatePage.guestEmailConfirmationLocator);
        assertion.assertElementExists(driver,guestsCreatePage.guestPhoneLocator);
        assertion.assertElementExists(driver,guestsCreatePage.guestNotesLocator);
        assertion.assertElementExists(driver,guestsCreatePage.guestMarketingConsentEmailLocator);
        assertion.assertElementExists(driver,guestsCreatePage.guestMarketingConsentMailLocator);
        assertion.assertElementExists(driver,guestsCreatePage.guestMarketingConsentSmsLocator);
        assertion.assertElementExists(driver,guestsCreatePage.guestSubmitButtonLocator);
    }

    public void setDataForCreatingGuestUser() {
        if (getDataToUse().equals("random")) {
            createdGuestUserName = data.getRandomName("firstName") + " " + data.getRandomName("lastName") + " " + data.getRandomString(4,"any");
            createdGuestUserEmail = data.getRandomString(10, "lowercase") + "@high-level-software.com";
            createdGuestUserPhone = data.getRandomString(10, "numeric");
            createdGuestUserNotes = data.getRandomString(20, "any");
            createdGuestUserMarketingEmail = "true";
            createdGuestUserMarketingMail = "false";
            createdGuestUserMarketingSms = "false";
            getReport().appendExport("INFO: Using random data.");
        }
        else {
            String[] createGuestValues = new String[]{"name", "email", "phone", "notes", "marketingEmail", "marketingMail", "marketingSms"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray createGuestDetails = data.returnJSONArray(getDataToUse(), createGuestValues);
            createdGuestUserName = createGuestDetails.get(0).toString();
            createdGuestUserEmail = createGuestDetails.get(1).toString();
            createdGuestUserPhone = createGuestDetails.get(2).toString();
            createdGuestUserNotes = createGuestDetails.get(3).toString();
            createdGuestUserMarketingEmail = createGuestDetails.get(4).toString();
            createdGuestUserMarketingMail = createGuestDetails.get(5).toString();
            createdGuestUserMarketingSms = createGuestDetails.get(6).toString();
        }
    }

    public void typeDataForGuestsCreatePage() {
        guestsCreatePage.typeGuestName(createdGuestUserName);
        guestsCreatePage.typeGuestEmail(createdGuestUserEmail);
        guestsCreatePage.typeGuestEmailConfirmation(createdGuestUserEmail);
        guestsCreatePage.typeGuestPhone(createdGuestUserPhone);
        guestsCreatePage.typeGuestNotes(createdGuestUserNotes);
        if(createdGuestUserMarketingEmail.equals("true")) {
            createdGuestUserMarketingEmailState = true;
            guestsCreatePage.tickGuestEmail();
        }
        if(createdGuestUserMarketingMail.equals("true")) {
            createdGuestUserMarketingMailState = true;
            guestsCreatePage.tickGuestMail();
        }
        if(createdGuestUserMarketingSms.equals("true")) {
            createdGuestUserMarketingSmsState = true;
            guestsCreatePage.tickGuestSms();
        }
    }

    public void assertValuesInsertedForGuestsCreatePage() {
        assertion.assertValueInField(driver, guestsCreatePage.guestNameLocator, createdGuestUserName);
        assertion.assertValueInField(driver, guestsCreatePage.guestEmailLocator, createdGuestUserEmail);
        assertion.assertValueInField(driver, guestsCreatePage.guestEmailConfirmationLocator, createdGuestUserEmail);
        assertion.assertValueInField(driver, guestsCreatePage.guestPhoneLocator, createdGuestUserPhone);
        assertion.assertValueInField(driver, guestsCreatePage.guestNotesLocator, createdGuestUserNotes);
        assertion.assertTickBoxState(driver, guestsCreatePage.guestMarketingConsentEmailLocator, createdGuestUserMarketingEmailState);
        assertion.assertTickBoxState(driver, guestsCreatePage.guestMarketingConsentMailLocator, createdGuestUserMarketingMailState);
        assertion.assertTickBoxState(driver, guestsCreatePage.guestMarketingConsentSmsLocator, createdGuestUserMarketingSmsState);
    }

    public void setDataForCreatedGuestUserUuid() {
        createdGuestUserUuid = guestsPage.returnGuestUuidFromName(createdGuestUserName);
    }

    public void addDataToExportFileForCreatedGuestUser() {
        export.putExportData("name", createdGuestUserName);
        export.putExportData("email", createdGuestUserEmail);
        export.putExportData("phone", createdGuestUserPhone);
        export.putExportData("notes", createdGuestUserNotes);
        export.putExportData("marketingEmail", createdGuestUserMarketingEmail);
        export.putExportData("marketingMail", createdGuestUserMarketingMail);
        export.putExportData("marketingSms", createdGuestUserMarketingSms);
        export.putExportData("user", "guest");
        export.putExportData("uuid", createdGuestUserUuid);
        export.putToTempJsonObject("slug", hotelSlug);
        export.putToTempJsonObject("name", hotelName);
        export.addToTempJsonArray();
        export.putArrayToMainJsonObject("hotels");
    }

    public void setDataForCreatedGuestPassword() {
        if (getDataToUse().equals("random")) {
            createdGuestUserPassword = genericMethods.getRandomPassword();
        }
        else {
            String[] editGuestValues = new String[]{"password"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editGuestDetails = data.returnJSONArray(getDataToUse(), editGuestValues);
            createdGuestUserPassword = editGuestDetails.get(0).toString();
        }
    }

    public void assertValuesEditedForGuestsEditPage() {
        assertion.assertValueInField(driver, guestsEditPage.guestNameLocator, createdGuestUserName);
        assertion.assertValueInField(driver, guestsEditPage.guestEmailLocator, createdGuestUserEmail);
        assertion.assertValueInField(driver, guestsEditPage.guestEmailConfirmationLocator, createdGuestUserEmail);
        assertion.assertValueInField(driver, guestsEditPage.guestPasswordLocator, createdGuestUserPassword);
        assertion.assertValueInField(driver, guestsEditPage.guestPasswordConfirmationLocator, createdGuestUserPassword);
        assertion.assertValueInField(driver, guestsEditPage.guestPhoneLocator, createdGuestUserPhone);
        assertion.assertValueInField(driver, guestsEditPage.guestNotesLocator, createdGuestUserNotes);
        assertion.assertTickBoxState(driver, guestsEditPage.guestMarketingConsentEmailLocator, createdGuestUserMarketingEmailState);
        assertion.assertTickBoxState(driver, guestsEditPage.guestMarketingConsentMailLocator, createdGuestUserMarketingMailState);
        assertion.assertTickBoxState(driver, guestsEditPage.guestMarketingConsentSmsLocator, createdGuestUserMarketingSmsState);
    }

    public void addDataToExportFileForEditedGuestUser() {
        export.putExportData("name", createdGuestUserName);
        export.putExportData("email", createdGuestUserEmail);
        export.putExportData("password", createdGuestUserPassword);
        export.putExportData("phone", createdGuestUserPhone);
        export.putExportData("notes", createdGuestUserNotes);
        export.putExportData("marketingEmail", createdGuestUserMarketingEmail);
        export.putExportData("marketingMail", createdGuestUserMarketingMail);
        export.putExportData("marketingSms", createdGuestUserMarketingSms);
        export.putExportData("user", "guest");
        export.putExportData("uuid", createdGuestUserUuid);
        export.putToTempJsonObject("slug", hotelSlug);
        export.putToTempJsonObject("name", hotelName);
        export.addToTempJsonArray();
        export.putArrayToMainJsonObject("hotels");
    }
}