import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.testng.annotations.Test;

    /**
     *
     * This covers the Creation of Hotelier Users, as a pre-requisite test.
     *
     * Scenarios:
     * SC1: A Staff Admin user creating a new Hotelier-Manager user, with password
     * SC2: A Staff Admin user creating a new Hotelier-Front-Desk user, with password
     * SC3: A Staff Admin user creating a new Hotelier-Terminal user, with password
     * SC4: A Staff Admin user creating a new Hotelier-Manager user, without password (ie assign pre-existing hotelier to another hotel)
     * SC5: A Staff Admin user creating a new Hotelier-Front-Desk user, without password (ie assign pre-existing hotelier to another hotel)
     * SC6: A Staff Admin user creating a new Hotelier-Terminal user, without password (ie assign pre-existing hotelier to another hotel)
     *
     */
public class PrerequisiteCreateHotelier extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    CurrentData currentData;
    Login login;
    Export export;
    Assertion assertion;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    HoteliersPage hoteliersPage;
    HoteliersCreatePage hoteliersCreatePage;
    HoteliersEditPage hoteliersEditPage;

    String testDataLoginPath = "";
    String testDataExportPath = "";
    String testDataHotelPath = "";
    String testDataRolePath = "";

    // Variables used throughout test
    String hotelName = "";
    String hotelSlug = "";
    int hoteliers = 0;
    String createdHotelierUserName = "";
    String createdHotelierUserEmail = "";
    String createdHotelierUserPhone = "";
    String createdHotelierUserRole = "";
    String createdHotelierUserUuid = "";
    String createdHotelierUserPassword = "";

    /**
     *
     * Prerequisite Create Hotelier User, with password.
     *
     * Covers scenarios of:
     * SC1: A Staff Admin user creating a new Hotelier-Manager user, with password
     * SC2: A Staff Admin user creating a new Hotelier-Front-Desk user, with password
     * SC3: A Staff Admin user creating a new Hotelier-Terminal user, with password
     *
     */
    @Test(groups = {"prerequisiteCreateHotelier"})
    public void prerequisiteCreateHotelier() {
        // Set test case name
        setTestCaseName("prerequisiteCreateHotelier");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";
        testDataHotelPath = getTestDataPath() + "Hotel.json";
        testDataRolePath = getTestDataPath() + "Role.json";

        // Set the data for the hotel
        setDataHotel();

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Hotelier User, with password\n" +
                        " * Covers scenarios of:\n" +
                        " * SC1: A Staff Admin user creating a new Hotelier-Manager user, with password\n" +
                        " * SC2: A Staff Admin user creating a new Hotelier-Front-Desk user, with password\n" +
                        " * SC3: A Staff Admin user creating a new Hotelier-Terminal user, with password\n" +
                        " *****");

        // Start the test
        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        hotelsPage.typeHotelNameInSearch(hotelName);
        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        dashboardPage.validatePage();

        // Retrieve hoteliers count at last possible moment. Needed due to change in behaviour when a hotelier already exists for a hotel
        hoteliers =  currentData.getHotelierCount(testDataHotelPath);
        getReport().appendExport("INFO: Hoteliers count: " + hoteliers);
        if(hoteliers > 0) {
            hoteliersPage = dashboardPage.horizontalOptionsPage.navigateToHoteliersPage();
            hoteliersPage.validatePage();
            hoteliersCreatePage = hoteliersPage.clickCreateHotelier();
        } else {
            hoteliersCreatePage = dashboardPage.horizontalOptionsPage.navigateToHoteliersCreatePageRedirect();
        }
        hoteliersCreatePage.validatePage();

        // Assert the page fields of the Hoteliers Create page
        assertPageFieldsForHoteliersCreatePage();

        // Set the data for creating the hotelier user
        setDataForCreatingHotelierUser();
        // Insert the data into the page for creating a hotelier user
        typeDataForHoteliersCreatePage();
        // Assert that the values inserted are actually there in the page for creating a hotelier user
        assertValuesInsertedForHoteliersCreatePage();

        // Create the hotelier user
        // User is navigated back to the Hoteliers page
        hoteliersPage = hoteliersCreatePage.clickSubmitButton();
        hoteliersPage.validatePage();

        // Set the createdHotelierUserUuid
        setDataForCreatedHotelierUserUuid();

        // Add the data set to an export file
        addDataToExportFileForCreatedHotelierUser();

        // Export created staff user to the path current data staff.
        // Needed here anyway, as the user has been created
        export.exportFileCurrentData(getPathCurrentDataHotels() + hotelSlug + "/hoteliers/" + createdHotelierUserRole + "_" + createdHotelierUserUuid);

        hoteliersEditPage = hoteliersPage.clickHotelierName(createdHotelierUserName, createdHotelierUserUuid);
        hoteliersEditPage.validatePage();

        // Edit the data for the hotelier. Just password for now
        setDataForCreatedHotelierPassword();

        // Insert the password
        hoteliersEditPage.editHotelierPassword(createdHotelierUserPassword);
        hoteliersEditPage.editHotelierPasswordConfirmation(createdHotelierUserPassword);

        // Assert that the values inserted are actually there
        assertValuesEditedForHoteliersEditPage();

        // Navigate to the hoteliers page
        hoteliersPage = hoteliersEditPage.clickSubmitButton();
        hoteliersPage.validateAfterEditPage(createdHotelierUserUuid);

        // Add the data set to an export file
        addDataToExportFileForEditedHotelierUser();

        // Export edited hotelier user to the areas needed (ie next test suite)
        export.exportFile(testDataExportPath);

        // Export edited hotelier user to the path current data staff
        export.exportFileCurrentData(getPathCurrentDataHotels() + hotelSlug + "/hoteliers/" + createdHotelierUserRole + "_" + createdHotelierUserUuid);

        setPassedReportLocation();
    }

    /**
     *
     * Prerequisite Create Hotelier User, without password.
     *
     * Covers scenarios of:
     * SC4: A Staff Admin user creating a new Hotelier-Manager user, without password (ie assign pre-existing hotelier to another hotel)
     * SC5: A Staff Admin user creating a new Hotelier-Front-Desk user, without password (ie assign pre-existing hotelier to another hotel)
     * SC6: A Staff Admin user creating a new Hotelier-Terminal user, without password (ie assign pre-existing hotelier to another hotel)
     *
     */
    @Test(groups = {"prerequisiteCreateHotelierNoPassword"})
    public void prerequisiteCreateHotelierNoPassword() {
        // Set test case name
        setTestCaseName("prerequisiteCreateHotelierNoPassword");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";
        testDataHotelPath = getTestDataPath() + "Hotel.json";
        testDataRolePath = getTestDataPath() + "Role.json";

        // Set the data for the hotel
        setDataHotel();

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * Prerequisite Create Hotelier User, without password\n" +
                        " * Covers scenarios of:\n" +
                        " * SC4: A Staff Admin user creating a new Hotelier-Manager user, without password (ie assign pre-existing hotelier to another hotel)\n" +
                        " * SC5: A Staff Admin user creating a new Hotelier-Front-Desk user, without password (ie assign pre-existing hotelier to another hotel)\n" +
                        " * SC6: A Staff Admin user creating a new Hotelier-Terminal user, without password (ie assign pre-existing hotelier to another hotel)\n" +
                        " *****");

        // Start the test
        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        hotelsPage.typeHotelNameInSearch(hotelName);
        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        dashboardPage.validatePage();

        // Retrieve hoteliers count at last possible moment. Needed due to change in behaviour when a hotelier already exists for a hotel
        hoteliers =  currentData.getHotelierCount(testDataHotelPath);
        getReport().appendExport("INFO: Hoteliers count: " + hoteliers);
        if(hoteliers > 0) {
            hoteliersPage = dashboardPage.horizontalOptionsPage.navigateToHoteliersPage();
            hoteliersPage.validatePage();
            hoteliersCreatePage = hoteliersPage.clickCreateHotelier();
        } else {
            hoteliersCreatePage = dashboardPage.horizontalOptionsPage.navigateToHoteliersCreatePageRedirect();
        }
        hoteliersCreatePage.validatePage();

        // Assert the page fields of the Hoteliers Create page
        assertPageFieldsForHoteliersCreatePage();

        // Set the data for creating the hotelier user
        setDataForCreatingHotelierUser();
        // Insert the data into the page for creating a hotelier user
        typeDataForHoteliersCreatePage();
        // Assert that the values inserted are actually there in the page for creating a hotelier user
        assertValuesInsertedForHoteliersCreatePage();

        // Create the hotelier user
        // User is navigated back to the Hoteliers page
        hoteliersPage = hoteliersCreatePage.clickSubmitButton();
        hoteliersPage.validatePage();

        // Set the createdHotelierUserUuid
        setDataForCreatedHotelierUserUuid();

        // Add the data set to an export file
        addDataToExportFileForCreatedHotelierUserNoPassword();

        // Export created hotelier user to the areas needed (ie next test suite)
        export.exportFile(testDataExportPath);

        // Export created hotelier user to the path current data staff
        export.exportFileCurrentData(getPathCurrentDataHotels() + hotelSlug + "/hoteliers/" + createdHotelierUserRole + "_" + createdHotelierUserUuid);

        // Export hotelier, with all hotels, to all other hotels that it is in
        for(int i =0; i<currentData.getHotelPaths().size(); i++) {
            export.exportFileCurrentData(currentData.getHotelPaths().get(i).toString());
        }

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        } else {
            getReport().appendExport("ERROR: Incorrect user type to create staff user.");
        }
    }

    public void setDataHotel() {
        String[] hotelValues = new String[]{"slug", "name"};
        JSONArray hotelDetails = data.returnJSONArray(testDataHotelPath, hotelValues);
        hotelSlug = hotelDetails.get(0).toString();
        hotelName = hotelDetails.get(1).toString();
    }

    public void assertPageFieldsForHoteliersCreatePage() {
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierNameLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierRoleLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierEmailLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierConfirmationEmailLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierTelephoneNumberLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.submitButtonLocator);
    }

    public void setDataForCreatingHotelierUser() {
        if (getDataToUse().equals("random")) {
            createdHotelierUserName = data.getRandomName("firstName") + " " + data.getRandomName("lastName") + " " + data.getRandomString(4,"any");
            createdHotelierUserEmail = data.getRandomString(10, "lowercase") + "@high-level-software.com";
            createdHotelierUserPhone = data.getRandomString(10, "numeric");
            getReport().appendExport("INFO: Using random data.");
            // Parse the role details
            String[] roleValues = new String[]{"role"};
            JSONArray roleDetails = data.returnJSONArray(testDataRolePath, roleValues);
            createdHotelierUserRole = roleDetails.get(0).toString();
        }
        else {
            String[] createHotelierValues = new String[]{"name", "email", "phone", "role"};
            getReport().appendExport("INFO: Using data in file: " + getTestDataPath() + getDataToUse());
            JSONArray createHotelierDetails = data.returnJSONArray(getTestDataPath() + getDataToUse(), createHotelierValues);
            createdHotelierUserName = createHotelierDetails.get(0).toString();
            createdHotelierUserEmail = createHotelierDetails.get(1).toString();
            createdHotelierUserPhone = createHotelierDetails.get(2).toString();
            createdHotelierUserRole = createHotelierDetails.get(3).toString();
        }
    }

    public void typeDataForHoteliersCreatePage() {
        hoteliersCreatePage.typeHotelierName(createdHotelierUserName);
        hoteliersCreatePage.typeHotelierRole(createdHotelierUserRole);
        hoteliersCreatePage.typeHotelierEmail(createdHotelierUserEmail);
        hoteliersCreatePage.typeHotelierConfirmationEmail(createdHotelierUserEmail);
        hoteliersCreatePage.typeHotelierTelephoneNumber(createdHotelierUserPhone);
    }

    public void assertValuesInsertedForHoteliersCreatePage() {
        assertion.assertValueInField(driver, hoteliersCreatePage.hotelierNameLocator, createdHotelierUserName);
        assertion.assertTextInDropDownField(driver, hoteliersCreatePage.hotelierRoleLocator, createdHotelierUserRole);
        assertion.assertValueInField(driver, hoteliersCreatePage.hotelierEmailLocator, createdHotelierUserEmail);
        assertion.assertValueInField(driver, hoteliersCreatePage.hotelierConfirmationEmailLocator, createdHotelierUserEmail);
        assertion.assertValueInField(driver, hoteliersCreatePage.hotelierTelephoneNumberLocator, createdHotelierUserPhone);
    }

    public void setDataForCreatedHotelierUserUuid() {
        createdHotelierUserUuid = hoteliersPage.returnHotelierUuidFromName(createdHotelierUserName);
    }

    public void addDataToExportFileForCreatedHotelierUser() {
        export.putExportData("name", createdHotelierUserName);
        export.putExportData("email", createdHotelierUserEmail);
        export.putExportData("phone", createdHotelierUserPhone);
        export.putExportData("role", createdHotelierUserRole);
        switch (createdHotelierUserRole) {
            case "Manager":
                export.putExportData("user", "hotelier-manager");
                break;
            case "Front Desk":
                export.putExportData("user", "hotelier-front-desk");
                break;
            case "Terminal":
                export.putExportData("user", "hotelier-terminal");
                break;
            case "Group Manager":
                export.putExportData("user", "group-manager");
                break;
        }
        export.putExportData("uuid", createdHotelierUserUuid);
        export.putToTempJsonObject("slug", hotelSlug);
        export.putToTempJsonObject("name", hotelName);
        export.addToTempJsonArray();
        export.putArrayToMainJsonObject("hotels");
    }

    public void setDataForCreatedHotelierPassword() {
        if (getDataToUse().equals("random")) {
            createdHotelierUserPassword = genericMethods.getRandomPassword();
        }
        else {
            String[] editStaffValues = new String[]{"password"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editStaffDetails = data.returnJSONArray(getDataToUse(), editStaffValues);
            createdHotelierUserPassword = editStaffDetails.get(0).toString();
        }
    }

    public void assertValuesEditedForHoteliersEditPage() {
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierNameLocator, createdHotelierUserName);
        assertion.assertTextInDropDownField(driver, hoteliersEditPage.hotelierRoleLocator, createdHotelierUserRole);
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierEmailLocator, createdHotelierUserEmail);
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierConfirmationEmailLocator, createdHotelierUserEmail);
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierTelephoneNumberLocator, createdHotelierUserPhone);
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierPasswordLocator, createdHotelierUserPassword);
        assertion.assertValueInField(driver, hoteliersEditPage.hotelierPasswordConfirmationLocator, createdHotelierUserPassword);
    }

    public void addDataToExportFileForEditedHotelierUser() {
        export.putExportData("name", createdHotelierUserName);
        export.putExportData("email", createdHotelierUserEmail);
        export.putExportData("phone", createdHotelierUserPhone);
        export.putExportData("password", createdHotelierUserPassword);
        export.putExportData("role", createdHotelierUserRole);
        switch (createdHotelierUserRole) {
            case "Manager":
                export.putExportData("user", "hotelier-manager");
                break;
            case "Front Desk":
                export.putExportData("user", "hotelier-front-desk");
                break;
            case "Terminal":
                export.putExportData("user", "hotelier-terminal");
                break;
            case "Group Manager":
                export.putExportData("user", "group-manager");
                break;
        }
        export.putExportData("uuid", createdHotelierUserUuid);
        export.putToTempJsonObject("slug", hotelSlug);
        export.putToTempJsonObject("name", hotelName);
        export.addToTempJsonArray();
        export.putArrayToMainJsonObject("hotels");
    }

    public void addDataToExportFileForCreatedHotelierUserNoPassword() {
        currentData.findHotelier(getTestDataPath() + getDataToUse());

        export.putExportData("name", createdHotelierUserName);
        export.putExportData("email", createdHotelierUserEmail);
        export.putExportData("phone", createdHotelierUserPhone);
        export.putExportData("password", currentData.getHotelierPassword());
        export.putExportData("role", createdHotelierUserRole);
        switch (createdHotelierUserRole) {
            case "Manager":
                export.putExportData("user", "hotelier-manager");
                break;
            case "Front Desk":
                export.putExportData("user", "hotelier-front-desk");
                break;
            case "Terminal":
                export.putExportData("user", "hotelier-terminal");
                break;
            case "Group Manager":
                export.putExportData("user", "group-manager");
                break;
        }
        export.putExportData("uuid", createdHotelierUserUuid);
        // Add new hotel
        export.putToTempJsonObject("slug", hotelSlug);
        export.putToTempJsonObject("name", hotelName);
        export.addToTempJsonArray();
        // Re-add pre-existing hotels
        JSONArray tempJsonArrayHotels = currentData.getHotelierHotels();
        for(int i=0; i<tempJsonArrayHotels.size(); i++) {
            JSONObject hotelFromExisting = (JSONObject) tempJsonArrayHotels.get(i);
            export.putToTempJsonObject("slug", hotelFromExisting.get("slug").toString());
            export.putToTempJsonObject("name", hotelFromExisting.get("name").toString());
            export.addToTempJsonArray();
        }
        export.putArrayToMainJsonObject("hotels");
    }
}