import org.openqa.selenium.By;
import org.testng.annotations.Test;
import org.json.simple.JSONArray;

    /**
     * 
     * All the tests relating to the page for the creation of Staff Users
     * 
     * AC1: The Staff Create page must have the following fields, if a user has access:
     *      name, email, phone, role, submit button
     * 
     * AC2: The Staff Create page must not show the following fields, if a user does not have access:
     *      name, email, phone, role, submit button
     * 
     * AC3: The Staff Create page must show an Access Denied message, if a user does not have access
     *
     * AC4: The Staff Create page must only be accessible by a Staff Admin user
     * 
     * AC5: The Staff Create page must not be accessible by:
     *      Staff Support, Staff Manager, Staff Sales, Group Admin, Group Manager, Hotelier Manager,
     *      Hotelier Front Desk, Terminal, Guest with account, Corporation, Guest without an account
     * 
     */
public class StaffCreatePageVerification extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    StaffPage staffPage;
    StaffCreatePage staffCreatePage;
    AvailabilityPage availabilityPage;
    GroupRatePlansPage groupRatePlansPage;

    String testDataLoginPath = "";

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC1: The Staff Create page must have the following fields, if a user has access:
     *      name, email, phone, role, submit button
     *
     * AC4: The Staff Create page must only be accessible by a Staff Admin user
     * 
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to view all of the fields on the Staff Create page
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Staff Admin user, that is able to log in
     * 
     */
    @Test(groups={"staffCreatePageVerificationTC1"})
    public void staffCreatePageVerificationTC1() {
        // Set test case name
        setTestCaseName("staffCreatePageVerificationTC1");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to view all of the fields on the Staff Create page\n" +
                        " * Covers AC of:\n" +
                        " * AC1: The Staff Create page must have the following fields, if a user has access:\n" +
                        " *      name, email, phone, role, submit button\n" +
                        " * AC4: The Staff Create page must only be accessible by a Staff-Admin user\n" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Validate the Staff page that the user has landed on
        staffPage.validatePage();

        // Navigate to Staff Create page
        staffCreatePage = staffPage.clickCreate();
        staffCreatePage.validatePage();

        // Assert the page fields of the Staff Create page
        assertPageFieldsForStaffCreatePage();

        setPassedReportLocation();
    }

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC2: The Staff Create page must not show the following fields, if a user does not have access:
     *      name, email, phone, role, submit button
     *
     * AC3: The Staff Create page must show an Access Denied message, if a user does not have access
     * 
     * AC5: The Staff Create page must not be accessible by:
     *      Staff Support, Staff Manager, Staff Sales, Group Admin, Group Manager, Hotelier Manager,
     *      Hotelier Front Desk, Terminal, Guest with account, Corporation, Guest without an account
     * 
     * This test covers the test cases of:
     * TC2: Verify that a user without permission is not able to view all of the fields on the Staff Create page
     *      and is shown an Access Denied message
     *
     * TC2 requires the following test data to cover it:
     * TD1: A Staff Support user, that is able to log in
     * TD2: A Staff Manager user, that is able to log in
     * TD3: A Staff Sales user, that is able to log in
     * TD4: A Hotelier Manager user, that is able to log in
     * TD5: A Hotelier Front Desk user, that is able to log in
     * TD6: A Terminal user, that is able to log in
     * TD7: A Guest with account user, that is able to log in
     * TD8: A Corporation user, that is able to log in
     * TD9:  A Group Admin user, that is able to log in
     * TD10: A Group Manager user, that is able to log in
     * TD11: A Guest without an account user
     * 
     */
    @Test(groups={"staffCreatePageVerificationTC2"})
    public void staffCreatePageVerificationTC2() {
        /// Set test case name
        setTestCaseName("staffCreatePageVerificationTC2");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user without permission is not able to view all of the fields on the Staff Create page\n" +
                        " *      and is shown an Access Denied message\n" +
                        " * Covers AC of:\n" +
                        " * AC2: The Staff Create page must not show the following fields, if a user does not have access:\n" +
                        " *      name, email, phone, role, submit button\n" +
                        " * AC3: The Staff Create page must show an Access Denied message, if a user does not have access\n" +
                        " * AC5: The Staff Create page must not be accessible by:\n" +
                        " *      Staff Support, Staff Manager, Staff Sales, Group Admin, Group Manager, Hotelier Manager,\n" +
                        " *      Hotelier Front Desk, Terminal, Guest with account, Corporation, Guest without an account\n" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();
        // Verify that user is denied access to staff page and staff create page
        staffPage.validateAccessDeniedToPage();
        driver.get(getEnvironment() + "/staff/create");
        staffCreatePage = new StaffCreatePage(driver, getReport(), getEnvironment());
        staffCreatePage.validateAccessDeniedToPage();
        // Assert the page fields of the Staff Create page do not exist to the user
        assertPageFieldsNotExistingForStaffCreatePage();

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin") || login.getLoginUser().equals("staff-support") || login.getLoginUser().equals("staff-manager") || login.getLoginUser().equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-admin")){
            groupRatePlansPage = login.groupRatePlansPage;
            groupRatePlansPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-manager") || login.getLoginUser().equals("hotelier-manager") || login.getLoginUser().equals("hotelier-front-desk")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("hotelier-terminal")){
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("guest") || login.getLoginUser().equals("corporation")) {
             availabilityPage = login.availabilityPage;
             availabilityPage.validatePage();
        }
        getReport().appendExport("STEP: Navigate to the Staff page.");
        driver.get(getEnvironment() + "/staff");
        getReport().appendExport("INFO: User should be on the Staff page.");
        staffPage = new StaffPage(driver, getReport(), getEnvironment());
    }

    public void assertPageFieldsForStaffCreatePage() {
        assertion.assertTextInField(driver, staffCreatePage.boxHeaderLocator, "CREATE STAFF ACCOUNT");
        assertion.assertElementExists(driver,staffCreatePage.nameLocator);
        assertion.assertElementExists(driver,staffCreatePage.emailLocator);
        assertion.assertElementExists(driver,staffCreatePage.phoneLocator);
        assertion.assertElementExists(driver,staffCreatePage.roleLocator);
        assertion.assertElementExists(driver,staffCreatePage.submitButtonLocator);
    }

    public void assertPageFieldsNotExistingForStaffCreatePage() {
        // Saves 35s
        genericMethods.setImplicitlyWaitTime(driver,1);
        By[] elements = {   staffCreatePage.nameLocator,
                            staffCreatePage.emailLocator,
                            staffCreatePage.phoneLocator,
                            staffCreatePage.roleLocator,
                            staffCreatePage.submitButtonLocator};
        assertion.assertElementsDoNotExist(driver, elements);
        genericMethods.setImplicitlyWaitTime(driver,10);
    }
}