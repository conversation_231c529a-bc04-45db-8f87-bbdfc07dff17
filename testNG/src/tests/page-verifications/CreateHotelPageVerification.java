import org.json.simple.JSONObject;
import org.openqa.selenium.By;
import org.testng.annotations.Test;
import org.json.simple.JSONArray;

    /**
     * 
     * All the tests relating to the page for the creation of Hotels
     * 
     * AC1: The Create Hotel page must have the following fields, if a user has access:
     *      slug, name, address, post code, country, phone, email, description, privacy link, terms, cancellation, 
     *      checkin from, checkout by, tax rate, tax number, rdx code, is rdx enabled, text colour, background colour,
     *      text on background colour, background shade colour, text on background shade colour, highlight colour,
     *      text on highlight colour, midlight colour, text on midlight colour, lowlight colour, text on lowlight colour,
     *      logo button, submit button 
     * 
     * AC2: The Create Hotel page must not show the following fields, if a user does not have access:
     *      slug, name, address, post code, country, phone, email, description, privacy link, terms, cancellation, 
     *      checkin from, checkout by, tax rate, tax number, rdx code, is rdx enabled, text colour, background colour,
     *      text on background colour, background shade colour, text on background shade colour, highlight colour,
     *      text on highlight colour, midlight colour, text on midlight colour, lowlight colour, text on lowlight colour,
     *      logo button, submit button 
     * 
     * AC3: The Create Hotel page must show an Access Denied message, if a user does not have access
     *
     * AC4: The Create Hotel page must only be accessible by a Staff Admin, Staff Support, Staff Manager and Staff Sales user
     * 
     * AC5: The Create Hotel page must not be accessible by:
     *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk,
     *      Terminal, Guest with account, Corporation, Guest without an account
     * 
     */
public class CreateHotelPageVerification extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    CurrentData currentData;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    CreateHotelPage createHotelPage;
    DashboardPage dashboardPage;
    AvailabilityPage availabilityPage;
    GroupRatePlansPage groupRatePlansPage;

    // Variables used throughout test
    String testDataLoginPath = "";

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC1: The Create Hotel page must have the following fields, if a user has access:
     *      slug, name, address, post code, country, phone, email, description, privacy link, terms, cancellation, 
     *      checkin from, checkout by, tax rate, tax number, rdx code, is rdx enabled, text colour, background colour,
     *      text on background colour, background shade colour, text on background shade colour, highlight colour,
     *      text on highlight colour, midlight colour, text on midlight colour, lowlight colour, text on lowlight colour,
     *      logo button, submit button
     * 
     * AC4: The Create Hotel page must only be accessible by a Staff Admin, Staff Support, Staff Manager and Staff Sales user
     *
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to view all of the fields on the create hotel page
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Staff Admin user, that is able to log in
     * TD2: A Staff Support user, that is able to log in
     * TD3: A Staff Manager user, that is able to log in
     * TD4: A Staff Sales user, that is able to log in
     * 
     */
    @Test(groups={"createHotelPageVerificationTC1"})
    public void createHotelPageVerificationTC1() {
        // Set test case name
        setTestCaseName("createHotelPageVerificationTC1");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to view all of the fields on the create hotel page\n" +
                        " * Covers AC of:\n" +
                        " * AC1: The Create Hotel page must have the following fields, if a user has access:\n" +
                        " *      slug, name, address, post code, country, phone, email, description, privacy link, terms, cancellation,\n" +
                        " *      checkin from, checkout by, tax rate, tax number, rdx code, is rdx enabled, text colour, background colour,\n" +
                        " *      text on background colour, background shade colour, text on background shade colour, highlight colour,\n" +
                        " *      text on highlight colour, midlight colour, text on midlight colour, lowlight colour, text on lowlight colour,\n" +
                        " *      logo button, submit button\n" +
                        " * AC4: The Create Hotel page must only be accessible by a Staff Admin, Staff Support, Staff Manager and Staff Sales user\n" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Navigate to Create Hotel page
        createHotelPage = hotelsPage.clickCreateHotelLink();
        createHotelPage.validatePage();

        // Assert the page fields of the Create Hotel page
        assertPageFieldsForCreateHotelPage();

        setPassedReportLocation();
    }

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC2: The Create Hotel page must not show the following fields, if a user does not have access:
     *      slug, name, address, post code, country, phone, email, description, privacy link, terms, cancellation, 
     *      checkin from, checkout by, tax rate, tax number, rdx code, is rdx enabled, text colour, background colour,
     *      text on background colour, background shade colour, text on background shade colour, highlight colour,
     *      text on highlight colour, midlight colour, text on midlight colour, lowlight colour, text on lowlight colour,
     *      logo button, submit button 
     * 
     * AC3: The Create Hotel page must show an Access Denied message, if a user does not have access
     * 
     * AC5: The Create Hotel page must not be accessible by:
     *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk,
     *      Terminal, Guest with account, Corporation, Guest without an account
     * 
     * This test covers the test cases of:
     * TC2: Verify that a user without permission is not able to view all of the fields on the Create Hotel page
     *      and is shown an Access Denied message
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Hotelier Manager user, that is able to log in
     * TD2: A Hotelier Front Desk user, that is able to log in
     * TD3: A Terminal user, that is able to log in
     * TD4: A Guest with account user, that is able to log in
     * TD5: A Corporation user, that is able to log in
     * TD6: A Group Admin user, that is able to log in
     * TD7: A Group Manager user, that is able to log in
     * TD8: A Guest without an account user
     * 
     */
    @Test(groups={"createHotelPageVerificationTC2"})
    public void createHotelPageVerificationTC2() {
        // Set test case name
        setTestCaseName("createHotelPageVerificationTC2");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to view all of the fields on the create hotel page\n" +
                        " * Covers AC of:\n" +
                        " * AC1: The Create Hotel page must have the following fields, if a user has access:\n" +
                        " *      slug, name, address, post code, country, phone, email, description, privacy link, terms, cancellation,\n" +
                        " *      checkin from, checkout by, tax rate, tax number, rdx code, is rdx enabled, text colour, background colour,\n" +
                        " *      text on background colour, background shade colour, text on background shade colour, highlight colour,\n" +
                        " *      text on highlight colour, midlight colour, text on midlight colour, lowlight colour, text on lowlight colour,\n" +
                        " *      logo button, submit button\n" +
                        " * AC4: The Create Hotel page must only be accessible by a Staff Admin, Staff Support, Staff Manager and Staff Sales user\n" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Navigate to Create Hotel page
        driver.get(getEnvironment() + "/hotels/create");
        createHotelPage = new CreateHotelPage(getDriver(), getReport(), getEnvironment());
        createHotelPage.validateAccessDeniedToPage();

        // Assert the page fields of the Create Hotel page
        assertPageFieldsNotExistingForCreateHotelPage();

        setPassedReportLocation();
    }


    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin") || login.getLoginUser().equals("staff-support") || login.getLoginUser().equals("staff-manager") || login.getLoginUser().equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-admin")){
            groupRatePlansPage = login.groupRatePlansPage;
            groupRatePlansPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-manager") || login.getLoginUser().equals("hotelier-manager") || login.getLoginUser().equals("hotelier-front-desk")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("hotelier-terminal")){
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("guest") || login.getLoginUser().equals("corporation")) {
            availabilityPage = login.availabilityPage;
            availabilityPage.validatePage();
        }
    }

    public void assertPageFieldsForCreateHotelPage() {
        By[] elements = {   createHotelPage.statusLocator,
                            createHotelPage.nameLocator,
                            createHotelPage.addressLocator,
                            createHotelPage.postcodeLocator,
                            createHotelPage.countryLocator,
                            createHotelPage.phoneLocator,
                            createHotelPage.emailLocator,
                            createHotelPage.descriptionLocator,
                            createHotelPage.privacyLinkLocator,
                            createHotelPage.termsLocator,
                            createHotelPage.cancellationLocator,
                            createHotelPage.checkinFromLocator,
                            createHotelPage.checkoutByLocator,
                            createHotelPage.taxRateLocator,
                            createHotelPage.taxNumberLocator,
                            createHotelPage.rdxCodeLocator,
                            createHotelPage.isRdxEnabledLocator,
                            createHotelPage.textColourLocator,
                            createHotelPage.backgroundColourLocator,
                            createHotelPage.textOnBackgroundColourLocator,
                            createHotelPage.backgroundShadeColourLocator,
                            createHotelPage.textOnBackgroundShadeColourLocator,
                            createHotelPage.highlightColourLocator,
                            createHotelPage.textOnHighlightColourLocator,
                            createHotelPage.midlightColourLocator,
                            createHotelPage.textOnMidlightColourLocator,
                            createHotelPage.lowlightColourLocator,
                            createHotelPage.textOnLowlightColourLocator,
                            createHotelPage.logoButtonLocator,
                            createHotelPage.submitButtonLocator };
        assertion.assertElementsDoExist(driver,elements);
    }

    public void assertPageFieldsNotExistingForCreateHotelPage() {
        // Saves 35s
        genericMethods.setImplicitlyWaitTime(driver,1);
        By[] elements = {   createHotelPage.statusLocator,
                createHotelPage.nameLocator,
                createHotelPage.addressLocator,
                createHotelPage.postcodeLocator,
                createHotelPage.countryLocator,
                createHotelPage.phoneLocator,
                createHotelPage.emailLocator,
                createHotelPage.descriptionLocator,
                createHotelPage.privacyLinkLocator,
                createHotelPage.termsLocator,
                createHotelPage.cancellationLocator,
                createHotelPage.checkinFromLocator,
                createHotelPage.checkoutByLocator,
                createHotelPage.taxRateLocator,
                createHotelPage.taxNumberLocator,
                createHotelPage.rdxCodeLocator,
                createHotelPage.isRdxEnabledLocator,
                createHotelPage.textColourLocator,
                createHotelPage.backgroundColourLocator,
                createHotelPage.textOnBackgroundColourLocator,
                createHotelPage.backgroundShadeColourLocator,
                createHotelPage.textOnBackgroundShadeColourLocator,
                createHotelPage.highlightColourLocator,
                createHotelPage.textOnHighlightColourLocator,
                createHotelPage.midlightColourLocator,
                createHotelPage.textOnMidlightColourLocator,
                createHotelPage.lowlightColourLocator,
                createHotelPage.textOnLowlightColourLocator,
                createHotelPage.logoButtonLocator,
                createHotelPage.submitButtonLocator };
        assertion.assertElementsDoNotExist(driver, elements);
        genericMethods.setImplicitlyWaitTime(driver,10);
    }
}