import org.openqa.selenium.By;
import org.testng.annotations.Test;
import org.json.simple.JSONArray;

    /**
     * 
     * All the tests relating to the page for the editing of Staff Users
     * 
     * AC1: The Staff Edit page must have the following fields, if a user has access:
     *      name, email, password, password confirmation, phone, role, submit button
     * 
     * AC2: The Staff Edit page must not show the following fields, if a user does not have access:
     *      name, email, password, password confirmation, phone, role, submit button
     * 
     * AC3: The Staff Edit page must show an Access Denied message, if a user does not have access
     *
     * AC4: Any role for the Staff Edit page can always be accessible by a Staff Admin user
     * 
     * AC5: A Staff Support user must be able to access only their own Staff Edit page
     * 
     * AC6: A Staff Manager user must be able to access only their own Staff Edit page
     * 
     * AC7: A Staff Sales user must be able to access only their own Staff Edit page
     * 
     * AC8: The Staff Edit page must not be accessible by:
     *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk, Terminal,
     *      Guest with account, Corporation, Guest without an account
     * 
     */
public class StaffEditPageVerification extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    CurrentData currentData;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    StaffPage staffPage;
    StaffEditPage staffEditPage;
    AvailabilityPage availabilityPage;
    GroupRatePlansPage groupRatePlansPage;

    // Variables used throughout test
    String name = "";
    String email = "";
    String phone = "";
    String role = "";
    String staffUuid = "";
    String testDataLoginPath = "";
    String testDataStaffUserPath = "";

    /**
     * 
     * This test covers the acceptance criteria of:
     *
     * AC5: A Staff Support user must be able to access only their own Staff Edit page
     *
     * AC6: A Staff Manager user must be able to access only their own Staff Edit page
     *
     * AC7: A Staff Sales user must be able to access only their own Staff Edit page
     * 
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to view all of the fields for their own Staff Edit page
     *
     * TC1 requires the following test data to cover it:
     * TD1Login: A Staff Support user, that is able to log in, that edits themselves
     * TD2Login: A Staff Manager user, that is able to log in, that edits themselves
     * TD3Login: A Staff Sales user, that is able to log in, that edits themselves
     * 
     */
    @Test(groups={"staffEditPageVerificationTC1"})
    public void staffEditPageVerificationTC1() {
        // Set test case name
        setTestCaseName("staffEditPageVerificationTC1");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to view all of the fields for their own Staff Edit page\n" +
                        " * Covers AC of:\n" +
                        " * AC5: A Staff Support user must be able to access only their own Staff Edit page\n" +
                        " * AC6: A Staff Manager user must be able to access only their own Staff Edit page\n" +
                        " * AC7: A Staff Sales user must be able to access only their own Staff Edit page\n" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        setStaffUser();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        getReport().appendExport("STEP: Navigate to the current users Staff edit page.");
        driver.findElement(By.linkText(name)).click();
        getReport().appendExport("INFO: User should be on the current users Staff edit page.");

        staffEditPage = new StaffEditPage(getDriver(), getReport(), getEnvironment(), staffUuid, name);
        staffEditPage.validatePage();

        // Assert the page fields of the Staff Edit page
        assertPageFieldsForStaffEditPageNonStaffAdmin();

        setPassedReportLocation();
    }

    /**
     *
     * This test covers the acceptance criteria of:
     * AC1: The Staff Edit page must have the following fields, if a user has access:
     *      name, email, password, password confirmation, phone, role, submit button
     *
     * AC4: Any role for the Staff Edit page can always be accessible by a Staff Admin user
     *
     * This test covers the test cases of:
     * TC2: Verify that a user with the right permission is able to view all of the fields for any Staff Edit page
     *
     * TC1 requires the following test data to cover it:
     * TD1Login: A Staff Admin user, that is able to log in, that edits the current Staff Admin User
     * TD2Login: A Staff Admin user, that is able to log in, that edits another Staff Admin User
     * TD3Login: A Staff Admin user, that is able to log in, that edits a Staff Support User
     * TD4Login: A Staff Admin user, that is able to log in, that edits a Staff Manager User
     * TD5Login: A Staff Admin user, that is able to log in, that edits a Staff Sales User
     *
     */
    @Test(groups={"staffEditPageVerificationTC2"})
    public void staffEditPageVerificationTC2() {
        // Set test case name
        setTestCaseName("staffEditPageVerificationTC2");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC2: Verify that a user with the right permission is able to view all of the fields for any Staff Edit page\n" +
                        " * Covers AC of:\n" +
                        " * AC1: The Staff Edit page must have the following fields, if a user has access:\n" +
                        " *      name, email, password, password confirmation, phone, role, submit button\n" +
                        " * AC4: Any role for the Staff Edit page can always be accessible by a Staff Admin user\n" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        setStaffUser();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        getReport().appendExport("STEP: Navigate to the Staff page.");
        driver.get(getEnvironment() + "/staff");
        getReport().appendExport("INFO: User should be on the Staff page.");

        staffPage = new StaffPage(driver, getReport(), getEnvironment());
        staffPage.validatePage();

        // Navigate to Staff Edit page
        staffEditPage = staffPage.clickStaffName(name, staffUuid);
        staffEditPage.validatePage();

        // Assert the page fields of the Staff Edit page
        assertPageFieldsForStaffEditPageStaffAdmin();

        setPassedReportLocation();
    }

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC2: The Staff Edit page must not show the following fields, if a user does not have access:
     *      name, email, password, password confirmation, phone, role, submit button
     *
     * AC3: The Staff Edit page must show an Access Denied message, if a user does not have access
     *
     * AC8: The Staff Edit page must not be accessible by:
     *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk, Terminal,
     *      Guest with account, Corporation, Guest without an account
     * 
     * This test covers the test cases of:
     * TC3: Verify that a user without permission is not able to view all of the fields on the Staff Edit page
     *      and is shown an Access Denied message
     *
     * TC2 requires the following test data to cover it:
     * TD1: A Staff Support user, that is able to log in, editing a Staff Admin user
     * TD2: A Staff Support user, that is able to log in, editing a different Staff Support user
     * TD3: A Staff Support user, that is able to log in, editing a Staff Manager user
     * TD4: A Staff Support user, that is able to log in, editing a Staff Sales user
     *
     * TD5: A Staff Manager user, that is able to log in, editing a Staff Admin user
     * TD6: A Staff Manager user, that is able to log in, editing a Staff Support user
     * TD7: A Staff Manager user, that is able to log in, editing a different Staff Manager user
     * TD8: A Staff Manager user, that is able to log in, editing a Staff Sales user
     *
     * TD9: A Staff Sales user, that is able to log in, editing a Staff Admin user
     * TD10: A Staff Sales user, that is able to log in, editing a Staff Support user
     * TD11: A Staff Sales user, that is able to log in, editing a Staff Manager user
     * TD12: A Staff Sales user, that is able to log in, editing a different Staff Sales user
     *
     * TD13: A Hotelier Manager user, that is able to log in, editing a Staff Admin user
     * TD14: A Hotelier Manager user, that is able to log in, editing a Staff Support user
     * TD15: A Hotelier Manager user, that is able to log in, editing a Staff Manager user
     * TD16: A Hotelier Manager user, that is able to log in, editing a Staff Sales user
     *
     * TD17: A Hotelier Front Desk user, that is able to log in, editing a Staff Admin user
     * TD18: A Hotelier Front Desk user, that is able to log in, editing a Staff Support user
     * TD19: A Hotelier Front Desk user, that is able to log in, editing a Staff Manager user
     * TD20: A Hotelier Front Desk user, that is able to log in, editing a Staff Sales user
     *
     * TD21: A Terminal user, that is able to log in, editing a Staff Admin user
     * TD22: A Terminal user, that is able to log in, editing a Staff Support user
     * TD23: A Terminal user, that is able to log in, editing a Staff Manager user
     * TD24: A Terminal user, that is able to log in, editing a Staff Sales user
     *
     * TD25: A Guest with account user, that is able to log in, editing a Staff Admin user
     * TD26: A Guest with account user, that is able to log in, editing a Staff Support user
     * TD27: A Guest with account user, that is able to log in, editing a Staff Manager user
     * TD28: A Guest with account user, that is able to log in, editing a Staff Sales user
     *
     * TD29: A Corporation user, that is able to log in, editing a Staff Admin user
     * TD30: A Corporation user, that is able to log in, editing a Staff Support user
     * TD31: A Corporation user, that is able to log in, editing a Staff Manager user
     * TD32: A Corporation user, that is able to log in, editing a Staff Sales user
     *
     * TD33:  A Group Admin user, that is able to log in, editing a Staff Admin user
     * TD34:  A Group Admin user, that is able to log in, editing a Staff Support user
     * TD35:  A Group Admin user, that is able to log in, editing a Staff Manager user
     * TD36:  A Group Admin user, that is able to log in, editing a Staff Sales user
     *
     * TD37: A Group Manager user, that is able to log in, editing a Staff Admin user
     * TD38: A Group Manager user, that is able to log in, editing a Staff Support user
     * TD39: A Group Manager user, that is able to log in, editing a Staff Manager user
     * TD40: A Group Manager user, that is able to log in, editing a Staff Sales user
     *
     * TD41: A Guest without an account user, editing a Staff Admin user
     * TD42: A Guest without an account user, editing a Staff Support user
     * TD43: A Guest without an account user, editing a Staff Manager user
     * TD44: A Guest without an account user, editing a Staff Sales user
     * 
     */
    @Test(groups={"staffEditPageVerificationTC3"})
    public void staffEditPageVerificationTC3() {
        // Set test case name
        setTestCaseName("staffEditPageVerificationTC3");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC2: Verify that a user with the right permission is able to view all of the fields for any Staff Edit page\n" +
                        " * Covers AC of:\n" +
                        " * AC2: The Staff Edit page must not show the following fields, if a user does not have access:\n" +
                        " *      name, email, password, password confirmation, phone, role, submit button\n" +
                        " * AC3: The Staff Edit page must show an Access Denied message, if a user does not have access\n" +
                        " * AC8: The Staff Edit page must not be accessible by:\n" +
                        " *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk, Terminal,\n" +
                        " *      Guest with account, Corporation, Guest without an account\n" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        setStaffUser();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Verify that user is denied access to Staff Edit page
        driver.get(getEnvironment() + "/staff/" + staffUuid + "/edit");
        staffEditPage = new StaffEditPage(getDriver(), getReport(), getEnvironment(), staffUuid, name);
        staffEditPage.validateAccessDeniedToPage();

        // Assert the page fields of the Staff Edit page
        assertPageFieldsNotExistingForStaffEditPage();

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin") || login.getLoginUser().equals("staff-support") || login.getLoginUser().equals("staff-manager") || login.getLoginUser().equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-admin")){
            groupRatePlansPage = login.groupRatePlansPage;
            groupRatePlansPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-manager") || login.getLoginUser().equals("hotelier-manager") || login.getLoginUser().equals("hotelier-front-desk")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("hotelier-terminal")){
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("guest") || login.getLoginUser().equals("corporation")) {
            availabilityPage = login.availabilityPage;
            availabilityPage.validatePage();
        }
    }

    /*
     * Set staff user to edit
     *
     */
    public void setStaffUser() {
        currentData = new CurrentData(getReport(), getIteration());
        // Set the staff user to use in test
        testDataStaffUserPath = getTestDataPath() + "StaffUser.json";
        testDataStaffUserPath = currentData.getStaffUser(testDataStaffUserPath);

        String[] editStaffValues = new String[]{"name", "email", "phone", "role", "uuid"};
        getReport().appendExport("INFO: Using data in file: " + testDataStaffUserPath);
        JSONArray editStaffDetails = data.returnJSONArray(testDataStaffUserPath, editStaffValues);
        name = editStaffDetails.get(0).toString();
        email = editStaffDetails.get(1).toString();
        phone = editStaffDetails.get(2).toString();
        role = editStaffDetails.get(3).toString();
        staffUuid = editStaffDetails.get(4).toString();
    }

    public void assertPageFieldsForStaffEditPageNonStaffAdmin() {
        assertion.assertElementExists(driver,staffEditPage.nameLocator);
        assertion.assertElementExists(driver,staffEditPage.emailLocator);
        assertion.assertElementExists(driver,staffEditPage.passwordLocator);
        assertion.assertElementExists(driver,staffEditPage.passwordConfirmationLocator);
        assertion.assertElementExists(driver,staffEditPage.phoneLocator);
        assertion.assertElementExists(driver,staffEditPage.submitButtonLocator);
    }

    public void assertPageFieldsForStaffEditPageStaffAdmin() {
        assertion.assertElementExists(driver,staffEditPage.nameLocator);
        assertion.assertElementExists(driver,staffEditPage.emailLocator);
        assertion.assertElementExists(driver,staffEditPage.passwordLocator);
        assertion.assertElementExists(driver,staffEditPage.passwordConfirmationLocator);
        assertion.assertElementExists(driver,staffEditPage.phoneLocator);
        assertion.assertElementExists(driver,staffEditPage.roleLocator);
        assertion.assertElementExists(driver,staffEditPage.submitButtonLocator);
    }

    public void assertPageFieldsNotExistingForStaffEditPage() {
        // Saves 35s
        genericMethods.setImplicitlyWaitTime(driver,1);
        By[] elements = {   staffEditPage.nameLocator,
                staffEditPage.emailLocator,
                staffEditPage.passwordLocator,
                staffEditPage.passwordConfirmationLocator,
                staffEditPage.phoneLocator,
                staffEditPage.roleLocator,
                staffEditPage.submitButtonLocator};
        assertion.assertElementsDoNotExist(driver, elements);
        genericMethods.setImplicitlyWaitTime(driver,10);
    }
}