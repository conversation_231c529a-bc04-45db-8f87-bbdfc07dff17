import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.openqa.selenium.By;
import org.testng.annotations.Test;

    /**
     *
     * All the tests relating to the page for the creation of Hoteliers
     *
     * AC1: The Create Hotelier page must have the following fields, if a user has access:
     *      name, role, email-address, confirm email address, phone, submit button
     *
     * AC2: The Create Hotelier page must not show the following fields, if a user does not have access:
     *      name, role, email-address, confirm email address, phone, submit button
     *
     * AC3: The Create Hotelier page must show an Access Denied message, if a user does not have access
     *
     * AC4: The Create Hotelier page must be accessible by a Staff Admin, Staff Support, Staff Manager and Staff Sales user
     *
     * AC5: The Create Hotelier page must be accessible by:
     *      Group Admin, Group Manager, Hotelier Manager
     *      that have access to that hotel.
     *
     * AC6: The Create Hotelier page must not be accessible by:
     *      Hotelier Front Desk, Terminal, Guest with account, Corporation
     *      that have access to that hotel.
     *
     * AC7: The Create Hotelier page must not be accessible by:
     *      <PERSON> Admin, Group Manager, Hotelier Manager, Hotelier Front Desk
     *      Terminal, Guest with account, Corporation, Guest without an account
     *      that do not have access to that hotel.
     *
     */
public class HoteliersCreatePageVerification extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    CurrentData currentData;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    AvailabilityPage availabilityPage;
    GroupRatePlansPage groupRatePlansPage;
    HoteliersPage hoteliersPage;
    HoteliersCreatePage hoteliersCreatePage;

    // Variables used throughout test
    String testDataHotelPath = "";
    String testDataLoginPath = "";
    String hotelName = "";
    String hotelSlug = "";
    int hoteliers = 0;

    /**
     *
     * This test covers the acceptance criteria of:
     * AC1: The Create Hotelier page must have the following fields, if a user has access:
     *      name, role, email-address, confirm email address, phone, submit button
     *
     * AC4: The Create Hotelier page must be accessible by a Staff Admin, Staff Support, Staff Manager and Staff Sales user
     *
     * AC5: The Create Hotelier page must be accessible by:
     *      Group Admin, Group Manager, Hotelier Manager
     *      that have access to that hotel.
     *
     *
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to view all of the fields on the Create Hotelier page
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Staff Admin user, that is able to log in
     * TD2: A Staff Support user, that is able to log in
     * TD3: A Staff Manager user, that is able to log in
     * TD4: A Staff Sales user, that is able to log in
     * TD5: A Hotelier Manager, that has access to a hotel, that is able to log in
     * TD6: A Group Admin user, that has access to a hotel, that is able to log in
     * TD7: A Group Manager, that has access to a hotel, that is able to log in
     *
     */
    @Test(groups={"hoteliersCreatePageVerificationTC1"})
    public void hoteliersCreatePageVerificationTC1() {
        // Set test case name
        setTestCaseName("hoteliersCreatePageVerificationTC1");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataHotelPath = getTestDataPath() + "Hotel.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to view all of the fields on the Create Hotelier page\n" +
                        " * Covers AC of:\n" +
                        " * AC1: The Create Hotelier page must have the following fields, if a user has access:\n" +
                        " *      name, role, email-address, confirm email address, phone, submit button\n" +
                        " *\n" +
                        " * AC4: The Create Hotelier page must be accessible by a Staff Admin, Staff Support, Staff Manager and Staff Sales user\n" +
                        " *\n" +
                        " * AC5: The Create Hotelier page must be accessible by:\n" +
                        " *      Group Admin, Group Manager, Hotelier Manager\n" +
                        " *      that have access to that hotel.\n" +
                        " *****");

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());
        data = new Data();

        // Start the test
        // Log in
        login.loginRouting();

        // Set the data for the hotel
        setDataHotel();

        startOfTestPageRoutingTC1();

        // Retrieve hoteliers count at last possible moment. Needed due to change in behaviour when a hotelier already exists for a hotel
        hoteliers =  currentData.getHotelierCount(testDataHotelPath);
        getReport().appendExport("INFO: Hoteliers count: " + hoteliers);
        if(hoteliers > 0) {
            hoteliersPage = dashboardPage.horizontalOptionsPage.navigateToHoteliersPage();
            hoteliersPage.validatePage();
            hoteliersCreatePage = hoteliersPage.clickCreateHotelier();
        } else {
            hoteliersCreatePage = dashboardPage.horizontalOptionsPage.navigateToHoteliersCreatePageRedirect();
        }
        hoteliersCreatePage.validatePage();

        // Assert the page fields of the Hoteliers Create page
        assertPageFieldsForHoteliersCreatePage();

        setPassedReportLocation();
    }

    /**
     *
     * AC2: The Create Hotelier page must not show the following fields, if a user does not have access:
     *      name, role, email-address, confirm email address, phone, submit button
     *
     * AC3: The Create Hotelier page must show an Access Denied message, if a user does not have access
     *
     * AC6: The Create Hotelier page must not be accessible by:
     *      Hotelier Front Desk, Terminal, Guest with account, Corporation
     *      that have access to that hotel.
     *
     * This test covers the test cases of:
     * TC2: Verify that a user without permission is not able to view all of the fields on the Hoteliers Create page
     *      and is shown an Access Denied message
     *
     * TC2 requires the following test data to cover it:
     *
     * TD1: A Hotelier Front Desk user, that is able to log in, that has access to the hotel
     * TD2: A Terminal user, that is able to log in, that has access to the hotel
     * TD3: A Guest with account user, that is able to log in, that has access to the hotel
     * TD4: A Corporation with account user, that is able to log in, that has access to the hotel
     *
     */
    @Test(groups={"hoteliersCreatePageVerificationTC2"})
    public void hoteliersCreatePageVerificationTC2() {
        // Set test case name
        setTestCaseName("hoteliersCreatePageVerificationTC2");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataHotelPath = getTestDataPath() + "Hotel.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC2: Verify that a user without permission is not able to view all of the fields on the Hoteliers Create page\n" +
                        " *      and is shown an Access Denied message\n" +
                        " * Covers AC of:\n" +
                        " * AC2: The Create Hotelier page must not show the following fields, if a user does not have access:\n" +
                        " *      name, role, email-address, confirm email address, phone, submit button\n" +
                        " *\n" +
                        " * AC3: The Create Hotelier page must show an Access Denied message, if a user does not have access\n" +
                        " *\n" +
                        " * AC6: The Create Hotelier page must not be accessible by:\n" +
                        " *      Hotelier Front Desk, Terminal, Guest with account, Corporation\n" +
                        " *      that have access to that hotel.\n" +
                        " *****");

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());
        data = new Data();

        // Start the test
        // Log in
        login.loginRouting();

        // Set the data for the hotel
        setDataHotel();

        startOfTestPageRoutingTC2();

        hoteliersCreatePage.validateAccessDeniedToPage();

        // Assert the page fields do not exist of the Hoteliers Create page
        assertPageFieldsNotExistingForHoteliersCreatePage();

        setPassedReportLocation();
    }

    /**
     *
     * AC2: The Create Hotelier page must not show the following fields, if a user does not have access:
     *      name, role, email-address, confirm email address, phone, submit button
     *
     * AC3: The Create Hotelier page must show an Access Denied message, if a user does not have access
     *
     * AC7: The Create Hotelier page must not be accessible by:
     *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk
     *      Terminal, Guest with account, Corporation, Guest without an account
     *      that do not have access to that hotel.
     *
     * This test covers the test cases of:
     * TC3: Verify that a user without permission, that does not have access to a certain hotel, is not able to view
     *      all of the fields on the Hoteliers Create page and is shown an Access Denied message
     *
     * TC3 requires the following test data to cover it:
     *
     * TD1: A Hotelier Manager user, that is able to log in, that does not have access to a hotel
     * TD2: A Hotelier Front Desk user, that is able to log in, that does not have access to a hotel
     * TD3: A Terminal user, that is able to log in, that does not have access to a hotel
     * TD4: A Guest with account user, that is able to log in, that does not have access to a hotel
     * TD5: A Corporation with account user, that is able to log in, that does not have access to a hotel
     * TD6: A Group Admin user, that is able to log in, that does not have access to a hotel
     * TD7: A Group Manager user, that is able to log in, that does not have access to a hotel
     * TD8: A Guest without an account user, that does not have access to a hotel
     */
    @Test(groups={"hoteliersCreatePageVerificationTC3"})
    public void hoteliersCreatePageVerificationTC3() {
        // Set test case name
        setTestCaseName("hoteliersCreatePageVerificationTC3");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataHotelPath = getTestDataPath() + "Hotel.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC3: Verify that a user without permission, that does not have access to a certain hotel, is not able to view\n" +
                        " *      all of the fields on the Hoteliers Create page and is shown an Access Denied message\n" +
                        " * Covers AC of:\n" +
                        " * AC2: The Create Hotelier page must not show the following fields, if a user does not have access:\n" +
                        " *      name, role, email-address, confirm email address, phone, submit button\n" +
                        " *\n" +
                        " * AC3: The Create Hotelier page must show an Access Denied message, if a user does not have access\n" +
                        " *\n" +
                        " * AC7: The Create Hotelier page must not be accessible by:\n" +
                        " *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk\n" +
                        " *      Terminal, Guest with account, Corporation, Guest without an account\n" +
                        " *      that do not have access to that hotel.\n" +
                        " *****");

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());
        data = new Data();

        // Start the test
        // Log in
        login.loginRouting();

        // Set the data for the hotel
        setDataDifferentHotel();

        startOfTestPageRoutingTC3();

        // Navigate directly to the hoteliers create page
        driver.get(getEnvironment() + "/hotels/" + hotelSlug + "/hoteliers/create");
        hoteliersCreatePage = new HoteliersCreatePage(getDriver(), getReport(), getEnvironment(), hotelSlug, hotelName);
        hoteliersCreatePage.validateAccessDeniedToPage();

        // Assert the page fields do not exist of the Hoteliers Create page
        assertPageFieldsNotExistingForHoteliersCreatePage();

        setPassedReportLocation();
    }

    public void startOfTestPageRoutingTC1() {
        if (login.getLoginUser().equals("staff-admin") || login.getLoginUser().equals("staff-support") || login.getLoginUser().equals("staff-manager") || login.getLoginUser().equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
            hotelsPage.typeHotelNameInSearch(hotelName);
            hotelsPage = hotelsPage.clickSearch();
            hotelsPage.validatePage();
            dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        }
        else if (login.getLoginUser().equals("group-admin")){
            groupRatePlansPage = login.groupRatePlansPage;
            groupRatePlansPage.validatePage();
            groupRatePlansPage.clickGroupViewDropDown();
            dashboardPage = groupRatePlansPage.clickHotelGoToProperty(hotelSlug, hotelName);
        }
        else if (login.getLoginUser().equals("group-manager") || login.getLoginUser().equals("hotelier-manager")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
                dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
            }
        }
        dashboardPage.validatePage();
    }

    public void startOfTestPageRoutingTC2() {
        if (login.getLoginUser().equals("hotelier-front-desk")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
                hotelsPage.typeHotelNameInSearch(hotelName);
                hotelsPage = hotelsPage.clickSearch();
                hotelsPage.validatePage();
                dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
                dashboardPage.validatePage();
            }
            // Retrieve hoteliers count at last possible moment. Needed due to change in behaviour when a hotelier already exists for a hotel
            hoteliers =  currentData.getHotelierCount(testDataHotelPath);
            getReport().appendExport("INFO: Hoteliers count: " + hoteliers);
            if(hoteliers > 0) {
                hoteliersPage = dashboardPage.horizontalOptionsPage.navigateToHoteliersPage();
                hoteliersPage.validatePage();
                hoteliersCreatePage = hoteliersPage.clickCreateHotelier();
            } else {
                hoteliersCreatePage = dashboardPage.horizontalOptionsPage.navigateToHoteliersCreatePageRedirect();
            }
        }
        else if (login.getLoginUser().equals("hotelier-terminal")){
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
            driver.get(getEnvironment() + "/hotels/" + hotelSlug + "/hoteliers/create");
            hoteliersCreatePage = new HoteliersCreatePage(getDriver(), getReport(), getEnvironment(), hotelSlug, hotelName);
        }
        else if (login.getLoginUser().equals("guest") || login.getLoginUser().equals("corporation")) {
            availabilityPage = login.availabilityPage;
            availabilityPage.validatePage();
            driver.get(getEnvironment() + "/hotels/" + hotelSlug + "/hoteliers/create");
            hoteliersCreatePage = new HoteliersCreatePage(getDriver(), getReport(), getEnvironment(), hotelSlug, hotelName);
        }
    }

    public void startOfTestPageRoutingTC3() {
        if (login.getLoginUser().equals("group-admin")){
            groupRatePlansPage = login.groupRatePlansPage;
            groupRatePlansPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-manager") || login.getLoginUser().equals("hotelier-manager") || login.getLoginUser().equals("hotelier-front-desk")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("hotelier-terminal")){
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("guest") || login.getLoginUser().equals("corporation")) {
            availabilityPage = login.availabilityPage;
            availabilityPage.validatePage();
        }
    }

    public void setDataHotel() {
        testDataHotelPath = login.currentData.getHotel(testDataHotelPath);
        String[] hotelValues = new String[]{"slug", "name"};
        JSONArray hotelDetails = data.returnJSONArray(testDataHotelPath, hotelValues);
        hotelSlug = hotelDetails.get(0).toString();
        hotelName = hotelDetails.get(1).toString();
    }

    public void setDataDifferentHotel() {
        testDataHotelPath = login.currentData.getDifferentHotel(testDataHotelPath, login.hotelDetails);
        String[] hotelValues = new String[]{"slug", "name"};
        JSONArray hotelDetails = data.returnJSONArray(testDataHotelPath, hotelValues);
        hotelSlug = hotelDetails.get(0).toString();
        hotelName = hotelDetails.get(1).toString();
    }

    public void assertPageFieldsForHoteliersCreatePage() {
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierNameLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierRoleLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierEmailLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierConfirmationEmailLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.hotelierTelephoneNumberLocator);
        assertion.assertElementExists(driver,hoteliersCreatePage.submitButtonLocator);
    }

    public void assertPageFieldsNotExistingForHoteliersCreatePage() {
        // Saves 35s
        genericMethods.setImplicitlyWaitTime(driver,1);
        By[] elements = {hoteliersCreatePage.hotelierNameLocator,
                hoteliersCreatePage.hotelierRoleLocator,
                hoteliersCreatePage.hotelierEmailLocator,
                hoteliersCreatePage.hotelierConfirmationEmailLocator,
                hoteliersCreatePage.hotelierTelephoneNumberLocator,
                hoteliersCreatePage.submitButtonLocator
        };
        assertion.assertElementsDoNotExist(driver, elements);
        genericMethods.setImplicitlyWaitTime(driver,10);
    }
}