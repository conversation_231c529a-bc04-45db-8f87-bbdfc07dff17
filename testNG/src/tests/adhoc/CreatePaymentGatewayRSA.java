import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;
import org.json.simple.JSONObject;

import java.time.Duration;

public class CreatePaymentGatewayRSA extends BaseTest
{
    // required classes
    SoftAssertions softAssertions;
    Data data;
    SoftAssert softAssert;
    FileDownload fileDownload;
    FileUpload fileUpload;
    FileZipping fileZipping;
    GenericMethods genericMethods;
    Login login;
    HotelsPage hotelsPage;
    CreateHotelPage createHotelPage;
    DashboardPage dashboardPage;
    SettingsPage settingsPage;

    String testDataLoginPath = "";

    @Test(groups={"createPaymentGatewayRSA"})
    public void createPaymentGatewayRSA() throws InterruptedException {
        // Set test case name
        setTestCaseName("createPaymentGatewayRSA");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Adhoc - Create Payment Gateway for RSA" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        data = new Data();

        softAssert = new SoftAssert();
        softAssertions = new SoftAssertions(getDriver(), getReport(), softAssert);

        fileDownload = new FileDownload();
        fileUpload = new FileUpload();
        fileZipping = new FileZipping();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();
        if(getHeadless().equals("false")) {
            new WebDriverWait(driver, Duration.ofSeconds(10)).until(ExpectedConditions.invisibilityOfElementLocated(hotelsPage.messageSuccess));
        }
        // create new hotel
        createHotelPage = hotelsPage.clickCreateHotelLink();
        createHotelPage.validatePage();

        String hotelName = "hotelAutomation" + data.getRandomString(5, "numeric");
        String hotelSlug = data.getRandomString(10, "lowercase");

        if(getHeadless().equals("false")) {
            new WebDriverWait(driver, Duration.ofSeconds(10)).until(ExpectedConditions.invisibilityOfElementLocated(createHotelPage.messageSuccess));
        }
        createHotelPage.typeSlug(hotelSlug);
        createHotelPage.typeName(hotelName);
        JSONObject randomAddress = data.getRandomAddress();
        createHotelPage.typeAddress(randomAddress.get("line1").toString());
        createHotelPage.typePostcode(randomAddress.get("postcode").toString());
        createHotelPage.typeCountry(randomAddress.get("country").toString());
        createHotelPage.typePhone(data.getRandomString(10, "numeric"));
        createHotelPage.typeEmail(data.getRandomString(10, "lowercase") + "@example.com");
        createHotelPage.typeDescription(data.getRandomString(50, "any"));
        createHotelPage.typePrivacyLink("https://www.high-level-software.com");
        createHotelPage.typeTermsLink(data.getRandomString(50, "any"));
        createHotelPage.typeCancellationLink(data.getRandomString(50, "any"));
        createHotelPage.clearCheckinFrom();
        createHotelPage.typeCheckinFrom(data.getRandomMinutes("AM"));
        createHotelPage.clearCheckoutBy();
        createHotelPage.typeCheckoutBy(data.getRandomMinutes("PM"));
        createHotelPage.typeTaxRate(data.getRandomString(2, "numeric"));
        createHotelPage.typeTaxNumber(data.getRandomString(5, "numeric"));
        createHotelPage.selectStatus("Live");
        dashboardPage = createHotelPage.clickSubmit();
        dashboardPage.validatePage();
        if(getHeadless().equals("false")) {
            new WebDriverWait(driver, Duration.ofSeconds(10)).until(ExpectedConditions.invisibilityOfElementLocated(dashboardPage.messageSuccess));
        }

        settingsPage = dashboardPage.navigateToHotelSettingsPage();
        settingsPage.validatePage();

        // navigate to rsa key creation page
        settingsPage.clickCreateMyRSAKeys();
//        softAssertions.waitForExpectedURL(getEnvironment() + "/hotels/" + hotelSlug + "/key/create");

        // navigate to new tab screen to download key
        driver.get(getEnvironment() + "/hotels/" + hotelSlug + "/key/generate");
        softAssertions.waitForExpectedURL(getEnvironment() + "/hotels/" + hotelSlug + "/key/generate");

        // download key and verify the download
        settingsPage.clickGenerateRSAKeys();
        fileDownload.verifyDownloadWithFileName(driver, softAssert, hotelName + " Security Keys.zip", false);

        // navigate to key regeneration page to upload private & public keys
        driver.get(getEnvironment() + "/hotels/" + hotelSlug + "/key/create");
        softAssertions.waitForExpectedURL(getEnvironment() + "/hotels/" + hotelSlug + "/key/create");

        // unzip previous download key zipfile
        fileZipping.UnZipFile(System.getProperty("user.dir") + "/testFiles/", hotelName + " Security Keys.zip");

        // upload the public key
        fileUpload.UploadFile(driver, settingsPage.publicKeyBrowse(), System.getProperty("user.dir") + "/testFiles/", "key.public", false);
        
        settingsPage.clickIUnderstand();
        settingsPage.confirmPasswordField("Password123?");
        settingsPage.confirmCreateRsaKeys();

        // need to hard refresh page otherwise locator for 'ClickIsPrimary' becomes tempermental
        Thread.sleep(1000);
        driver.get(getEnvironment() + "/hotels/" + hotelSlug + "/edit");
        softAssertions.waitForExpectedURL(getEnvironment() + "/hotels/" + hotelSlug + "/edit");
        settingsPage.clickIsPrimary();

        settingsPage.clickWishToChangeGateway();
        settingsPage.savePrimaryGateway();

        // Give time for saving payment gateway request to happen
        Thread.sleep(1000);

        // check all assertions made
        softAssert.assertAll();
        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        // Else not handled in this test
    }
}