import org.testng.annotations.Test;
import org.json.simple.JSONArray;

    /**
     *
     * Adhoc test - Staff <PERSON><PERSON>gin Fail
     *
     */
public class StaffAdminLoginFail extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Assertion assertion;
    Data data;
    StaffLoginPage staffLoginPage;

    String testDataLoginPath = "";

        /**
     *
     * Adhoc test - Staff Admin Login Fail
     *
     * Navigate to the appropriate URL
     * Log in using invalid credentials
     * Wait until the user is returned to the staff/login page
     * Verify it is actually the staff/login page
     * Verify the error text on the staff/login page, stating: Login failed. Username or password incorrect
     *
     */
    @Test(groups={"staffAdminLoginFail"})
    public void staffAdminLoginFail() {
        // Set test case name
        setTestCaseName("staffAdminLoginFail");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Adhoc - Staff Admin Login Fail" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        data = new Data();

        // Parse the login details
        String[] loginValues = new String[]{"email", "password", "user"};
        JSONArray loginDetails = data.returnJSONArray(testDataLoginPath, loginValues);
        String email = loginDetails.get(0).toString();
        String password = loginDetails.get(1).toString();
        String user = loginDetails.get(2).toString();

        driver.get(getEnvironment() + "/staff/login");
        staffLoginPage = new StaffLoginPage(getDriver(), getReport(), getEnvironment());
        staffLoginPage.validatePage();

        staffLoginPage.typeEmail(email);
        staffLoginPage.typePassword(password);
        staffLoginPage = staffLoginPage.submitLoginExpectingFailure();
        staffLoginPage.validatePage();

        getReport().appendExport("INFO: " + staffLoginPage.returnErrorMessage());
        genericMethods.checkText("Login failed. Username or password incorrect", staffLoginPage.returnErrorMessage());

        setPassedReportLocation();
    }
}