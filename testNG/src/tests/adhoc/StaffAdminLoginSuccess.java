import org.testng.annotations.Test;

    /**
     *
     * Adhoc test - Staff Admin Login Success
     *
     */
public class StaffAdminLoginSuccess   extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    HotelsPage hotelsPage;

    String testDataLoginPath = "";

        /**
     *
     * Adhoc test - Staff Admin Login Success
     *
     * Navigate to the appropriate URL
     * Get the email and password for the corresponding test data
     * Log in using valid credentials
     * Wait until the user is on the hotels page
     * Verify it is actually the hotels page
     * Verify the success text on the hotels page, stating: Logged in
     *
     */
    @Test(groups={"staffAdminLoginSuccess"})
    public void staffAdminLoginSuccess() {
        // Set test case name
        setTestCaseName("staffAdminLoginSuccess");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Adhoc - Staff Admin Login Success" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(),getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();
        getReport().appendExport("INFO: " + hotelsPage.returnSuccessMessage());
        genericMethods.checkText(hotelsPage.returnSuccessMessage(), "Logged in");

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        // Else not handled in this test
    }
}