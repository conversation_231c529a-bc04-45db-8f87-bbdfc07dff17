import org.json.simple.JSONObject;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

    /**
     *
     * Adhoc - Staff Login with testing out Soft Assertions
     *
     */
public class StaffLoginSoftAssertionTest extends BaseTest {
    // Required classes
    SoftAssertions softAssertions;
    GenericMethods genericMethods;
    StaffLoginPage staffLoginPage;
    HotelsPage hotelsPage;
    SoftAssert softAssert;

    String testDataLoginPath = "";

        /**
     *
     * Adhoc - Staff Login with testing out Soft Assertions
     *
     */
    @Test(groups={"staffLoginSoftAssertionTest"})
    public void staffLoginSoftAssertionTest() {
        // Set test case name
        setTestCaseName("staffLoginSoftAssertionTest");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Adhoc - Staff Login with testing out Soft Assertions" +
                        " *****");

        softAssert = new SoftAssert();
        softAssertions = new SoftAssertions(getDriver(), getReport(), softAssert);
        genericMethods = new GenericMethods(getReport());
        staffLoginPage = new StaffLoginPage(getDriver(), getReport(), getEnvironment());
        hotelsPage = new HotelsPage(getDriver(), getReport(), getEnvironment(), new JSONObject());

        getReport().appendExport("STEP: Navigate to the staff login page.");
        driver.get(getEnvironment() + staffLoginPage.pageUrl);
        staffLoginPage.validatePage();

        // Check that the page returns a successful response code
        softAssertions.httpResponseCodeViaGet(getEnvironment() + staffLoginPage.pageUrl);

        // Check we are on expected url
        softAssertions.waitForExpectedURL( getEnvironment() + staffLoginPage.pageUrl);

        // Submit invalid credentials
        getReport().appendExport("INFO: Entering invalid user credentials.");
        staffLoginPage.loginAs("<EMAIL>", "WrongPassword");
        staffLoginPage.validatePage();

        // Verify login error message
        getReport().appendExport("INFO: Validating error message: Login failed. Username or password incorrect");
        softAssertions.compareText(staffLoginPage.returnErrorMessage(), "Login failed. Username or password incorrect");

        // Submit valid credentials
        getReport().appendExport("INFO: Entering valid user credentials.");
        staffLoginPage.loginAs("<EMAIL>", "Password123?");

        // Validate this was a successful login
        softAssertions.waitForExpectedURL(getEnvironment() + hotelsPage.pageUrl);

        // Check all assertions made
        softAssert.assertAll();

        setPassedReportLocation();
    }
}