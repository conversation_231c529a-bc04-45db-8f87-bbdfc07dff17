import org.json.simple.JSONObject;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

    /**
     *
     * Adhoc - Hotelier Login with testing out Soft Assertions
     *
     */
public class HotelierLoginSoftAssertionTest extends BaseTest {
    // Required classes
    SoftAssertions softAssertions;
    GenericMethods genericMethods;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    SoftAssert softAssert;

    /**
     *
     * Adhoc - Hotelier Login with testing out Soft Assertions
     *
     */
    @Test(groups={"hotelierLoginSoftAssertionTest"})
    public void hotelierLoginSoftAssertionTest() {
        // Set test case name
        setTestCaseName("hotelierLoginSoftAssertionTest");

        // Start reporting
        setFailedReportLocation();

        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Adhoc - Hotelier Login with testing out Soft Assertions" +
                        " *****");

        softAssert = new SoftAssert();
        softAssertions = new SoftAssertions(getDriver(), getReport(), softAssert);
        genericMethods = new GenericMethods(getReport());
        hotelierLoginPage = new HotelierLoginPage(getDriver(), getReport(), getEnvironment());
        hotelsPage = new HotelsPage(getDriver(), getReport(), getEnvironment(), new JSONObject());

        getReport().appendExport("STEP: Navigate to the hotelier login page.");
        driver.get(getEnvironment() + hotelierLoginPage.pageUrl);
        hotelierLoginPage.validatePage();

        // Check that the page returns a successful response code
        softAssertions.httpResponseCodeViaGet(getEnvironment() + hotelierLoginPage.pageUrl);

        // Check we are on expected url
        softAssertions.waitForExpectedURL(getEnvironment() + hotelierLoginPage.pageUrl);

        // Submit invalid credentials
        getReport().appendExport("INFO: Entering invalid user credentials.");
        hotelierLoginPage.loginAs("<EMAIL>", "WrongPassword");
        hotelierLoginPage.validatePage();

        // Verify login error message
        getReport().appendExport("INFO: Validating error message: Login failed. Username or password incorrect");
        softAssertions.compareText(hotelierLoginPage.returnErrorMessage(), "Login failed. Username or password incorrect");

        // Submit valid credentials
        getReport().appendExport("INFO: Entering valid user credentials.");
        hotelierLoginPage.loginAs("<EMAIL>", "shady");

        // Validate this was a successful login
        softAssertions.waitForExpectedURL(getEnvironment() + "/password/strength");

        // Check all assertions made
        softAssert.assertAll();
        setPassedReportLocation();
    }
}