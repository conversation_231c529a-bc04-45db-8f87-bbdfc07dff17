import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import io.github.bonigarcia.wdm.WebDriverManager;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Simple Staff Login Test
 * This test demonstrates a basic login to the PMS as a staff member
 */
public class SimpleStaffLoginTest {
    // Required classes
    WebDriver driver;
    GenericMethods genericMethods;
    StaffLoginPage staffLoginPage;
    HotelsPage hotelsPage;
    Data data;
    Report report;

    String environment = "http://localhost:58000";
    String email = "<EMAIL>";
    String password = "Password123?";

    @BeforeMethod
    public void setup() {
        // Initialize report
        report = new Report();
        report.setLocation("reports/SimpleStaffLoginTest.txt");
        report.appendExport("\n *****\n * Start of test: Simple Staff Login Test\n *****");

        // Initialize WebDriver with WebDriverManager
        WebDriverManager.chromedriver().setup();
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless=new");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        driver = new ChromeDriver(options);

        // Initialize required objects
        genericMethods = new GenericMethods(report);
        data = new Data();
    }

    @Test
    public void staffLogin() throws IOException {
        try {
            // Navigate to staff login page
            report.appendExport("STEP: Navigate to the Staff Login page.");
            driver.get(environment + "/staff/login");

            // Print page title and URL for debugging
            report.appendExport("INFO: Page title: " + driver.getTitle());
            report.appendExport("INFO: Current URL: " + driver.getCurrentUrl());

            // Take screenshot for debugging
            File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
            Path directory = Paths.get("reports");
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }
            Path destination = Paths.get("reports/login_page_debug_" + System.currentTimeMillis() + ".png");
            Files.copy(screenshot.toPath(), destination);
            report.appendExport("INFO: Screenshot saved to: " + destination.toAbsolutePath());

            // Print page source for debugging
            report.appendExport("INFO: Page source: " + driver.getPageSource().substring(0, 500) + "...");

            // Test completed successfully
            report.appendExport("TEST COMPLETED: Staff login test completed with debugging information.");
        } catch (Exception e) {
            report.appendExport("TEST FAILED: " + e.getMessage());
            throw e;
        }
    }

    @AfterMethod
    public void tearDown() {
        report.appendExport("\n *****\n * End of test\n *****");
        report.exportReport();
        if (driver != null) {
            driver.quit();
        }
    }
}
