import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;
import org.openqa.selenium.By;

import java.time.Duration;

public class CreatePayment extends BaseTest
{
    // required classes
    SoftAssertions softAssertions;
    Data data;
    SoftAssert softAssert;
    GenericMethods genericMethods;
    Login login;
    HotelsPage hotelsPage;

    BookingReferencePage bookingReferencePage;

    String testDataLoginPath = "";

    @Test(groups={"createPayment"})
    public void createPayment() {
        // Set test case name
        setTestCaseName("createPayment");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Adhoc - Create Payment" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        data = new Data();

        softAssert = new SoftAssert();
        softAssertions = new SoftAssertions(getDriver(), getReport(), softAssert);

        // Log in
        login.loginRouting();
        startOfTestPageRouting();
        if(getHeadless().equals("false")) {
            new WebDriverWait(driver, Duration.ofSeconds(10)).until(ExpectedConditions.invisibilityOfElementLocated(hotelsPage.messageSuccess));
        }

        // Navigate to a booking (needs to be changed to a dynamic hotel & booking id)
        driver.get(getEnvironment() + "/hotels/cardiff/bookings/**********");
        bookingReferencePage = new BookingReferencePage(getDriver(), getReport(), getEnvironment(), "cardiff", "Cardiff Plaza", "");
        bookingReferencePage.validatePage();

        // Click 'Add Payment'
        bookingReferencePage.clickFirstAddPayment();

        // Click select payment method
        bookingReferencePage.selectSoftPaymentCreatePaymentMethod("Cash");

        // Enter Amount
        bookingReferencePage.enterSoftPaymentCreatePaymentAmount("5.67");
    
        // Enter Note
        bookingReferencePage.enterSoftPaymentCreatePaymentNote("Test Automation");

        // Click SAVE
        bookingReferencePage.clickSoftPaymentCreatePaymentSave();

        bookingReferencePage.clickSoftPaymentProceed();

        BookingReferencePage bookingReferencePage1 = bookingReferencePage.clickSoftPaymentClose();
        bookingReferencePage1.validatePage();

        // Expand Booking
        bookingReferencePage1.expandBooking();

        // Verify payment is recorded on booking
        driver.findElement(By.xpath("//*[contains(text(),'5.67')]"));

        softAssert.assertAll();
        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        // Else not handled in this test
    }
}