import org.testng.annotations.Test;

    /**
     *
     * Adhoc - No Staff Account Availability Room Booking
     *
     */
public class NoAccountAvailabilityRoomBooking extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    AvailabilityPage availabilityPage;
    AvailabilityGuestPage availabilityGuestPage;
    AvailabilityConfirmPage availabilityConfirmPage;
    BookingsPage bookingsPage;
    BookingReferencePage bookingReferencePage;

    String testDataLoginPath = "";

    /**
     *
     * Adhoc - No Staff Account Availability Room Booking
     *
     */
    @Test(groups={"noAccountAvailabilityRoomBooking"})
    public void noAccountAvailabilityRoomBooking() {
        // Set test case name
        setTestCaseName("noAccountAvailabilityRoomBooking");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Adhoc - No Account Availability Room Booking" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Do guest booking here
        driver.get(getEnvironment() + "/hotels/cardiff/availability");
        availabilityPage = new AvailabilityPage(getDriver(), getReport(), getEnvironment(), "cardiff", "Cardiff Plaza");
        availabilityPage.validatePage();

        availabilityPage.clickShowRatesForRoomType("lux");
        availabilityPage = availabilityPage.clickAddToBookingForRate("lux", "Base rate");
        availabilityPage.validatePage();

        availabilityGuestPage = availabilityPage.clickGuestDetailsProgressButton();
        availabilityGuestPage.validatePage();

        availabilityGuestPage.enterFirstName("bam bam");
        availabilityGuestPage.enterLastName("warner");
        availabilityGuestPage.enterEmail("<EMAIL>");
        availabilityGuestPage.enterConfirmationEmail("<EMAIL>");
        availabilityGuestPage.enterPhone("0123456789");

        availabilityGuestPage.clickEnterAddressManuallyButton();
        availabilityGuestPage.enterAddressLine1("100 Mackintosh");
        availabilityGuestPage.enterAddressLine2("Place");
        availabilityGuestPage.enterCity("Cardiff");
        availabilityGuestPage.enterCounty("Cardiff");
        availabilityGuestPage.enterPostcode("CF24 4RN");

        availabilityGuestPage.enterCardNumber("4976");
        availabilityGuestPage.enterCardNumber("3500");
        availabilityGuestPage.enterCardNumber("0000");
        availabilityGuestPage.enterCardNumber("6891");

        availabilityGuestPage.enterCardHolderName("Geoff Wayne");
        availabilityGuestPage.enterCardExpireMonth("12");
        availabilityGuestPage.enterCardExpireYear("2025");
        availabilityGuestPage.enterCardSecurityNumber("341");

        availabilityGuestPage.selectEmailPreference(true);
        availabilityGuestPage.selectMailPreference(false);
        availabilityGuestPage.selectSmsPreference(false);

        availabilityGuestPage = availabilityGuestPage.clickConfirmBookingAsUserNotLoggedIn3DS();
        availabilityGuestPage.validate3DSecurePage();

        availabilityConfirmPage = availabilityGuestPage.clickSubmitButton3DSSuccess();
        availabilityConfirmPage.validatePage();
        String bookingReference = availabilityConfirmPage.getBookingReference();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Navigate to a hotels Dashboard page page
        hotelsPage.typeHotelNameInSearch("Cardiff Plaza");

        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel("cardiff", "Cardiff Plaza");

        dashboardPage.validatePage();
        bookingsPage = dashboardPage.horizontalOptionsPage.navigateToBookingsPage();
        bookingsPage.validatePage();

        bookingsPage.typeReferenceInSearch(bookingReference);
        bookingsPage = bookingsPage.clickSearch();
        bookingsPage.validatePage();

        bookingReferencePage =  bookingsPage.selectBooking(bookingReference);
        bookingReferencePage.validatePage();

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        // Else not handled in this test
    }
}