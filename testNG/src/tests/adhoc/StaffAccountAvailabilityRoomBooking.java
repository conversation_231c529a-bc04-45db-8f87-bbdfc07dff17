import org.testng.annotations.Test;

    /**
     *
     * Adhoc - Staff Account Availability Room Booking
     *
     */
public class StaffAccountAvailabilityRoomBooking extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    AvailabilityPage availabilityPage;
    AvailabilityGuestPage availabilityGuestPage;
    BookingReferencePage bookingReferencePage;

    String testDataLoginPath = "";

        /**
     *
     * Adhoc - Staff Account Availability Room Booking
     *
     */
    @Test(groups={"staffAccountAvailabilityRoomBooking"})
    public void staffAccountAvailabilityRoomBooking() {
        // Set test case name
        setTestCaseName("staffAccountAvailabilityRoomBooking");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Adhoc - Staff Account Availability Room Booking" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Navigate to a hotels Dashboard page page
        hotelsPage.typeHotelNameInSearch("Cardiff Plaza");

        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel("cardiff", "Cardiff Plaza");

        dashboardPage.validatePage();
        availabilityPage = dashboardPage.horizontalOptionsPage.navigateToAvailabilityPage();
        availabilityPage.validatePage();

        availabilityPage.clickShowRatesForRoomType("dbl");
        availabilityPage = availabilityPage.clickAddToBookingForRate("dbl", "Base rate");
        availabilityPage.validatePage();

        availabilityGuestPage = availabilityPage.clickGuestDetailsProgressButton();
        availabilityGuestPage.validatePage();

        availabilityGuestPage.enterFirstName("Fred");
        availabilityGuestPage.enterLastName("Flintstone");
        availabilityGuestPage.enterEmail("<EMAIL>");
        availabilityGuestPage.enterConfirmationEmail("<EMAIL>");
        availabilityGuestPage.enterPhone("01234654789");

        availabilityGuestPage.clickEnterAddressManuallyButton();
        availabilityGuestPage.enterAddressLine1("30 Mackintosh");
        availabilityGuestPage.enterAddressLine2("Place");
        availabilityGuestPage.enterCity("Cardiff");
        availabilityGuestPage.enterCounty("Cardiff");
        availabilityGuestPage.enterPostcode("CF24 4RQ");

        availabilityGuestPage.enterCardNumber("4976");
        availabilityGuestPage.enterCardNumber("0000");
        availabilityGuestPage.enterCardNumber("0000");
        availabilityGuestPage.enterCardNumber("3436");

        availabilityGuestPage.enterCardHolderName("Geoff no3ds");
        availabilityGuestPage.enterCardExpireMonth("12");
        availabilityGuestPage.enterCardExpireYear("2025");
        availabilityGuestPage.enterCardSecurityNumber("452");

        availabilityGuestPage.selectEmailPreference(false);
        availabilityGuestPage.selectMailPreference(false);
        availabilityGuestPage.selectSmsPreference(true);

        bookingReferencePage = availabilityGuestPage.clickConfirmBookingAsUserLoggedIn();
        bookingReferencePage.validatePage();

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        // Else not handled in this test
    }
}