import org.openqa.selenium.By;
import org.testng.annotations.Test;
import org.json.simple.JSONArray;

    /**
     * 
     * All the tests relating to editing of Staff Users
     * 
     * AC1: A Staff Admin user must be able to edit themselves
     * AC2: A Staff Admin user must be able to edit a different Staff Admin user
     * AC3: A Staff Admin user must be able to edit a Staff Support user
     * AC4: A Staff Admin user must be able to edit a Staff Manager user
     * AC5: A Staff Admin user must be able to edit a Staff Sales user
     *
     * AC6: A Staff Support user must be able to edit only their own Staff Edit page
     *
     * AC7: A Staff Manager user must be able to edit only their own Staff Edit page
     *
     * AC8: A Staff Sales user must be able to edit only their own Staff Edit page
     *
     * AC9: The following users must not be able to edit a Staff Admin, Staff Support,
     *      Staff Manager or Staff Sales user:
     *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk, Terminal,
     *      Guest with account, Corporation, Guest without an account
     */

public class EditStaffUser extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Export export;
    Assertion assertion;
    CurrentData currentData;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    AvailabilityPage availabilityPage;
    GroupRatePlansPage groupRatePlansPage;
    StaffPage staffPage;
    StaffEditPage staffEditPage;

    String testDataLoginPath = "";
    String testDataStaffUserPath = "";
    String testDataExportPath = "";

    // Variables used throughout test
    String staffName = "";
    String staffEmail = "";
    String staffPhone = "";
    String staffPassword = "";
    String staffRole = "";
    String staffUuid = "";
    
    /**
     *
     * This test covers the acceptance criteria of:
     *
     * AC6: A Staff Support user must be able to edit only their own Staff Edit page
     *
     * AC7: A Staff Manager user must be able to edit only their own Staff Edit page
     *
     * AC8: A Staff Sales user must be able to edit only their own Staff Edit page
     *
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to edit themselves
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Staff Support user, that is able to log in, that edits themselves
     * TD2: A Staff Manager user, that is able to log in, that edits themselves
     * TD3: A Staff Sales user, that is able to log in, that edits themselves
     *
     */
    @Test(groups={"editStaffUserTC1"})
    public void editStaffUserTC1() {
        // Set test case name
        setTestCaseName("editStaffUserTC1");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to the staff user
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to edit themselves\n" +
                        " * Covers AC of:\n" +
                        " * AC6: A Staff Support user must be able to edit only their own Staff Edit page\n" +
                        " * AC7: A Staff Manager user must be able to edit only their own Staff Edit page\n" +
                        " * AC8: A Staff Sales user must be able to edit only their own Staff Edit page\n" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());

        // Set the staff user to use in test
        setStaffUser();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();
        getReport().appendExport("STEP: Navigate to the current users Staff edit page.");
        driver.findElement(By.linkText(staffName)).click();
        getReport().appendExport("INFO: User should be on the current users Staff edit page.");

        staffEditPage = new StaffEditPage(getDriver(), getReport(), getEnvironment(), staffUuid, staffName);
        staffEditPage.validatePage();

        // Set the data for the test
        // Just password for now
        setDataPassword();

        // Insert the data into page for the test
        staffEditPage.editPassword(staffPassword);
        staffEditPage.editPasswordConfirmation(staffPassword);

        // Assert that the values inserted are actually there
        assertValuesEditedTC1();

        // Navigate to Staff page
        hotelsPage = staffEditPage.clickSubmitBackToHotels();
        hotelsPage.validatePage();

        // Add the data set to an export file
        addDataToExportFile();

        // Export the file
        export.exportFile(testDataExportPath);

        // Export created staff user to the path current data staff.
        // Needed here anyway, as the user has been created
        export.exportFileCurrentData(getPathCurrentDataStaff() + staffRole + "_" + staffUuid);

        setPassedReportLocation();
    }

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC1: A Staff Admin user must be able to edit themselves
     * AC2: A Staff Admin user must be able to edit a different Staff Admin user
     * AC3: A Staff Admin user must be able to edit a Staff Support user
     * AC4: A Staff Admin user must be able to edit a Staff Manager user
     * AC5: A Staff Admin user must be able to edit a Staff Sales user
     *
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to edit a Staff user
     *
     * TC2 requires the following test data to cover it:
     * TD1: A Staff Admin user, that is able to log in, that edits themselves
     * TD2: A Staff Admin user, that is able to log in, that edits a different Staff Admin user
     * TD3: A Staff Admin user, that is able to log in, that edits a Staff Support user
     * TD4: A Staff Admin user, that is able to log in, that edits a Staff Manager user
     * TD5: A Staff Admin user, that is able to log in, that edits a Staff Sales user
     * 
     */
    @Test(groups={"editStaffUserTC2"})
    public void editStaffUserTC2() {
        // Set test case name
        setTestCaseName("editStaffUserTC2");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to the staff user
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC2: Verify that a user with the right permission is able to edit a Staff user\n" +
                        " * Covers AC of:\n" +
                        " * AC1: A Staff Admin user must be able to edit themselves\n" +
                        " * AC2: A Staff Admin user must be able to edit a different Staff Admin user\n" +
                        " * AC3: A Staff Admin user must be able to edit a Staff Support user\n" +
                        " * AC4: A Staff Admin user must be able to edit a Staff Manager user\n" +
                        " * AC5: A Staff Admin user must be able to edit a Staff Sales user\n" +
                        " *****");

        // Set the staff user to use in test
        setStaffUser();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        getReport().appendExport("STEP: Navigate to the Staff page.");
        driver.get(getEnvironment() + "/staff");
        getReport().appendExport("INFO: User should be on the Staff page.");
        staffPage = new StaffPage(driver, getReport(), getEnvironment());

        // Validate the Staff page that the user has landed on
        staffPage.validatePage();

        // Navigate to Staff Edit page
        staffEditPage = staffPage.clickStaffName(staffName, staffUuid);
        staffEditPage.validatePage();

        // Set the data for the test
        // Just password for now
        setDataPassword();

        // Insert the data into page for the test
        staffEditPage.editPassword(staffPassword);
        staffEditPage.editPasswordConfirmation(staffPassword);

        // Assert that the values inserted are actually there
        assertValuesEditedTC2();

        // Navigate to Staff page
        staffPage = staffEditPage.clickSubmit();
        staffPage.validateAfterEditPage(staffUuid);

        // Add the data set to an export file
        addDataToExportFile();

        // Export the file
        export.exportFile(testDataExportPath);

        // Export created staff user to the path current data staff.
        // Needed here anyway, as the user has been created
        export.exportFileCurrentData(getPathCurrentDataStaff() + staffRole + "_" + staffUuid);

        setPassedReportLocation();
    }

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC9: The following users must not be able to edit a Staff Admin, Staff Support,
     *      Staff Manager or Staff Sales user,
     *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk, Terminal,
     *      Guest with account, Corporation, Guest without an account
     *
     * This test covers the test cases of:
     * TC3: Verify that a user without permission is not able to edit a Staff user
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Staff Support user, that is able to log in
     * TD2: A Staff Manager user, that is able to log in
     * TD3: A Staff Sales user, that is able to log in
     * TD4: A Group Admin user, that is able to log in
     * TD5: A Group Manager user, that is able to log in
     * TD6: A Hotelier Manager user, that is able to log in
     * TD7: A Hotelier Front Desk user, that is able to log in
     * TD8: A Terminal user, that is able to log in
     * TD9: A Guest with account user, that is able to log in
     * TD10: A Corporation user, that is able to log in
     * TD11: A Guest without an account
     * 
     */
    @Test(groups={"editStaffUserTC3"})
    public void editStaffUserTC3() {
        // Set test case name
        setTestCaseName("editStaffUserTC3");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC3:  Verify that a user without permission is not able to edit a Staff user\n" +
                        " * Covers AC of:\n" +
                        " * AC9: The following users must not be able to edit a Staff Admin, Staff Support,\n" +
                        " *      Staff Manager or Staff Sales user,\n" +
                        " *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk, Terminal,\n" +
                        " *      Guest with account, Corporation, Guest without an account\n" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        setStaffUser();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Verify that user is denied access to Staff Edit page
        driver.get(getEnvironment() + "/staff/" + staffUuid + "/edit");
        staffEditPage = new StaffEditPage(getDriver(), getReport(), getEnvironment(), staffUuid, staffName);
        staffEditPage.validateAccessDeniedToPage();

        // Incomplete

        // Assert the page fields of the Staff Edit page
//        assertPageFieldsNotExistingForStaffEditPage();

//        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin") || login.getLoginUser().equals("staff-support") || login.getLoginUser().equals("staff-manager") || login.getLoginUser().equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-admin")){
            groupRatePlansPage = login.groupRatePlansPage;
            groupRatePlansPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-manager") || login.getLoginUser().equals("hotelier-manager") || login.getLoginUser().equals("hotelier-front-desk")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("hotelier-terminal")){
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("guest") || login.getLoginUser().equals("corporation")) {
            availabilityPage = login.availabilityPage;
            availabilityPage.validatePage();
        }
    }

    /*
     * Set staff user to edit
     *
     */
    public void setStaffUser() {
        currentData = new CurrentData(getReport(), getIteration());
        // Set the staff user to use in test
        testDataStaffUserPath = getTestDataPath() + "StaffUser.json";
        testDataStaffUserPath = currentData.getStaffUser(testDataStaffUserPath);

        String[] editStaffValues = new String[]{"name", "email", "phone", "role", "uuid"};
        getReport().appendExport("INFO: Using data in file: " + testDataStaffUserPath);
        JSONArray editStaffDetails = data.returnJSONArray(testDataStaffUserPath, editStaffValues);
        staffName = editStaffDetails.get(0).toString();
        staffEmail = editStaffDetails.get(1).toString();
        staffPhone = editStaffDetails.get(2).toString();
        staffRole = editStaffDetails.get(3).toString();
        staffUuid = editStaffDetails.get(4).toString();
    }

    /*
     * Set the data to use to be random data, data from a file or data in a chain for a chain of tests
     *
     */
    public void setData() {
        if (getDataToUse().equals("random")) {
            staffName = data.getRandomName("firstName") + " " + data.getRandomName("lastName");
            staffEmail = data.getRandomString(10, "lowercase") + "@high-level-software.com";
            staffPhone = data.getRandomString(10, "numeric");
            staffPassword = genericMethods.getRandomPassword();
            // Parse the role details
            String testDataRolePath = "/src/testdata/" + getIteration() + "/pms/createStaffUser/" + getTestData() + "Role.json";
            String[] roleValues = new String[]{"role"};
            JSONArray roleDetails = data.returnJSONArray(testDataRolePath, roleValues);
            staffRole = roleDetails.get(0).toString();
        }
        else {
            String[] editStaffValues = new String[]{"name", "email", "phone", "password", "role"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editStaffDetails = data.returnJSONArray(getDataToUse(), editStaffValues);
            staffName = editStaffDetails.get(0).toString();
            staffEmail = editStaffDetails.get(1).toString();
            staffPhone = editStaffDetails.get(2).toString();
            staffPassword = editStaffDetails.get(3).toString();
            staffRole = editStaffDetails.get(4).toString();
        }
    }

    public void setDataName() {
        if (getDataToUse().equals("random")) {
            staffName = data.getRandomName("firstName") + " " + data.getRandomName("lastName");
        }
        else {
            String[] editStaffValues = new String[]{"name"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editStaffDetails = data.returnJSONArray(getDataToUse(), editStaffValues);
            staffName = editStaffDetails.get(0).toString();
        }
    }

    public void setDataEmail() {
        if (getDataToUse().equals("random")) {
            staffEmail = data.getRandomString(10, "lowercase") + "@high-level-software.com";
        }
        else {
            String[] editStaffValues = new String[]{"email"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editStaffDetails = data.returnJSONArray(getDataToUse(), editStaffValues);
            staffEmail = editStaffDetails.get(0).toString();
        }
    }

    public void setDataPhone() {
        if (getDataToUse().equals("random")) {
            staffPhone = data.getRandomString(10, "numeric");
        }
        else {
            String[] editStaffValues = new String[]{"phone"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editStaffDetails = data.returnJSONArray(getDataToUse(), editStaffValues);
            staffPhone = editStaffDetails.get(0).toString();
        }
    }

    public void setDataPassword() {
        if (getDataToUse().equals("random")) {
            staffPassword = genericMethods.getRandomPassword();
        }
        else {
            String[] editStaffValues = new String[]{"password"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editStaffDetails = data.returnJSONArray(getDataToUse(), editStaffValues);
            staffPassword = editStaffDetails.get(0).toString();
        }
    }

    public void setDataRole() {
        if (getDataToUse().equals("random")) {
            // Parse the role details
            String testDataRolePath = "/src/testdata/" + getIteration() + "/pms/createStaffUser/" + getTestData() + "Role.json";
            String[] roleValues = new String[]{"role"};
            JSONArray roleDetails = data.returnJSONArray(testDataRolePath, roleValues);
            staffRole = roleDetails.get(0).toString();
        }
        else {
            String[] editStaffValues = new String[]{"role"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray editStaffDetails = data.returnJSONArray(getDataToUse(), editStaffValues);
            staffRole = editStaffDetails.get(0).toString();
        }
    }

    public void addDataToExportFile() {
        export.putExportData("name", staffName);
        export.putExportData("email", staffEmail);
        export.putExportData("phone", staffPhone);
        export.putExportData("password", staffPassword);
        export.putExportData("role", staffRole);
        if(staffRole.equals("Administrator"))
            export.putExportData("user", "staff-admin");
        else if(staffRole.equals("Support"))
            export.putExportData("user", "staff-support");
        else if(staffRole.equals("Manager"))
            export.putExportData("user", "staff-manager");
        else if(staffRole.equals("Sales"))
            export.putExportData("user", "staff-sales");
        export.putExportData("uuid", staffUuid);
    }

    public void typeData() {
        staffEditPage.editName(staffName);
        staffEditPage.editEmail(staffEmail);
        staffEditPage.editPassword(staffPassword);
        staffEditPage.editPasswordConfirmation(staffPassword);
        staffEditPage.editPhone(staffPhone);
        staffEditPage.editRole(staffRole);
    }

    public void assertValuesEditedTC1() {
        assertion.assertValueInField(driver, staffEditPage.nameLocator, staffName);
        assertion.assertValueInField(driver, staffEditPage.emailLocator, staffEmail);
        assertion.assertValueInField(driver, staffEditPage.passwordLocator, staffPassword);
        assertion.assertValueInField(driver, staffEditPage.passwordConfirmationLocator, staffPassword);
        assertion.assertValueInField(driver, staffEditPage.phoneLocator, staffPhone);
    }

    public void assertValuesEditedTC2() {
        assertion.assertValueInField(driver, staffEditPage.nameLocator, staffName);
        assertion.assertValueInField(driver, staffEditPage.emailLocator, staffEmail);
        assertion.assertValueInField(driver, staffEditPage.passwordLocator, staffPassword);
        assertion.assertValueInField(driver, staffEditPage.passwordConfirmationLocator, staffPassword);
        assertion.assertValueInField(driver, staffEditPage.phoneLocator, staffPhone);
        assertion.assertTextInDropDownField(driver, staffEditPage.roleLocator, staffRole);
    }
}