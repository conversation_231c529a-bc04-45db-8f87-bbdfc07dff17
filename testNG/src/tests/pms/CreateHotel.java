import org.testng.annotations.Test;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

    /**
     *
     * All the tests relating to Creation of Hotels
     *
     * AC1: A Staff Admin user must be able to create a new hotel
     * AC2: A Staff Support user must be able to create a new hotel
     * AC3: A Staff Manager user must be able to create a new hotel
     * AC4: A Staff Sales user must be able to create a new hotel
     *
     * AC5: A Staff Manager user is not able to check the Rates Overview page
     * AC6: A Staff Sales user is not able to check the Rates Overview page
     *
     * AC7: The following users must not be able to create a new hotel:
     *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk, Terminal,
     *      Guest with account, Corporation, Guest without an account
     */
public class CreateHotel extends BaseTest {
    
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Export exportCreatedHotel;
    Export exportCreatedHotelOptions;
    Export exportCreatedHotelCategories;
    Assertion assertion;
    CurrentData currentData;
    HotelsPage hotelsPage;
    CreateHotelPage createHotelPage;
    AvailabilityPage availabilityPage;
    GroupRatePlansPage groupRatePlansPage;
    DashboardPage dashboardPage;
    HotelierLoginPage hotelierLoginPage;
    SettingsPage settingsPage;
    OptionsPage optionsPage;
    CategoriesPage categoriesPage;
    RoomTypesCreatePage roomTypesCreatePage;
    StaffLoginPage staffLoginPage;
    RatesOverviewPage ratesOverviewPage;

    String testDataLoginPath = "";
    String testDataExportPathCreatedHotel = "";
    String testDataExportPathCreatedHotelOptions = "";
    String testDataExportPathCreatedHotelCategories = "";

    // Variables used throughout test
    String status = "";
    String slug = "";
    String name = "";
    String address = "";
    String postcode = "";
    String country = "";
    String phone = "";
    String email = "";
    String description = "";
    String privacyLink = "";
    String termsLink = "";
    String cancellationLink = "";
    String checkinFrom = "";
    String checkoutBy = "";
    String taxRate = "";
    String taxNumber = "";
    String hotelUuid = "";
    String hotelId = "";

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC1: A Staff Admin user must be able to create a new hotel
     * AC2: A Staff Support user must be able to create a new hotel
     *
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to create a hotel
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Staff Admin user, that is able to log in
     * TD2: A Staff Support user, that is able to log in
     * 
     */
    @Test(groups={"createHotelTC1"})
    public void createHotelTC1() {
        // Set test case name
        setTestCaseName("createHotelTC1");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPathCreatedHotel = getTestDataPath() + "ExportPathCreatedHotel.json";
        testDataExportPathCreatedHotelOptions = getTestDataPath() + "ExportPathCreatedHotelOptions.json";
        testDataExportPathCreatedHotelCategories = getTestDataPath() + "ExportPathCreatedHotelCategories.json";

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        exportCreatedHotel = new Export();
        exportCreatedHotelOptions = new Export();
        exportCreatedHotelCategories = new Export();
        assertion = new Assertion(getReport());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to create a hotel\n" +
                        " * Covers AC of:\n" +
                        " * AC1: A Staff Admin user must be able to create a new hotel\n" +
                        " * AC2: A Staff Support user must be able to create a new hotel\n" +
                        " *****");

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Navigate to Create Hotel page
        createHotelPage = hotelsPage.clickCreateHotelLink();
        createHotelPage.validatePage();

        // Set the data for the test
        setDataCreateHotelPage();
        // Insert the data into page for the test
        typeDataCreateHotelPage();
        // Assert that the values inserted are actually there
        assertCreateHotelPageValuesInserted();

        // Navigate to Dashboard page
        dashboardPage = createHotelPage.clickSubmit();
        dashboardPage.validatePage();

        // Get the hotel uuid and set it in a variable
        setHotelUuid();

        // Add the data set to an export file
        addDataToExportCreatedHotelFile();
        addDataToExportCreatedHotelOptionsFile();
        addDataToExportCreatedHotelCategoriesFile();

        // Export the file
        exportCreatedHotel.exportFile(testDataExportPathCreatedHotel);
        exportCreatedHotelOptions.exportFile(testDataExportPathCreatedHotelOptions);
        exportCreatedHotelCategories.exportFile(testDataExportPathCreatedHotelCategories);

        // Export current data file
        exportCreatedHotel.exportFileCurrentData(getPathCurrentDataHotels() + slug + "/hotel_" + slug);
        exportCreatedHotelOptions.exportFileCurrentData(getPathCurrentDataHotels() + slug + "/options_" + slug);
        exportCreatedHotelCategories.exportFileCurrentData(getPathCurrentDataHotels() + slug + "/categories_" + slug);

        settingsPage = dashboardPage.navigateToHotelSettingsPage();
        settingsPage.validatePage();
        assertSettingsPageCreatedHotelValues();

        optionsPage = settingsPage.clickOptions();
        optionsPage.validatePage();
        assertOptionsPageCreatedHotelOptions();

        categoriesPage = optionsPage.horizontalOptionsPage.navigateToCategoriesPage();
        categoriesPage.validatePage();
        assertCategoriesPageCreatedHotelCategories();

        roomTypesCreatePage = categoriesPage.horizontalOptionsPage.navigateToRoomTypesCreatePageRedirect();
        roomTypesCreatePage.validatePage();
        roomTypesCreatePage.logout(login.getLoginUser());
        logoutRouting(login.getLoginUser(), roomTypesCreatePage.logout);

        // Log in again
        login.loginRouting();
        startOfTestPageRouting();

        hotelsPage.typeHotelNameInSearch(name);
        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel(slug, name);
        dashboardPage.validatePage();

        ratesOverviewPage = dashboardPage.horizontalOptionsPage.navigateToRatesOverviewPage();
        ratesOverviewPage.validatePage();
        ratesOverviewPage.validateNoRoomTypes();

        roomTypesCreatePage = ratesOverviewPage.clickCreateNewRoomType();
        roomTypesCreatePage.validatePage();

        setPassedReportLocation();
    }

    /**
     *
     * This test covers the acceptance criteria of:
     * AC3: A Staff Manager user must be able to create a new hotel
     * AC4: A Staff Sales user must be able to create a new hotel
     * AC5: A Staff Manager user is not able to check the Rates Overview page
     * AC6: A Staff Sales user is not able to check the Rates Overview page
     *
     * This test covers the test cases of:
     * TC2: Verify that a user with the right permission is able to create a hotel
     *      but is unable to verify via Rates Overview Pages
     *
     * TC2 requires the following test data to cover it:
     * TD1: A Staff Manager user, that is able to log in
     * TD2: A Staff Sales user, that is able to log in
     *
     */
    @Test(groups={"createHotelTC2"})
    public void createHotelTC2() {
        // Set test case name
        setTestCaseName("createHotelTC2");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPathCreatedHotel = getTestDataPath() + "ExportPathCreatedHotel.json";
        testDataExportPathCreatedHotelOptions = getTestDataPath() + "ExportPathCreatedHotelOptions.json";
        testDataExportPathCreatedHotelCategories = getTestDataPath() + "ExportPathCreatedHotelCategories.json";

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        exportCreatedHotel = new Export();
        exportCreatedHotelOptions = new Export();
        exportCreatedHotelCategories = new Export();
        assertion = new Assertion(getReport());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC2: Verify that a user with the right permission is able to create a hotel\n" +
                        " *      but is unable to verify via Rates Overview Pages\n" +
                        " * Covers AC of:\n" +
                        " * AC3: A Staff Manager user must be able to create a new hotel\n" +
                        " * AC4: A Staff Sales user must be able to create a new hotel\n" +
                        " * AC5: A Staff Manager user is not able to check the Rates Overview page\n" +
                        " * AC6: A Staff Sales user is not able to check the Rates Overview page\n" +
                        " *****");

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Navigate to Create Hotel page
        createHotelPage = hotelsPage.clickCreateHotelLink();
        createHotelPage.validatePage();

        // Set the data for the test
        setDataCreateHotelPage();
        // Insert the data into page for the test
        typeDataCreateHotelPage();
        // Assert that the values inserted are actually there
        assertCreateHotelPageValuesInserted();

        // Navigate to Dashboard page
        dashboardPage = createHotelPage.clickSubmit();
        dashboardPage.validatePage();

        // Get the hotel uuid and set it in a variable
        setHotelUuid();

        // Add the data set to an export file
        addDataToExportCreatedHotelFile();
        addDataToExportCreatedHotelOptionsFile();
        addDataToExportCreatedHotelCategoriesFile();

        // Export the file
        exportCreatedHotel.exportFile(testDataExportPathCreatedHotel);
        exportCreatedHotelOptions.exportFile(testDataExportPathCreatedHotelOptions);
        exportCreatedHotelCategories.exportFile(testDataExportPathCreatedHotelCategories);

        // Export current data file
        exportCreatedHotel.exportFileCurrentData(getPathCurrentDataHotels() + slug + "/hotel_" + slug);
        exportCreatedHotelOptions.exportFileCurrentData(getPathCurrentDataHotels() + slug + "/options_" + slug);
        exportCreatedHotelCategories.exportFileCurrentData(getPathCurrentDataHotels() + slug + "/categories_" + slug);

        settingsPage = dashboardPage.navigateToHotelSettingsPage();
        settingsPage.validatePage();
        assertSettingsPageCreatedHotelValues();

        optionsPage = settingsPage.clickOptions();
        optionsPage.validatePage();
        assertOptionsPageCreatedHotelOptions();

        categoriesPage = optionsPage.horizontalOptionsPage.navigateToCategoriesPage();
        categoriesPage.validatePage();
        assertCategoriesPageCreatedHotelCategories();

        roomTypesCreatePage = categoriesPage.horizontalOptionsPage.navigateToRoomTypesCreatePageRedirect();
        roomTypesCreatePage.validatePage();

        setPassedReportLocation();
    }

    /**
     * 
     * Covers acceptance criteria of:
     * AC7: The following users must not be able to create a new hotel:
     *      Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk, Terminal,
     *      Guest with account, Corporation, Guest without an account
     *
     * Covers test cases of:
     * TC3: Verify that a user without the permission to create a hotel, is not able to create a new hotel
     * 
     * TC3 requires the following test data to cover it:
     * TD1: A Group Admin user, with access to one hotel, that is able to log in
     * TD2: A Group Admin user, with access to multiple hotels, that is able to log in
     * TD3: A Group Manager user, with access to one hotel, that is able to log in
     * TD4: A Group Manager user, with access to multiple hotels, that is able to log in
     * TD5: A Hotelier Manager user, with access to one hotel, that is able to log in
     * TD6: A Hotelier Manager user, with access to multiple hotels, that is able to log in
     * TD7: A Hotelier Front Desk user, with access to one hotel, that is able to log in
     * TD8: A Hotelier Front Desk user, with access to multiple hotels, that is able to log in
     * TD9: A Terminal user, with access to one hotel, that is able to log in
     * TD10: A Terminal user, with access to multiple hotels, that is able to log in
     * TD11: A Guest user, on one hotel, that is able to log in
     * TD12: A Guest user, on multiple hotels, that is able to log in
     * TD13: A Corporation user, on one hotel, that is able to log in
     * TD14: A Corporation user, on multiple hotels, that is able to log in
     * TD15: A user not logged in, that is able to log in
     *
     */

    @Test(groups={"createHotelTC3"})
    public void createHotelTC3() {
        // Get the test data path
        String testDataPath = "/src/testdata/" + getIteration() + "/pms/createHotel/" + getTestData();

        // Parse the login details
        currentData = new CurrentData(getReport(), getIteration());
        String testDataLoginPath = currentData.getStaffUser(testDataPath+"Login.json");
        String[] loginValues = new String[]{"email", "password", "user"};
        JSONArray loginDetails = data.returnJSONArray(testDataLoginPath, loginValues);
        String email = loginDetails.get(0).toString();
        String password = loginDetails.get(1).toString();
        String user = loginDetails.get(2).toString();

        getReport().setLocation("src/testdata/" + getIteration() + "/report-data/pms/createHotel/" + getTestData() + "Report_Export.txt");

        getReport().appendExport("*****");
        getReport().appendExport("Start of test:");
        getReport().appendExport("TC3: Verify that a user without the permission to create a hotel, is not able to create a new hotel");
        getReport().appendExport("Covers AC of:");
        getReport().appendExport("AC7: The following users must not be able to create a new hotel:");
        getReport().appendExport("     Group Admin, Group Manager, Hotelier Manager, Hotelier Front Desk, Terminal,");
        getReport().appendExport("     Guest with account, Corporation, Guest without an account");
        getReport().appendExport("*****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Navigate to Create Hotel page - might not work!!
        createHotelPage = hotelsPage.clickCreateHotelLink();
        createHotelPage.validateAccessDeniedToPage();

        // Nothing to export? At the moment
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin") || login.getLoginUser().equals("staff-support") || login.getLoginUser().equals("staff-manager") || login.getLoginUser().equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-admin")){
            groupRatePlansPage = login.groupRatePlansPage;
            groupRatePlansPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-manager") || login.getLoginUser().equals("hotelier-manager") || login.getLoginUser().equals("hotelier-front-desk")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("hotelier-terminal")){
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("guest") || login.getLoginUser().equals("corporation")) {
            availabilityPage = login.availabilityPage;
            availabilityPage.validatePage();
        }
    }

    public void logoutRouting(String user, Logout logout) {
        if (user.equals("staff-admin") || user.equals("staff-support") || user.equals("staff-manager") || user.equals("staff-sales")) {
            staffLoginPage = logout.staffLoginPage;
            staffLoginPage.validatePage();
        }
    }

    /*
     * Set the data to use to be random data, data from a file or data in a chain for a chain of tests
     *
     */
    public void setDataCreateHotelPage() {
        if (getDataToUse().equals("random")) {
            status = "Live";
            slug = data.getRandomString(10, "lowercase");
            name = data.getRandomName("hotelName") + "_" + data.getRandomString(5, "numeric");
            JSONObject randomAddress = data.getRandomAddress();
            address = randomAddress.get("line1").toString();
            postcode = randomAddress.get("postcode").toString();
            country = randomAddress.get("country").toString();
            phone = data.getRandomString(10, "numeric");
            email = data.getRandomString(10, "lowercase") + "@high-level-software.com";
            description = data.getRandomString(50, "any");
            privacyLink = "https://www.high-level-software.com";
            termsLink = data.getRandomString(50, "any");
            cancellationLink = data.getRandomString(50, "any");
            checkinFrom = data.getRandomMinutes("AM");
            checkoutBy = data.getRandomMinutes("PM");
            taxRate = "20";
            taxNumber = data.getRandomString(5, "numeric");
        }
        else {
            String[] createHotelValues = new String[]{"status", "slug", "name", "address", "postcode","country", "phone", "email", "description",
                                                        "privacyLink", "termsLink", "cancellationLink", "checkinFrom", "checkoutBy", 
                                                        "taxRate", "taxNumber"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray createHotelDetails = data.returnJSONArray(getDataToUse(), createHotelValues);
            status = createHotelDetails.get(0).toString();
            slug = createHotelDetails.get(1).toString();
            name = createHotelDetails.get(2).toString();
            address = createHotelDetails.get(3).toString();
            postcode = createHotelDetails.get(4).toString();
            country = createHotelDetails.get(5).toString();
            phone = createHotelDetails.get(6).toString();
            email = createHotelDetails.get(7).toString();
            description = createHotelDetails.get(8).toString();
            privacyLink = createHotelDetails.get(9).toString();
            termsLink = createHotelDetails.get(10).toString();
            cancellationLink = createHotelDetails.get(11).toString();
            checkinFrom = createHotelDetails.get(12).toString();
            checkoutBy = createHotelDetails.get(13).toString();
            taxRate = createHotelDetails.get(14).toString();
            taxNumber = createHotelDetails.get(15).toString();
        }
    }

    public void addDataToExportCreatedHotelFile() {
        exportCreatedHotel.putExportData("status", status);
        exportCreatedHotel.putExportData("slug", slug);
        exportCreatedHotel.putExportData("name", name);
        exportCreatedHotel.putExportData("address", address);
        exportCreatedHotel.putExportData("postcode", postcode);
        exportCreatedHotel.putExportData("country", country);
        exportCreatedHotel.putExportData("phone", phone);
        exportCreatedHotel.putExportData("email", email);
        exportCreatedHotel.putExportData("description", description);
        exportCreatedHotel.putExportData("privacyLink", privacyLink);
        exportCreatedHotel.putExportData("termsLink", termsLink);
        exportCreatedHotel.putExportData("cancellationLink", cancellationLink);
        exportCreatedHotel.putExportData("checkinFrom", checkinFrom);
        exportCreatedHotel.putExportData("checkoutBy", checkoutBy);
        exportCreatedHotel.putExportData("taxRate", taxRate);
        exportCreatedHotel.putExportData("taxNumber", taxNumber);
        exportCreatedHotel.putArrayToMainJsonObject("hoteliers");
    }

    public void addDataToExportCreatedHotelOptionsFile() {
        exportCreatedHotelOptions.putToTempJsonObject("name", "availability.occupancy.max");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "availability.rates.noToggle");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "booking.regform");
        exportCreatedHotelOptions.putToTempJsonObject("value", "Passport Number\nCar Registration");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "booking.regform.prices");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "calendar.display");
        exportCreatedHotelOptions.putToTempJsonObject("value", "ALL_TYPES_WITH_PRIMARY_ROOMS");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "invoice.corporate.full-breakdown");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "invoice.guest.full-breakdown");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.booking.confirmation.log");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.booking.invoice.log");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.guest.booking.confirmation");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.hotel.booking.confirmation");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.rdx.guest.booking.confirmation");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putToTempJsonObject("name", "mail.rdx.hotel.booking.confirmation");
        exportCreatedHotelOptions.putToTempJsonObject("value", "1");
        exportCreatedHotelOptions.addToTempJsonArray();
        exportCreatedHotelOptions.putArrayToMainJsonObject("options");
    }

    public void addDataToExportCreatedHotelCategoriesFile() {
        exportCreatedHotelCategories.putToTempJsonObject("code", "food");
        exportCreatedHotelCategories.putToTempJsonObject("title", "Food");
        exportCreatedHotelCategories.putToTempJsonObject("color", "FF6961");
        exportCreatedHotelCategories.addToTempJsonArray();
        exportCreatedHotelCategories.putToTempJsonObject("code", "drink");
        exportCreatedHotelCategories.putToTempJsonObject("title", "Drink");
        exportCreatedHotelCategories.putToTempJsonObject("color", "77DD77");
        exportCreatedHotelCategories.addToTempJsonArray();
        exportCreatedHotelCategories.putToTempJsonObject("code", "miscellaneous");
        exportCreatedHotelCategories.putToTempJsonObject("title", "Miscellaneous");
        exportCreatedHotelCategories.putToTempJsonObject("color", "AEC6CF");
        exportCreatedHotelCategories.addToTempJsonArray();
        exportCreatedHotelCategories.putToTempJsonObject("code", "accommodation");
        exportCreatedHotelCategories.putToTempJsonObject("title", "Accommodation");
        exportCreatedHotelCategories.putToTempJsonObject("color", "FDFD96");
        exportCreatedHotelCategories.addToTempJsonArray();
        exportCreatedHotelCategories.putArrayToMainJsonObject("categories");
    }

    public void typeDataCreateHotelPage() {
        createHotelPage.selectStatus(status);
        createHotelPage.typeSlug(slug);
        createHotelPage.typeName(name);
        createHotelPage.typeAddress(address);
        createHotelPage.typePostcode(postcode);
        createHotelPage.typeCountry(country);
        createHotelPage.typePhone(phone);
        createHotelPage.typeEmail(email);
        createHotelPage.typeDescription(description);
        createHotelPage.typePrivacyLink(privacyLink);
        createHotelPage.typeTermsLink(termsLink);
        createHotelPage.typeCancellationLink(cancellationLink);
        createHotelPage.clearCheckinFrom();
        createHotelPage.typeCheckinFrom(checkinFrom);
        createHotelPage.clearCheckoutBy();
        createHotelPage.typeCheckoutBy(checkoutBy);     
        createHotelPage.typeTaxRate(taxRate);
        createHotelPage.typeTaxNumber(taxNumber);
    }

    public void assertCreateHotelPageValuesInserted() {
        assertion.assertValueInField(driver, createHotelPage.statusLocator, status.toLowerCase());
        assertion.assertValueInField(driver, createHotelPage.slugLocator, slug);
        assertion.assertValueInField(driver, createHotelPage.nameLocator, name);
        assertion.assertValueInField(driver, createHotelPage.addressLocator, address);
        assertion.assertValueInField(driver, createHotelPage.postcodeLocator, postcode);
        assertion.assertValueInField(driver, createHotelPage.countryLocator, country);
        assertion.assertValueInField(driver, createHotelPage.phoneLocator, phone);
        assertion.assertValueInField(driver, createHotelPage.emailLocator, email);
        assertion.assertValueInField(driver, createHotelPage.descriptionLocator, description);
        assertion.assertValueInField(driver, createHotelPage.privacyLinkLocator, privacyLink);
        assertion.assertValueInField(driver, createHotelPage.termsLocator, termsLink);
        assertion.assertValueInField(driver, createHotelPage.cancellationLocator, cancellationLink);
        assertion.assertValueInField(driver, createHotelPage.checkinFromLocator, checkinFrom);
        assertion.assertValueInField(driver, createHotelPage.checkoutByLocator, checkoutBy);
        assertion.assertValueInField(driver, createHotelPage.taxRateLocator, taxRate);
        assertion.assertValueInField(driver, createHotelPage.taxNumberLocator, taxNumber);
    }

    public void setHotelUuid() {
        AuthenticationRequests authenticationRequests = new AuthenticationRequests(report, getEnvironment());
        JSONObject token = authenticationRequests.postLoginUser(login.getLoginEmail(), login.getLoginPassword(), login.getLoginUser());
        JSONObject tokenResult = (JSONObject) token.get("result");
        String jwt = tokenResult.get("access_token").toString();

        HydraRequests hydraRequests = new HydraRequests(report, getEnvironment());
        JSONObject hotels = hydraRequests.postHotelsSearch(jwt, name);
        JSONArray hotelsResult = (JSONArray) hotels.get("results");
        for(int i=0; i<hotelsResult.size(); i++) {
            JSONObject hotel;
            hotel = (JSONObject) hotelsResult.get(i);
            String hotelSlug = (String) hotel.get("slug");
            if(hotelSlug.equals(slug)) {
                hotelUuid = (String) hotel.get("uuid");
                hotelId = hotel.get("id").toString();
            }
        }
    }

    public void assertSettingsPageCreatedHotelValues() {
        assertion.assertValueInField(driver, settingsPage.statusLocator, status.toLowerCase());
        assertion.assertValueInField(driver, settingsPage.slugLocator, slug);
        assertion.assertValueInField(driver, settingsPage.nameLocator, name);
        assertion.assertValueInField(driver, settingsPage.addressLocator, address);
        assertion.assertValueInField(driver, settingsPage.postcodeLocator, postcode);
        assertion.assertValueInField(driver, settingsPage.countryLocator, country);
        assertion.assertValueInField(driver, settingsPage.phoneLocator, phone);
        assertion.assertValueInField(driver, settingsPage.emailLocator, email);
        assertion.assertValueInField(driver, settingsPage.descriptionLocator, description);
        assertion.assertValueInField(driver, settingsPage.privacyLinkLocator, privacyLink);
        assertion.assertValueInField(driver, settingsPage.termsLocator, termsLink);
        assertion.assertValueInField(driver, settingsPage.cancellationLocator, cancellationLink);
        assertion.assertValueInField(driver, settingsPage.checkinFromLocator, checkinFrom + ":00");
        assertion.assertValueInField(driver, settingsPage.checkoutByLocator, checkoutBy+ ":00");
        assertion.assertValueInField(driver, settingsPage.taxRateLocator, taxRate);
        assertion.assertValueInField(driver, settingsPage.taxNumberLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.rdxCodeLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.isRdxEnabledLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.textColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.backgroundColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.textOnBackgroundColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.backgroundShadeColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.textOnBackgroundShadeColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.highlightColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.textOnHighlightColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.midlightColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.textOnMidlightColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.lowlightColourLocator, taxNumber);
//        assertion.assertValueInField(driver, settingsPage.textOnLowlightColourLocator, taxNumber);
    }

        public void assertOptionsPageCreatedHotelOptions() {
            assertion.assertTrue(optionsPage.optionAndValueExists("availability.occupancy.max", "1"), "availability.occupancy.max option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("availability.rates.noToggle", "1"), "availability.rates.noToggle option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("booking.regform", "Passport Number\nCar Registration"), "booking.regform option with a value of Passport Number\nCar Registration");
            assertion.assertTrue(optionsPage.optionAndValueExists("booking.regform.prices", "1"), "booking.regform.prices option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("calendar.display", "ALL_TYPES_WITH_PRIMARY_ROOMS"), "calendar.display option with a value of ALL_TYPES_WITH_PRIMARY_ROOMS");
            assertion.assertTrue(optionsPage.optionAndValueExists("invoice.corporate.full-breakdown", "1"), "invoice.corporate.full-breakdown option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("invoice.guest.full-breakdown", "1"), "invoice.guest.full-breakdown option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("mail.booking.confirmation.log", "1"), "mail.booking.confirmation.log option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("mail.booking.invoice.log", "1"), "mail.booking.invoice.log option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("mail.guest.booking.confirmation", "1"), "mail.guest.booking.confirmation option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("mail.hotel.booking.confirmation", "1"), "mail.hotel.booking.confirmation option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("mail.rdx.guest.booking.confirmation", "1"), "mail.rdx.guest.booking.confirmation option with a value of 1");
            assertion.assertTrue(optionsPage.optionAndValueExists("mail.rdx.hotel.booking.confirmation", "1"), "mail.rdx.hotel.booking.confirmation option with a value of 1");
        }

        public void assertCategoriesPageCreatedHotelCategories() {
            assertion.assertTrue(categoriesPage.categoryExists("FOOD"), "Category of FOOD");
            assertion.assertTrue(categoriesPage.categoryExists("DRINK"), "Category of DRINK");
            assertion.assertTrue(categoriesPage.categoryExists("MISCELLANEOUS"), "Category of MISCELLANEOUS");
            assertion.assertTrue(categoriesPage.categoryExists("ACCOMMODATION"), "Category of ACCOMMODATION");
        }
}