import org.apache.commons.io.FileUtils;
import org.openqa.selenium.By;
import org.testng.annotations.Test;
import org.json.simple.JSONArray;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
     * 
     * All the tests relating to Creation of Staff Users
     * 
     * AC1: A Staff Admin user must be able to create a new Staff Admin user
     * AC2: A Staff Admin user must be able to create a new Staff Support user
     * AC3: A Staff Admin user must be able to create a new Staff Manager user
     * AC4: A Staff Admin user must be able to create a new Staff Sales user
     * 
     * AC5: The following users must not be able to create a new Staff Admin, Staff Support,
     *      Staff Manager or Staff Sales user:
     *      Staff Support, Staff Manager, Staff Sales, Group Admin, Group Manager, Hotelier Manager,
     *      Hotelier Front Desk, Terminal, Guest with account, Corporation, Guest without an account
     */
public class CreateStaffUser extends BaseTest {
    
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Export export;
    Assertion assertion;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    StaffPage staffPage;
    StaffCreatePage staffCreatePage;
    AvailabilityPage availabilityPage;
    GroupRatePlansPage groupRatePlansPage;

    String testDataLoginPath = "";
    String testDataExportPath = "";
    String testDataRolePath = "";

    // Variables used throughout test
    String createdStaffUserName = "";
    String createdStaffUserEmail = "";
    String createdStaffUserPhone = "";
    String createdStaffUserRole = "";
    String createdStaffUserUuid = "";

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC1: A Staff Admin user must be able to create a new Staff Admin user
     * AC2: A Staff Admin user must be able to create a new Staff Support user
     * AC3: A Staff Admin user must be able to create a new Staff Manager user
     * AC4: A Staff Admin user must be able to create a new Staff Sales user
     *
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to create a new Staff user
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Staff Admin user, that is able to log in, that creates a Staff Admin user
     * TD2: A Staff Admin user, that is able to log in, that creates a Staff Support user
     * TD3: A Staff Admin user, that is able to log in, that creates a Staff Manager user
     * TD4: A Staff Admin user, that is able to log in, that creates a Staff Sales user
     * 
     */
    @Test(groups={"createStaffUserTC1"})
    public void createStaffUserTC1() {
        // Set test case name
        setTestCaseName("createStaffUserTC1");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataExportPath = getTestDataPath() + "ExportLocation.json";
        testDataRolePath = getTestDataPath() + "Role.json";

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(),getEnvironment(), getIteration(), testDataLoginPath);
        export = new Export();
        assertion = new Assertion(getReport());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to create a new Staff user\n" +
                        " * Covers AC of:\n" +
                        " * AC1: A Staff Admin user creating a new Staff Admin user\n" +
                        " * AC2: A Staff Admin user creating a new Staff Support user\n" +
                        " * AC3: A Staff Admin user creating a new Staff Manager user\n" +
                        " * AC4: A Staff Admin user creating a new Staff Sales user\n" +
                        " *****");

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        getReport().appendExport("STEP: Navigate to the Staff page.");
        driver.get(getEnvironment() + "/staff");
        getReport().appendExport("INFO: User should be on the Staff page.");
        staffPage = new StaffPage(driver, getReport(), getEnvironment());

        // Validate the Staff page that the user has landed on
        staffPage.validatePage();

        // Navigate to Staff Create page
        staffCreatePage = staffPage.clickCreate();
        staffCreatePage.validatePage();

        // Set the data for creating the staff user
        setDataForCreatingStaffUser();
        // Insert the data into the page for creating a staff user
        typeDataForStaffCreatePage();
        // Assert that the values inserted are actually there in the page for creating a staff user
        assertValuesInsertedForStaffCreatePage();

        // Create the staff user
        // User is navigated back to the Staff page
        staffPage = staffCreatePage.clickSubmit();
        staffPage.validatePage();

        // Set the createdStaffUserUuid
        setDataForCreatedStaffUserUuid();

        // Add the data set to an export file
        addDataToExportFileForCreatedStaffUser();

        // Export created staff user to the path current data staff.
        // Needed here anyway, as the user has been created
        export.exportFileCurrentData(getPathCurrentDataStaff() + createdStaffUserRole + "_" + createdStaffUserUuid);

        setPassedReportLocation();
    }

    /**
     * 
     * This test covers the acceptance criteria of:
     * AC5: The following users must not be able to create a new Staff Admin, Staff Support,
     *      Staff Manager or Staff Sales user:
     *      Staff Support, Staff Manager, Staff Sales, Group Admin, Group Manager, Hotelier Manager,
     *      Hotelier Front Desk, Terminal, Guest with account, Corporation, Guest without an account
     *
     * This test covers the test cases of:
     * TC2: Verify that a user without permission is not able to create a new Staff user
     *
     * TC1 requires the following test data to cover it: staff admin!!!!!!!!!!
     * TD1: A Staff Admin user, that is able to log in
     * TD2: A Staff Support user, that is able to log in
     * TD3: A Staff Manager user, that is able to log in
     * TD4: A Staff Sales user, that is able to log in
     * TD5: A Hotelier Manager user, that is able to log in
     * TD6: A Hotelier Front Desk user, that is able to log in
     * TD7: A Terminal user, that is able to log in
     *
     * TD8: A Guest with account user, that is able to log in
     * TD9: A Corporation user, that is able to log in
     * TD10: A Group Admin user, that is able to log in
     * TD11: A Group Manager user, that is able to log in
     * TD12: A Guest without an account
     *
     */
    @Test(groups={"createStaffUserTC2"})
    public void createStaffUserTC2() {
        // Set test case name
        setTestCaseName("createStaffUserTC2");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";
        testDataRolePath = getTestDataPath() + "Role.json";

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(),getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC2: Verify that a user without permission is not able to create a new Staff user\n" +
                        " * Covers AC of:\n" +
                        " * AC5: The following users must not be able to create a new Staff Admin, Staff Support,\n" +
                        " *      Staff Manager or Staff Sales user:\n" +
                        " *      Staff Support, Staff Manager, Staff Sales, Group Admin, Group Manager, Hotelier Manager,\n" +
                        " *      Hotelier Front Desk, Terminal, Guest with account, Corporation, Guest without an account\n" +
                        " *****");

        // Log in
        login.loginRouting();
        startOfTestPageRouting();
        setHtmlStaffPage(getEnvironment() + "/staff");

        driver.get("file://" + System.getProperty("user.dir") + "/" + "src/csrfPages/StaffPage" + getTestData() + ".html");

        driver.findElement(By.cssSelector("#submit")).click();
        staffPage = new StaffPage(getDriver(), getReport(), getEnvironment());

        staffPage.validateCsrfMessageOnPage();

        setPassedReportLocation();
    }


    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin") || login.getLoginUser().equals("staff-support") || login.getLoginUser().equals("staff-manager") || login.getLoginUser().equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-admin")){
            groupRatePlansPage = login.groupRatePlansPage;
            groupRatePlansPage.validatePage();
        }
        else if (login.getLoginUser().equals("group-manager") || login.getLoginUser().equals("hotelier-manager") || login.getLoginUser().equals("hotelier-front-desk")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("hotelier-terminal")){
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (login.getLoginUser().equals("guest") || login.getLoginUser().equals("corporation")) {
            availabilityPage = login.availabilityPage;
            availabilityPage.validatePage();
        }
    }

    /*
     * Set the data to use to be random data, data from a file or data in a chain for a chain of tests
     *
     */
    public void setDataForCreatingStaffUser() {
        if (getDataToUse().equals("random")) {
            createdStaffUserName = data.getRandomName("firstName") + " " + data.getRandomName("lastName") + " " + data.getRandomString(4,"any");
            createdStaffUserEmail = data.getRandomString(10, "lowercase") + "@high-level-software.com";
            createdStaffUserPhone = data.getRandomString(10, "numeric");
            getReport().appendExport("INFO: Using random data.");
            // Parse the role details
            String[] roleValues = new String[]{"role"};
            JSONArray roleDetails = data.returnJSONArray(testDataRolePath, roleValues);
            createdStaffUserRole = roleDetails.get(0).toString();
        }
        else {
            String[] createStaffValues = new String[]{"name", "email", "phone", "role"};
            getReport().appendExport("INFO: Using data in file: " + getDataToUse());
            JSONArray createStaffDetails = data.returnJSONArray(getDataToUse(), createStaffValues);
            createdStaffUserName = createStaffDetails.get(0).toString();
            createdStaffUserEmail = createStaffDetails.get(1).toString();
            createdStaffUserPhone = createStaffDetails.get(2).toString();
            createdStaffUserRole = createStaffDetails.get(3).toString();
        }
    }

    public void typeDataForStaffCreatePage() {
        staffCreatePage.typeName(createdStaffUserName);
        staffCreatePage.typeEmail(createdStaffUserEmail);
        staffCreatePage.typePhone(createdStaffUserPhone);
        staffCreatePage.typeRole(createdStaffUserRole);
    }

    public void assertValuesInsertedForStaffCreatePage() {
        assertion.assertValueInField(driver, staffCreatePage.nameLocator, createdStaffUserName);
        assertion.assertValueInField(driver, staffCreatePage.emailLocator, createdStaffUserEmail);
        assertion.assertValueInField(driver, staffCreatePage.phoneLocator, createdStaffUserPhone);
        assertion.assertTextInDropDownField(driver, staffCreatePage.roleLocator, createdStaffUserRole);
    }

    public void setDataForCreatedStaffUserUuid() {
        createdStaffUserUuid = staffPage.returnStaffUuidFromName(createdStaffUserName);
    }

    public void addDataToExportFileForCreatedStaffUser() {
        export.putExportData("name", createdStaffUserName);
        export.putExportData("email", createdStaffUserEmail);
        export.putExportData("phone", createdStaffUserPhone);
        export.putExportData("role", createdStaffUserRole);
        switch (createdStaffUserRole) {
            case "Administrator":
                export.putExportData("user", "staff-admin");
                break;
            case "Support":
                export.putExportData("user", "staff-support");
                break;
            case "Manager":
                export.putExportData("user", "staff-manager");
                break;
            case "Sales":
                export.putExportData("user", "staff-sales");
                break;
        }
        export.putExportData("uuid", createdStaffUserUuid);
    }

    public void setHtmlStaffPage(String page) {
        File htmlTemplateFile = new File(System.getProperty("user.dir")+ "/" + "src/csrfPages/StaffPageTemplate.html");
        String htmlString;
        try {
            htmlString = FileUtils.readFileToString(htmlTemplateFile, StandardCharsets.UTF_8);
            htmlString = htmlString.replace("$page", page);
            File newHtmlFile = new File(System.getProperty("user.dir") + "/" + "src/csrfPages/StaffPage" + getTestData() + ".html");
            FileUtils.writeStringToFile(newHtmlFile, htmlString, StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void deleteHtmlStaffPage() {

    }
}