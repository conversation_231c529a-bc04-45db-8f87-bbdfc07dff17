import org.testng.Assert;
import org.testng.annotations.Test;

/**
 *
 * Bug 35850 - BoxToBox not working
 *
 */
public class Bug35850BoxToBox extends BaseTest {

    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    HotelsPage hotelsPage;
    GroupsOverviewPage groupsOverviewPage;
    CreateGroupPage createGroupPage;

    String testDataLoginPath = "";

    /**
     *
     *
     */
    @Test(groups={"bug35850BoxToBoxTC1"})
    public void bug35850BoxToBoxTC1() throws InterruptedException {
        // Set test case name
        setTestCaseName("bug35850BoxToBoxTC1");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Bug 35850 - <PERSON>To<PERSON>ox not working" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Createhotelier here for a hotel...

        groupsOverviewPage = hotelsPage.clickCreateGroupLink();
        groupsOverviewPage.validatePage();
        createGroupPage = groupsOverviewPage.clickCreateNewGroup();
        createGroupPage.validatePage();

        // ISSUE WITH PAGINATION
        // POSSIBLY CHECK BY THE ONE THAT IS DRAGGED OVER HAS THE SAME HOTEL NAME/ADDRESS e.t.c
        // FILTERING TOO


        // Check size of hotels Unselected
        // Check size of hotels Selected
        // Check Hoteliers unselected and selected section is hidden
        // Move hotel left to right
        // Check size of Unselected - reduced by 1
        // Check size of Selected - increased by 1
        // Scroll to the end
        int countOfUnselectedHotels = createGroupPage.countOfUnselectedHotels();
        int countOfSelectedHotels = createGroupPage.countOfSelectedHotels();
        createGroupPage.scrollToViewHoteliersSection();
        createGroupPage.validateNoHoteliersLists();
        createGroupPage.dragHotelXLeftToRight("1");
        countOfUnselectedHotels = countOfUnselectedHotels - 1;
        countOfSelectedHotels = countOfSelectedHotels + 1;
        Assert.assertEquals(countOfUnselectedHotels, createGroupPage.countOfUnselectedHotels());
        Assert.assertEquals(countOfSelectedHotels, createGroupPage.countOfSelectedHotels());
        createGroupPage.scrollToViewSaveButton();

        // Check Hoteliers unselected and selected section is not hidden
        // Check size of hoteliers Unselected
        // Check size of hoteliers Selected
        // Move hotelier left to right
        // Check size of hoteliers Unselected - reduced by 1
        // Check size of hoteliers Selected - increased by 1
        createGroupPage.validateHotelierLists();
        int countOfUnselectedHoteliers = createGroupPage.countOfUnselectedHoteliers();
        int countOfSelectedHoteliers = createGroupPage.countOfSelectedHoteliers();
        createGroupPage.dragHotelierXLeftToRight(1);
        countOfUnselectedHoteliers = countOfUnselectedHoteliers - 1;
        countOfSelectedHoteliers = countOfSelectedHoteliers + 1;
        Assert.assertEquals(countOfUnselectedHoteliers, createGroupPage.countOfUnselectedHoteliers());
        Assert.assertEquals(countOfSelectedHoteliers, createGroupPage.countOfSelectedHoteliers());

        // Single right arrow hotelier

        // Single right arrow hotel
        // Check hotel counts
        // Single left arrow hotel
        // Check hotel counts
        // Drag right to left hotel
        // Check hotel counts
        // Double right arrow hotel
        // Check hotel unselected is now 0
        createGroupPage.selectXUnselectedHotel(1);
        createGroupPage.clickHotelsSingleRightArrow();
        countOfUnselectedHotels = countOfUnselectedHotels - 1;
        countOfSelectedHotels = countOfSelectedHotels + 1;
        Assert.assertEquals(countOfUnselectedHotels, createGroupPage.countOfUnselectedHotels());
        Assert.assertEquals(countOfSelectedHotels, createGroupPage.countOfSelectedHotels());
        createGroupPage.selectXSelectedHotel(1);
        createGroupPage.clickHotelsSingleLeftArrow();
        countOfUnselectedHotels = countOfUnselectedHotels + 1;
        countOfSelectedHotels = countOfSelectedHotels - 1;
        Assert.assertEquals(countOfUnselectedHotels, createGroupPage.countOfUnselectedHotels());
        Assert.assertEquals(countOfSelectedHotels, createGroupPage.countOfSelectedHotels());
        createGroupPage.dragHotelXRightToLeft(1);
        countOfUnselectedHotels = countOfUnselectedHotels + 1;
        countOfSelectedHotels = countOfSelectedHotels - 1;
        Assert.assertEquals(countOfUnselectedHotels, createGroupPage.countOfUnselectedHotels());
        Assert.assertEquals(countOfSelectedHotels, createGroupPage.countOfSelectedHotels());
        createGroupPage.clickHotelsDoubleRightArrow();
        countOfUnselectedHotels = 0;
        Assert.assertEquals(countOfUnselectedHotels, createGroupPage.countOfUnselectedHotels());


        // Double left arrow
        // Check zero count on right side

        // Do something similar for hoteliers. Maybe before hotels?

        // Check more here with arrow buttons, right to left dragging too for both hotels and hoteliers...




        Thread.sleep(5000);

        // Edit a group

        // hurdles
        // group staff
        // group hurdles

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        // Else not handled in this test
    }
}