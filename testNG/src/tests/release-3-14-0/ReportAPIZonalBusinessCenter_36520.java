import io.restassured.response.Response;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 *
 * All the tests relating to Story 36520 - Report API for Zonal Business center (ZBS)
 *
 * FR1:  For a Zonal to access the API a hotel setting will need to be enabled on the PMS,
 *       below is the hotel setting (please feel free to change). This will be a new hotel setting.
 *       The hotel setting text is below: export.reports.api
 *
 * NFR1: The endpoint should provision a throttling mechanism ensuring the endpoint does
 *       not attempt to retrieve data from MongoDB more than once per second.
 *       You may consider writing a basic locking mechanism into Redis, where a key can be
 *       written and set to expire after 1 second has elapsed.
 *
 * NFR2: ??????????????
 *
 */
public class ReportAPIZonalBusinessCenter_36520 extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    CurrentData currentData;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    SettingsPage settingsPage;
    OptionsPage optionsPage;
    GroupRatePlansPage groupRatePlansPage;

    HoteliersCreatePage hoteliersCreatePage;

    ExportDailySales exportDailySales;

    // Variables used throughout test
    String testDataHotelPath = "";
    String testDataLoginPath = "";
    String hotelName = "";
    String hotelSlug = "";

    String date;

    /**
     *
     * This test covers:
     * FR1:  For a Zonal to access the API a hotel setting will need to be enabled on the PMS,
     *       below is the hotel setting (please feel free to change). This will be a new hotel setting.
     *       The hotel setting text is below: export.reports.api
     *
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to set and view the new hotel setting of export.reports.api
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Staff Admin user, that is able to log in
     *
     */
    @Test(groups={"verifyThrottleLimit36520TC1"})
    public void verifyThrottleLimit36520TC1() throws InterruptedException {
        // Set test case name
        setTestCaseName("verifyThrottleLimit36520TC1");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to set and view the new hotel setting of export.reports.api\n" +
                        " * Covers:\n" +
                        " * FR1:  For a Zonal to access the API a hotel setting will need to be enabled on the PMS,\n" +
                        " *       below is the hotel setting (please feel free to change). This will be a new hotel setting.\n" +
                        " *       The hotel setting text is below: export.reports.api\n" +
                        " *****");

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());
        data = new Data();

        // Start the test
        // Log in
        login.loginRouting();

        // Set the data for the hotel
//        setDataHotel();
        hotelSlug = "glasgow";
        hotelName = "Glasgow Goldmine";

        startOfTestPageRouting();

        // Should be on dashboard here
        settingsPage = dashboardPage.navigateToHotelSettingsPage();
        settingsPage.validatePage();

        optionsPage = settingsPage.clickOptions();

        if(optionsPage.optionAndValueExists("export.reports.api", "1")==false) {
            optionsPage.typeOptionKey("export.reports.api");
            optionsPage.typeOptionValue("1");
            optionsPage = optionsPage.clickCreateOption();
            optionsPage.validatePage();
        }
        else {
            report.appendExport("INFO: The option of: export.reports.api has a value set to 1 already.");
        }

        setPassedReportLocation();
    }

    /**
     *
     * This test covers:
     * NFR1: The endpoint should provision a throttling mechanism ensuring the endpoint does
     *       not attempt to retrieve data from MongoDB more than once per second.
     *       You may consider writing a basic locking mechanism into Redis, where a key can be
     *       written and set to expire after 1 second has elapsed.
     *
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to be throttled when more than 1 request per second
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Terminal user, that is able to log in
     *
     */
    @Test(groups={"verifyThrottleLimit36520TC2"})
    public void verifyThrottleLimit36520TC2() throws InterruptedException, ParseException {
        // Set test case name
        setTestCaseName("verifyThrottleLimit36520TC2");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC1: Verify that a user with the right permission is able to be throttled when more than 1 request per second\n" +
                        " * Covers:\n" +
                        " * NFR1: The endpoint should provision a throttling mechanism ensuring the endpoint does\n" +
                        "         not attempt to retrieve data from MongoDB more than once per second.\n" +
                        "         You may consider writing a basic locking mechanism into Redis, where a key can be\n" +
                        "         written and set to expire after 1 second has elapsed.\n" +
                        " *****");

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());
        data = new Data();

        // Set the data for the hotel
//        setDataHotel();
        hotelSlug = "glasgow";
        hotelName = "Glasgow Goldmine";
        login.loginEmail = "<EMAIL>";
        login.loginPassword = "Password1234?";

//        login.loginEmail = "<EMAIL>";
//        login.loginPassword = "shady";

//        setCurrentDate();
        date = "2021-04-21";

        // Setup export daily sales request
        exportDailySales = new ExportDailySales(getReport(), getEnvironment(), hotelSlug);
        exportDailySales.setEmail(login.loginEmail);
        exportDailySales.setPassword(login.loginPassword);
        exportDailySales.setDate(date);

//        exportDailySales.postExportDailySalesResponseParsed();

        ExecutorService executor = Executors.newFixedThreadPool(6);
        List<Future<Response>> listFutureResponse = new ArrayList<Future<Response>>();
        Callable<Response> callable = new ExportDailySalesCall(getReport(), exportDailySales);
        List<Response> responses = new ArrayList<Response>();
        for(int i=0; i<6; i++) {
//            Thread.sleep(1500);
            Future<Response> future = executor.submit(callable);
            listFutureResponse.add(future);
        }

        for(Future<Response> fut : listFutureResponse) {
            try {
                responses.add(fut.get());
                report.appendExport("INFO THREAD: " + fut.get().statusCode() + " " + fut.get().getTime() + " | Headers: " + fut.get().getHeader("date"));
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }

        boolean foundMatchingTime = false;
        loop429: for(int i=0; i<responses.size(); i++) {
            report.appendExport("INFO: Checking status code: " + i + " | " + responses.get(i).getStatusCode());
            // If a response equals 429
            if(responses.get(i).getStatusCode()==429) {
                // Check to make sure any other response has the same date timestamp
                for(int j=0; j<responses.size(); j++) {
                    if(j!=i) {
                        if(responses.get(j).getHeader("date").equals(responses.get(i).getHeader("date"))) {
                            foundMatchingTime = true;
                        }
                    }
                }
            }
        }

//        boolean foundMatchingTime = false;
//        loop429: for(int i=0; i<responses.size(); i++) {
//            report.appendExport("INFO: Checking status code: " + i + " | " + responses.get(i).getStatusCode());
//            // If a response equals 429
//            if(responses.get(i).getStatusCode()==429) {
//                Date date0 = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss zzz").parse(responses.get(i).getHeader("date"));
//                String date1 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX").format(date0);
//                OffsetDateTime original = OffsetDateTime.parse(date1);
//                OffsetDateTime a = OffsetDateTime.parse(date1).plus(Duration.ofSeconds(1L));
//                OffsetDateTime b = OffsetDateTime.parse(date1).minus(Duration.ofSeconds(1L));
//                String original1 = original.toString();
//                String a1 = a.toString();
//                String b1 = b.toString();
//                // Check to make sure any other response has the same date timestamp
//                for(int j=0; j<responses.size(); j++) {
//                    if(j!=i) {
//                        Date date2 = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss zzz").parse(responses.get(j).getHeader("date"));
//                        String date3 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX").format(date2);
//                        OffsetDateTime attempting = OffsetDateTime.parse(date3);
//                        String attempting1 = attempting.toString();
//                        report.appendExport("INFO: All the timings: " + original1 + " | " + a1 + " | " + b1 + " | " + attempting1);
//                        if(attempting1.equals(original1)) {
//                            report.appendExport("INFO: Found matching date time for a request. Exact");
//                            foundMatchingTime = true;
//                            break loop429;
//                        }
//                        if(attempting1.equals(a1)) {
//                            report.appendExport("INFO: Found matching date time for a request. After");
//                            foundMatchingTime = true;
//                            break loop429;
//                        }
//                        if(attempting1.equals(b1)) {
//                            report.appendExport("INFO: Found matching date time for a request. Before");
//                            foundMatchingTime = true;
//                            break loop429;
//                        }
//                    }
//                }
//            }
//        }
        Assert.assertTrue(foundMatchingTime);

        setPassedReportLocation();
    }

    // TC3 check response codes and json content
    @Test(groups={"verifyThrottleLimit36520TC3"})
    public void verifyThrottleLimit36520TC3() throws InterruptedException {
        // Set test case name
        setTestCaseName("verifyThrottleLimit36520TC3");

        // Set paths needed
        // Set the path to login data for this test
        // Set the path to export locations for this test
        // Set the path to role file that contains the role needed for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:\n" +
                        " * TC3: \n" +
                        " * Covers:\n" +
                        " *****");

        // Setup classes needed
        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        currentData = new CurrentData(getReport(), getIteration());
        data = new Data();

        // Set the data for the hotel
//        setDataHotel();
        hotelSlug = "glasgow";
        hotelName = "Glasgow Goldmine";
        login.loginEmail = "<EMAIL>";
        login.loginPassword = "Password1234?";

//        login.loginEmail = "<EMAIL>";
//        login.loginPassword = "shady";

//        setCurrentDate();
        date = "2021-04-21";

        // Setup export daily sales request
        exportDailySales = new ExportDailySales(getReport(), getEnvironment(), hotelSlug);
        exportDailySales.setEmail(login.loginEmail);
        exportDailySales.setPassword(login.loginPassword);
        exportDailySales.setDate(date);

        JSONObject dailySalesResponseParsed = exportDailySales.postExportDailySalesResponseParsed();
        Assert.assertEquals(dailySalesResponseParsed.get("slug"), hotelSlug);
        Assert.assertEquals(dailySalesResponseParsed.get("name"), hotelName);
        Assert.assertEquals(dailySalesResponseParsed.get("date"), date);
        // id, uuid, group

        JSONObject dailySalesResponseParsedObjectSales = (JSONObject) dailySalesResponseParsed.get("sales");
        JSONObject dailySalesResponseParsedObjectPayments = (JSONObject) dailySalesResponseParsed.get("payments");
        JSONObject dailySalesResponseParsedObjectStats = (JSONObject) dailySalesResponseParsed.get("stats");

        report.appendExport("INFO: SALES: " + dailySalesResponseParsedObjectSales.toString());
        report.appendExport("INFO: PAYMENTS: " + dailySalesResponseParsedObjectPayments.toString());
        report.appendExport("INFO: STATS: " + dailySalesResponseParsedObjectStats.toString());

//        JSONArray dailySalesResponseParsedArraySales = (JSONArray) dailySalesResponseParsed.get("sales");
//        for(int i=0; i<dailySalesResponseParsedArraySales.size(); i++) {
//            JSONObject breakdown = (JSONObject) dailySalesResponseParsedArraySales.get(i);
//        }

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin") || login.getLoginUser().equals("staff-support") || login.getLoginUser().equals("staff-manager") || login.getLoginUser().equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
            hotelsPage.typeHotelNameInSearch(hotelName);
            hotelsPage = hotelsPage.clickSearch();
            hotelsPage.validatePage();
            dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        }
        else if (login.getLoginUser().equals("group-admin")){
            groupRatePlansPage = login.groupRatePlansPage;
            groupRatePlansPage.validatePage();
            groupRatePlansPage.clickGroupViewDropDown();
            dashboardPage = groupRatePlansPage.clickHotelGoToProperty(hotelSlug, hotelName);
        }
        else if (login.getLoginUser().equals("group-manager") || login.getLoginUser().equals("hotelier-manager")) {
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(login.hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
                dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
            }
        }
        dashboardPage.validatePage();
        // to settings page?
    }

    // Another page routing for users who don't have access to the hotel settings page

    public void setDataHotel() {
        testDataHotelPath = login.currentData.getHotel(testDataHotelPath);
        String[] hotelValues = new String[]{"slug", "name"};
        JSONArray hotelDetails = data.returnJSONArray(testDataHotelPath, hotelValues);
        hotelSlug = hotelDetails.get(0).toString();
        hotelName = hotelDetails.get(1).toString();
    }

    public void setCurrentDate() {

    }
}