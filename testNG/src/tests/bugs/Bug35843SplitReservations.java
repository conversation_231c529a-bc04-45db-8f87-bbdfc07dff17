import org.testng.Assert;
import org.testng.annotations.Test;

    /**
     *
     * Bug 35843 - Split Reservations - Is not creating an invoice for the new Booking
     *
     */
public class Bug35843SplitReservations extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    AvailabilityPage availabilityPage;
    AvailabilityGuestPage availabilityGuestPage;

    String testDataLoginPath = "";

        /**
     *
     * Bug 35843 - Split Reservations - Is not creating an invoice for the new Booking
     *
     */
    @Test(groups={"bug35843SplitReservationsTC1"})
    public void bug35843SplitReservationsTC1() {
        // Set test case name
        setTestCaseName("bug35843SplitReservationsTC1");

        // Set paths needed
        // Set the path to login data for this test
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                        " * Start of test:" +
                        " * Bug 35843 - Split Reservations - Is not creating an invoice for the new Booking" +
                        " *****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting();

        // Navigate to a hotels Dashboard page page
        hotelsPage.typeHotelNameInSearch("Cardiff Plaza");

        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel("cardiff", "Cardiff Plaza");

        dashboardPage.validatePage();
        availabilityPage = dashboardPage.horizontalOptionsPage.navigateToAvailabilityPage();
        availabilityPage.validatePage();

        availabilityPage.clickShowRatesForRoomType("sng");
        availabilityPage = availabilityPage.clickAddToBookingForRate("sng", "Base rate");
        availabilityPage.validatePage();

        availabilityPage.clickShowRatesForRoomType("fam");
        availabilityPage = availabilityPage.clickAddToBookingForRate("fam", "Base rate");
        availabilityPage.validatePage();

        availabilityGuestPage = availabilityPage.clickGuestDetailsProgressButton();
        availabilityGuestPage.validatePage();

        availabilityGuestPage.enterFirstName("bam bam");
        availabilityGuestPage.enterLastName("warner");
        availabilityGuestPage.enterEmail("<EMAIL>");
        availabilityGuestPage.enterConfirmationEmail("<EMAIL>");
        availabilityGuestPage.enterPhone("0123456789");
        BookingReferencePage bookingReferencePage1 = availabilityGuestPage.clickConfirmBookingAsUserLoggedIn();

        bookingReferencePage1.clickSplitReservations();
        bookingReferencePage1.splitReservationsSelectXReservation(1);
        bookingReferencePage1.clickSplitReservationsSelectReservationsNext();
        bookingReferencePage1.clickSplitReservationsCreditCardNext();
        bookingReferencePage1.clickSplitReservationsConfirmationConfirm();
        BookingReferencePage bookingReferencePage2 = bookingReferencePage1.clickSplitReservationsViewTheNewBooking();
        getReport().appendExport("Booking root value new: " + bookingReferencePage2.getGuestBookingRootValue());
        Assert.assertEquals(bookingReferencePage2.getGuestBookingRootValue(), "1 Reservation");

        bookingReferencePage1.genericMethods.switchToPreviousWindow(driver);
        bookingReferencePage1 = bookingReferencePage1.clickSplitReservationsViewTheOldBooking();
        bookingReferencePage1.validatePage();

        getReport().appendExport("Booking root value old: " + bookingReferencePage1.getGuestBookingRootValue());
        Assert.assertEquals(bookingReferencePage1.getGuestBookingRootValue(), "1 Reservation");

        setPassedReportLocation();
    }

    public void startOfTestPageRouting() {
        if (login.getLoginUser().equals("staff-admin")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        // Else not handled in this test
    }
}