import org.testng.annotations.Test;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

public class CreateLoadTestingHotel extends BaseTest {

    // required classes
    GenericMethods genericMethods;
    Data data;
    StaffLoginPage staffLoginPage;
    HotelsPage hotelsPage;
    CreateHotelPage createHotelPage;
    RoomTypesCreatePage roomTypesCreatePage;
    RoomsCreatePage roomsCreatePage;

    String[] roomTypeLetters = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O"};

    @Test(invocationCount = 35)
    public void setupHotel() throws InterruptedException {
        genericMethods = new GenericMethods(report);
        data = new Data();
        staffLoginPage = new StaffLoginPage(getDriver(), report, getEnvironment());
        hotelsPage = new HotelsPage(getDriver(), report, getEnvironment(), new JSONObject());
        createHotelPage = new CreateHotelPage(getDriver(), report, getEnvironment());

        executeTest(); 
        Thread.sleep(3000);
    }

    // Navigate to the appropriate URL
    // Log in using valid credentials
    // Wait until the user is on the hotels page
    // Verify it is actually the hotels page
    // Navigate to the Create Hotel page
    public void executeTest() {

        driver.get(staffLoginPage.pageUrl); 

        // LOGIN
        String[] loginValues = new String[]{"email", "password"};
        JSONArray loginDetails = data.returnJSONArray("/src/testdata/staffAdminLoginAlways.json", loginValues);
        staffLoginPage.loginAs(loginDetails.get(0).toString(), loginDetails.get(1).toString());

        // GOTO HOTELS PAGE
        genericMethods.waitForExpectedURL(driver, hotelsPage.pageUrl);
        genericMethods.assertMatchingURL(driver, hotelsPage.pageUrl);

        // CREATE HOTEL - TODO: Add a method in the createHotelPage that can do this (easier to maintain)
        createHotelPage = hotelsPage.clickCreateHotelLink();
        genericMethods.waitForExpectedURL(driver, createHotelPage.pageUrl);
        genericMethods.assertMatchingURL(driver, createHotelPage.pageUrl);
        createHotelPage.selectStatus("Live");
        String slug = data.getRandomString(10, "lowercase");
        createHotelPage.typeSlug(slug);
        String hotelName = data.getRandomName("hotelName") + "_" + data.getRandomString(5, "numeric");
        createHotelPage.typeName(hotelName);
        JSONObject randomAddress = data.getRandomAddress();
        createHotelPage.typeAddress(randomAddress.get("line1").toString());
        createHotelPage.typePostcode(randomAddress.get("postcode").toString());
        createHotelPage.typeCountry(randomAddress.get("country").toString());
        createHotelPage.typePhone(data.getRandomString(10, "numeric"));
        createHotelPage.typeEmail(data.getRandomString(10, "lowercase") + "@example.com");
        createHotelPage.typeDescription(data.getRandomString(50, "any"));
        createHotelPage.typePrivacyLink("https://www.high-level-software.com");
        createHotelPage.typeTermsLink(data.getRandomString(50, "any"));
        createHotelPage.typeCancellationLink(data.getRandomString(50, "any"));
        createHotelPage.clearCheckinFrom();
        createHotelPage.typeCheckinFrom(data.getRandomMinutes("AM"));
        createHotelPage.clearCheckoutBy();
        createHotelPage.typeCheckoutBy(data.getRandomMinutes("PM"));
        createHotelPage.typeTaxRate("20");
        createHotelPage.typeTaxNumber(data.getRandomString(5, "numeric"));
        createHotelPage.clickSubmit();

        // CREATE SOME ROOM TYPES
        for(int i=0; i < roomTypeLetters.length; i++) {
            driver.get("http://localhost:8000/hotels/" + slug +"/room-types/create");
            roomTypesCreatePage = new RoomTypesCreatePage(getDriver(), report, "http://localhost:8000", slug, hotelName);
            roomTypesCreatePage.typeType("Load test room");
            roomTypesCreatePage.typeCode(roomTypeLetters[i]);
            roomTypesCreatePage.typeTitle("Load test room " + roomTypeLetters[i]);
            roomTypesCreatePage.typeDescription("Load test room");
            roomTypesCreatePage.typeLetter(roomTypeLetters[i]);
            roomTypesCreatePage.typePrice("1");
            roomTypesCreatePage.typePoints("1");
            roomTypesCreatePage.typeMaxNumOccupants("2");
            roomTypesCreatePage.typeMaxNumAdults("2");
            roomTypesCreatePage.typeMaxNumChildren("0");
            roomTypesCreatePage.clickSubmit();
        }
        roomsCreatePage = new RoomsCreatePage(getDriver(), getReport(), getEnvironment(), slug, hotelName);

        // CREATE SOME ROOMS
        for(int i=1; i < roomTypeLetters.length + 1; i++) { // starts at 1 due to selectRoomType never being 0
            driver.get("http://localhost:8000/hotels/" + slug +"/rooms/create");
            roomsCreatePage.typeRoomNo(Integer.toString(i));
            roomsCreatePage.typeDirections("over there" + i);
            roomsCreatePage.selectRoomType("1");
            roomsCreatePage.clickSubmit();
        }

        // driver.get("http://localhost:8000/hotels/" + slug +"/v2/rates/create");
        // driver.manage().timeouts().implicitlyWait(5, TimeUnit.SECONDS);
        // ratesCreatePage.typeName("Load rate");
        // ratesCreatePage.typeRateCode("lr");
        // ratesCreatePage.typeDescription("Load testing rate");
        // ratesCreatePage.selectAddARoomSection();
        // for(int i=1; i < roomTypeLetters.length + 1; i++) {   // starts at 1 due to selectRoomType never being 0
        //     ratesCreatePage.selectRoomType(i + 1);
        //     ratesCreatePage.typeRoomTypePrice(roomTypeLetters[i-1],"100" + i);
        // } 
        // ratesCreatePage.clickSave();
    }
}