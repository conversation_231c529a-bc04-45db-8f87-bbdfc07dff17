import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.json.simple.JSONArray;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * Check Reports - comparing old to new reports
 *
 */
public class CheckReports extends BaseTest {

    // Required classes
    GenericMethods genericMethods;
    Login login;
    Export exportCreatedHotel;
    Export exportCreatedHotelOptions;
    Export exportCreatedHotelCategories;
    Assertion assertion;
    Data data;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    HotelierLoginPage hotelierLoginPage;
//    TestPage testPage;

    // Variables used throughout test
    String slug = "";
    String tempBeforeDate = "";
    String startDate = "";
    String endDate = "";

    String testReport;

    @Test(groups={"checkReportsTC1"})
    public void checkReportsTC1() throws InterruptedException {
        System.out.println("*****\nStart of test:");
        System.out.println("*****");

        genericMethods = new GenericMethods(getReport());
        exportCreatedHotel = new Export();
        exportCreatedHotelOptions = new Export();
        exportCreatedHotelCategories = new Export();
        assertion = new Assertion(getReport());
        data = new Data();

        // Get the test data path
        String testDataPath = "/src/testdata/" + getIteration() + "/quick/checkReports/" + getTestData();
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataPath);


        // Parse the login details
        String testDataLoginPath = testDataPath + "Login.json";
        String[] loginValues = new String[]{"email", "password", "user"};
        JSONArray loginDetails = data.returnJSONArray(testDataLoginPath, loginValues);
        String email = "<EMAIL>";
        String password = "Password123?";
        String user = loginDetails.get(2).toString();

        // Log in
        login.loginRouting();
        startOfTestPageRouting(user, testDataPath);

        setDataCheckReports();

        // Set location for Reports
        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/redo/" + getTestData() + "_" + slug + "_" + startDate + "_Report_Export.txt");

        report.appendExport("Test Data: Slug: " + slug + " | Start Date: " + startDate + " | End Date: " + endDate);
        // Navigate to Old Daily Sales Reports page
        driver.get(getEnvironment() + "/hotels/" + slug + "/reports/daily");
        genericMethods.waitForPageLoad(driver);

        // Enter the dates and click search
        driver.findElement(By.cssSelector("#date_min")).clear();
        driver.findElement(By.cssSelector("#date_min")).sendKeys(startDate);
        driver.findElement(By.cssSelector("#date_max")).clear();
        driver.findElement(By.cssSelector("#date_max")).sendKeys(endDate);
        driver.findElement(By.cssSelector("body > div.body > div > div.col1 > div:nth-child(1) > div > form > div > input")).click();

        // Get all the trs in the Old Daily Sales Report
        List<WebElement> oldDailySalesReportAllTrs = driver.findElements(By.xpath("/html/body/div[1]/div/div[1]/div/div/table/tbody/tr[1]/td/table/tbody/tr"));

        // Find out where the exceptions are
        boolean oldDailySalesReportFindExceptions = false;
        int oldDailySalesReportExceptionsTr = 0;
        int oldDailySalesReportTotalTr = 0;
        for(int k = 2; k < oldDailySalesReportAllTrs.size(); k++) {
            if(oldDailySalesReportAllTrs.get(k).findElement(By.xpath("./td[1]")).getText().equals("Exceptions")) {
                oldDailySalesReportExceptionsTr = k;
                oldDailySalesReportFindExceptions = true;
            }
            if(oldDailySalesReportAllTrs.get(k).findElement(By.xpath("./td[1]")).getText().equals("Total")) {
                oldDailySalesReportTotalTr = k;
                break;
            }
        }

        // Define Exceptions array here
        int oldDailySalesReportExceptionRowsSize = oldDailySalesReportTotalTr - oldDailySalesReportExceptionsTr;
        List<List<String>> oldDailySalesReportExceptionRowsValues = new ArrayList<List<String>>(oldDailySalesReportExceptionRowsSize);
        for (int i = 0; i < oldDailySalesReportExceptionRowsSize; i++) {
            oldDailySalesReportExceptionRowsValues.add(new ArrayList<String>());
        }

        // If there are exceptions, store all of the old Daily Sales Report Exception data
        if(oldDailySalesReportFindExceptions == true) {
//            oldDailySalesReportExceptionRowsValues = oldDailySalesReportExceptionsData(oldDailySalesReportExceptionRowsSize, oldDailySalesReportExceptionsTr, oldDailySalesReportAllTrs, oldDailySalesReportExceptionRowsValues);
            for (int j = 0; j < oldDailySalesReportExceptionRowsSize; j++) {
                int location = oldDailySalesReportExceptionsTr + j;
                int tdLength = oldDailySalesReportAllTrs.get(location).findElements(By.xpath("./td")).size();

                int exceptionRowsTdLength4 = tdLength - 4;
                int exceptionRowsTdLength3 = tdLength - 3;
                int exceptionRowsTdLength2 = tdLength - 2;
                int exceptionRowsTdLength1 = tdLength - 1;
                if (j == 0) { // exceptions row, use all tds
                    int exceptionRowsTdLength5 = tdLength - 5;
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + exceptionRowsTdLength5 + "]")).getText());
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + exceptionRowsTdLength4 + "]")).getText());
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + exceptionRowsTdLength3 + "]")).getText());
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + exceptionRowsTdLength2 + "]")).getText());
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + exceptionRowsTdLength1 + "]")).getText());
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + tdLength + "]")).getText());
                    System.out.println("EXCEPTIONS: " + oldDailySalesReportExceptionRowsValues.get(j).get(0) + " | " + oldDailySalesReportExceptionRowsValues.get(j).get(1) + " | " + oldDailySalesReportExceptionRowsValues.get(j).get(2) + " | " + oldDailySalesReportExceptionRowsValues.get(j).get(3) + " | " + oldDailySalesReportExceptionRowsValues.get(j).get(4) + " | " + oldDailySalesReportExceptionRowsValues.get(j).get(5));
                } else {
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + exceptionRowsTdLength4 + "]")).getText());
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + exceptionRowsTdLength3 + "]")).getText());
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + exceptionRowsTdLength2 + "]")).getText());
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + exceptionRowsTdLength1 + "]")).getText());
                    oldDailySalesReportExceptionRowsValues.get(j).add(oldDailySalesReportAllTrs.get(location).findElement(By.xpath("./td[" + tdLength + "]")).getText());
                    System.out.println("EXCEPTIONS: " + oldDailySalesReportExceptionRowsValues.get(j).get(0) + " | " + oldDailySalesReportExceptionRowsValues.get(j).get(1) + " | " + oldDailySalesReportExceptionRowsValues.get(j).get(2) + " | " + oldDailySalesReportExceptionRowsValues.get(j).get(3) + " | " + oldDailySalesReportExceptionRowsValues.get(j).get(4));
                }
            }
        }

        // Get all the totals rows in the Old Daily Sales Report
        List<WebElement> oldDailySalesReportTotalRows = driver.findElements(By.xpath("/html/body/div[1]/div/div[1]/div/div/table/tbody/tr[1]/td/table/tbody//tr[@class='total']"));

        // Define Totals array here
        int oldDailySalesReportTotalRowsSize = oldDailySalesReportTotalRows.size();
        List<List<String>> oldDailySalesReportTotalRowsValues = new ArrayList<List<String>>(oldDailySalesReportTotalRowsSize);
        for(int i = 0; i < oldDailySalesReportTotalRowsSize; i++)  {
            oldDailySalesReportTotalRowsValues.add(new ArrayList<String>());
        }

        // Store all of the old Daily Sales Report Total data
        for(int j=0; j<oldDailySalesReportTotalRowsSize; j++) {
            int tdLength = oldDailySalesReportTotalRows.get(j).findElements(By.xpath("./td")).size();

            int totalRowsTdLength4 = tdLength-4;
            int totalRowsTdLength3 = tdLength-3;
            int totalRowsTdLength2 = tdLength-2;
            int totalRowsTdLength1 = tdLength-1;
            if(j!=oldDailySalesReportTotalRowsSize-1) {
                oldDailySalesReportTotalRowsValues.get(j).add(oldDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + totalRowsTdLength4 + "]")).getText());
                oldDailySalesReportTotalRowsValues.get(j).add(oldDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + totalRowsTdLength3 + "]")).getText());
                oldDailySalesReportTotalRowsValues.get(j).add(oldDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + totalRowsTdLength2 + "]")).getText());
                oldDailySalesReportTotalRowsValues.get(j).add(oldDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + totalRowsTdLength1 + "]")).getText());
                oldDailySalesReportTotalRowsValues.get(j).add(oldDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + tdLength + "]")).getText());
                System.out.println("TOTALS: " + oldDailySalesReportTotalRowsValues.get(j).get(0) + " | " + oldDailySalesReportTotalRowsValues.get(j).get(1) + " | " + oldDailySalesReportTotalRowsValues.get(j).get(2) + " | " + oldDailySalesReportTotalRowsValues.get(j).get(3) + " | " +  oldDailySalesReportTotalRowsValues.get(j).get(4));
            }
            else {
                oldDailySalesReportTotalRowsValues.get(j).add(oldDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + totalRowsTdLength3 + "]")).getText());
                oldDailySalesReportTotalRowsValues.get(j).add(oldDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + totalRowsTdLength2 + "]")).getText());
                oldDailySalesReportTotalRowsValues.get(j).add(oldDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + totalRowsTdLength1 + "]")).getText());
                oldDailySalesReportTotalRowsValues.get(j).add(oldDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + tdLength + "]")).getText());
                System.out.println("TOTALS: " + oldDailySalesReportTotalRowsValues.get(j).get(0) + " | " + oldDailySalesReportTotalRowsValues.get(j).get(1) + " | " + oldDailySalesReportTotalRowsValues.get(j).get(2) + " | " + oldDailySalesReportTotalRowsValues.get(j).get(3));
            }
        }

        // Navigate to New Daily Sales Reports page
        driver.get(getEnvironment() + "/hotels/" + slug + "/v2/reports/daily-sales");

        // Click into the From date field
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).click();
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(Keys.chord(Keys.CONTROL,"a", Keys.DELETE));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(0,1));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(1,2));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(2,3));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(3,4));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(4,5));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(5,6));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(6,7));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(7,8));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(8,9));
        Thread.sleep(500);
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[2]/div[2]/div/div/input")).sendKeys(startDate.substring(9,10));
        Thread.sleep(3000);

        // Click into the From date field. Enter the end date
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[3]/div[2]/div/div/input")).click();
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[3]/div[2]/div/div/input")).sendKeys(Keys.chord(Keys.CONTROL,"a", Keys.DELETE));
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div/div/div[3]/div[2]/div/div/input")).sendKeys(endDate);
        Thread.sleep(3000);

        // Click Run Report, to get all of the data for Attachables
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div[1]/button")).click();
        report.appendExport("Actual dates searched: From Date: " + driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div[1]/div/div[2]/div[2]/div/div/input")).getAttribute("value") + " | To Date: " + driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[1]/div/div/div[3]/div/form/div[1]/div/div[3]/div[2]/div/div/input")).getAttribute("value"));
        new WebDriverWait(driver, Duration.ofSeconds(10)).until(ExpectedConditions.invisibilityOfElementLocated(By.cssSelector("#app-single > div > div.loader-wrapper")));

        // Expand Report
        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[2]/div[1]/div/table/thead/tr/th[1]/span")).click();

        // Store New Daily Sales Report Exception Data

        // Store New Daily Sales Report Non-ExceptionData
        List<WebElement> newDailySalesReportTotalRows = driver.findElements(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div[2]/div[1]/div/table/tbody//tr[@role='row']"));

        // Define newReportTotalRowsValues array here
        int newDailySalesReportTotalRowsSize = newDailySalesReportTotalRows.size();
        System.out.println("NEW REPORT SIZE: " + newDailySalesReportTotalRowsSize);
        List<List<String>> newDailySalesReportTotalRowsValues = new ArrayList<List<String>>(newDailySalesReportTotalRowsSize);
        for(int i = 0; i < newDailySalesReportTotalRowsSize; i++)  {
            newDailySalesReportTotalRowsValues.add(new ArrayList<String>());
        }

        // Store all of the new Daily Sales Report Total data
        for(int j=0; j<newDailySalesReportTotalRowsSize; j++) {
            int tdLength = newDailySalesReportTotalRows.get(j).findElements(By.xpath("./td")).size();

            int newReportTotalRowsTdLength5 = tdLength-5;
            int newReportTotalRowsTdLength4 = tdLength-4;
            int newReportTotalRowsTdLength3 = tdLength-3;
            int newReportTotalRowsTdLength2 = tdLength-2;
            int newReportTotalRowsTdLength1 = tdLength-1;

            newDailySalesReportTotalRowsValues.get(j).add(newDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + newReportTotalRowsTdLength5 + "]")).getText());
            newDailySalesReportTotalRowsValues.get(j).add(newDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + newReportTotalRowsTdLength4 + "]")).getText());
            newDailySalesReportTotalRowsValues.get(j).add(newDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + newReportTotalRowsTdLength3 + "]")).getText());
            newDailySalesReportTotalRowsValues.get(j).add(newDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + newReportTotalRowsTdLength2 + "]")).getText());
            newDailySalesReportTotalRowsValues.get(j).add(newDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + newReportTotalRowsTdLength1 + "]")).getText());
            newDailySalesReportTotalRowsValues.get(j).add(newDailySalesReportTotalRows.get(j).findElement(By.xpath("./td[" + tdLength + "]")).getText());
            System.out.println("New Report: " + newDailySalesReportTotalRowsValues.get(j).get(0) + " | " + newDailySalesReportTotalRowsValues.get(j).get(1) + " | " + newDailySalesReportTotalRowsValues.get(j).get(2) + " | " + newDailySalesReportTotalRowsValues.get(j).get(3) + " | " +  newDailySalesReportTotalRowsValues.get(j).get(4) + " | " +  newDailySalesReportTotalRowsValues.get(j).get(5));
        }

        // Compare old report 2d array: totalRowsValues
        // to new report 2d array:      newReportTotalRowsValues
        // Note: Alphabetical, other than Category: Uncategorised
        // Set decrement here as the old report list of lists has categories in it, which we don't compare here in Attachables
        boolean exceptionRowNext = false;
        int exceptionRow = 1;
        int decrement = 0;
        int newDecrement = 0; // WHere there is an exception for that type of attachable
        for(int i=0; i<oldDailySalesReportTotalRowsValues.size(); i++) {
            // All but last row. This is because the final row in Daily Sales has different elements - there is no total quantity in the old Daily Sales
            if(i!=oldDailySalesReportTotalRowsValues.size()-1) {
                boolean ignoreRow = false;
                // For each cell
                for (int j = 0; j < oldDailySalesReportTotalRowsValues.get(i).size(); j++) {
                    // Checking the first column
                    if (j == 0) {
                        // If the first column in the old daily sales has the word Exceptions or Category in it, ignore the whole row for these comparisons
                        Boolean venues = false;
                        Boolean excCat = false;
                        if(oldDailySalesReportTotalRowsValues.get(i).get(j).equals("Venues")) {
                            venues = true;
                        }
                        if(!venues) {
                            if (oldDailySalesReportTotalRowsValues.get(i).get(j).equals("Exceptions") || oldDailySalesReportTotalRowsValues.get(i).get(j).substring(0, 10).equals("Category: ")) {
                                excCat = true;
                            }
                        }
                        if (excCat) {
                            decrement = decrement + 1;
                            ignoreRow = true;
                        } else {
                            if(newDailySalesReportTotalRowsValues.get(i+newDecrement).get(j).equals("-")) {
                                exceptionRowNext = true;
                                report.appendExport("Exception row is next");
                                System.out.println("Exception row is next");
                            }
                            // get quantity column, if 0 it means only exception..
                            if(newDailySalesReportTotalRowsValues.get(i+newDecrement).get(j+1+1).equals("0")) {
                                report.appendExport("0 Found");
                                System.out.println("0 Found");
                                if(exceptionRowNext) {
                                    // Exception Attachable
                                    String oldAttachable = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(0);
                                    String newAttachable = newDailySalesReportTotalRowsValues.get(i+newDecrement).get(1);
                                    if(oldAttachable.equals(newAttachable)) {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("PASS: Old value: " + oldAttachable + " | New value: " + newAttachable);
                                        System.out.println("PASS: Old value: " + oldAttachable + " | New value: " + newAttachable);
                                    } else {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("FAIL: Old value: " + oldAttachable + " | New value: " + newAttachable);
                                        System.out.println("FAIL: Old value: " + oldAttachable + " | New value: " + newAttachable);
                                    }
                                    Assert.assertEquals(oldAttachable, newAttachable);

                                    // Exception Quantity
                                    String oldQuantity = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(1);
                                    String newQuantity = newDailySalesReportTotalRowsValues.get(i+newDecrement+1).get(2);
                                    if(oldQuantity.equals(newQuantity)) {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("PASS: Old value: " + oldQuantity + " | New value: " + newQuantity);
                                        System.out.println("PASS: Old value: " + oldQuantity + " | New value: " + newQuantity);
                                    } else {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("FAIL: Old value: " + oldQuantity + " | New value: " + newQuantity);
                                        System.out.println("FAIL: Old value: " + oldQuantity + " | New value: " + newQuantity);
                                    }
                                    Assert.assertEquals(oldQuantity, newQuantity);

                                    // Exception Net
                                    String oldNet = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(2);
                                    String newNet = newDailySalesReportTotalRowsValues.get(i+newDecrement+1).get(3);
                                    if(oldNet.equals(newNet)) {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("PASS: Old value: " + oldNet + " | New value: " + newNet);
                                        System.out.println("PASS: Old value: " + oldNet + " | New value: " + newNet);
                                    } else {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("FAIL: Old value: " + oldNet + " | New value: " + newNet);
                                        System.out.println("FAIL: Old value: " + oldNet + " | New value: " + newNet);
                                    }
                                    Assert.assertEquals(oldNet, newNet);

                                    // Exception Tax
                                    String oldTax = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(3);
                                    String newTax = newDailySalesReportTotalRowsValues.get(i+newDecrement+1).get(4);
                                    if(oldTax.equals(newTax)) {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("PASS: Old value: " + oldTax + " | New value: " + newTax);
                                        System.out.println("PASS: Old value: " + oldTax + " | New value: " + newTax);
                                    } else {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("FAIL: Old value: " + oldTax + " | New value: " + newTax);
                                        System.out.println("FAIL: Old value: " + oldTax + " | New value: " + newTax);
                                    }
                                    Assert.assertEquals(oldTax, newTax);

                                    // Exception Gross
                                    String oldGross = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(4);
                                    String newGross = newDailySalesReportTotalRowsValues.get(i+newDecrement+1).get(5);
                                    if(oldGross.equals(newGross)) {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("PASS: Old value: " + oldGross + " | New value: " + newGross);
                                        System.out.println("PASS: Old value: " + oldGross + " | New value: " + newGross);
                                    } else {
                                        report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                        report.appendExport("FAIL: Old value: " + oldGross + " | New value: " + newGross);
                                        System.out.println("FAIL: Old value: " + oldGross + " | New value: " + newGross);
                                    }
                                    Assert.assertEquals(oldGross, newGross);

                                    newDecrement = newDecrement +2;
                                    exceptionRow = exceptionRow + 1;
                                    exceptionRowNext = false;
                                }
                            }
                            String oldReportValue = "";
                            if(oldDailySalesReportTotalRowsValues.get(i).get(j).equals("Venues")) {
                                oldReportValue = oldDailySalesReportTotalRowsValues.get(i).get(j);
                            }
                            else if (oldDailySalesReportTotalRowsValues.get(i).get(j).substring(0, 12).equals("Attachable: ")) {
                                oldReportValue = oldDailySalesReportTotalRowsValues.get(i).get(j).substring(12, oldDailySalesReportTotalRowsValues.get(i).get(j).length());
                            } else
                                oldReportValue = oldDailySalesReportTotalRowsValues.get(i).get(j);

                            // Do the reporting and assertion for the first column
                            int currRow = i + newDecrement;
                            if (oldReportValue.equals(newDailySalesReportTotalRowsValues.get(currRow).get(j + 1))) {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("PASS: Old value: " + oldReportValue + " | New value: " + newDailySalesReportTotalRowsValues.get(currRow).get(j + 1));
                                System.out.println("PASS: Old value: " + oldReportValue + " | New value: " + newDailySalesReportTotalRowsValues.get(currRow).get(j + 1));
                            } else {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate + "_Report_Export.txt");
                                report.appendExport("FAIL: Old value: " + oldReportValue + " | New value: " + newDailySalesReportTotalRowsValues.get(currRow).get(j + 1));
                                System.out.println("FAIL: Old value: " + oldReportValue + " | New value: " + newDailySalesReportTotalRowsValues.get(currRow).get(j + 1));
                            }
                            Assert.assertEquals(oldReportValue, newDailySalesReportTotalRowsValues.get(currRow).get(j + 1));

                            if(newDailySalesReportTotalRowsValues.get(i+newDecrement).get(j).equals("-")) {
                                exceptionRowNext = true;
                                report.appendExport("Exception row is next");
                                System.out.println("Exception row is next");
                            }
                        }
                    } else {
                        if(!ignoreRow) {
                            // Compare Quantity, Net, Tax, Gross
                            String oldReportValue = oldDailySalesReportTotalRowsValues.get(i).get(j);
                            String newReportValue = newDailySalesReportTotalRowsValues.get(i+newDecrement).get(j+1);
                            // Do the reporting and assertion for all of the other columns
                            if (oldReportValue.equals(newReportValue)) {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("PASS: Old value: " + oldReportValue + " | New value: " + newReportValue);
                                System.out.println("PASS: Old value: " + oldReportValue + " | New value: " + newReportValue);
                            } else {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("FAIL: Old value: " + oldReportValue + " | New value: " + newReportValue);
                                System.out.println("FAIL: Old value: " + oldReportValue + " | New value: " + newReportValue);
                            }
                            Assert.assertEquals(oldReportValue, newReportValue);
                        }
                    }
                    // After checking row, if exceptions is next check it
                    if((j== oldDailySalesReportTotalRowsValues.get(i).size()-1)){
                        if(exceptionRowNext) {
                            // Exception Attachable
                            String oldAttachable = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(0);
                            String newAttachable = newDailySalesReportTotalRowsValues.get(i+newDecrement).get(1);
                            if(oldAttachable.equals(newAttachable)) {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("PASS: Old value: " + oldAttachable + " | New value: " + newAttachable);
                                System.out.println("PASS: Old value: " + oldAttachable + " | New value: " + newAttachable);
                            } else {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("FAIL: Old value: " + oldAttachable + " | New value: " + newAttachable);
                                System.out.println("FAIL: Old value: " + oldAttachable + " | New value: " + newAttachable);
                            }
                            Assert.assertEquals(oldAttachable, newAttachable);

                            // Exception Quantity
                            String oldQuantity = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(1);
                            String newQuantity = newDailySalesReportTotalRowsValues.get(i+newDecrement+1).get(2);
                            if(oldQuantity.equals(newQuantity)) {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("PASS: Old value: " + oldQuantity + " | New value: " + newQuantity);
                                System.out.println("PASS: Old value: " + oldQuantity + " | New value: " + newQuantity);
                            } else {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("FAIL: Old value: " + oldQuantity + " | New value: " + newQuantity);
                                System.out.println("FAIL: Old value: " + oldQuantity + " | New value: " + newQuantity);
                            }
                            Assert.assertEquals(oldQuantity, newQuantity);

                            // Exception Net
                            String oldNet = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(2);
                            String newNet = newDailySalesReportTotalRowsValues.get(i+newDecrement+1).get(3);
                            if(oldNet.equals(newNet)) {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("PASS: Old value: " + oldNet + " | New value: " + newNet);
                                System.out.println("PASS: Old value: " + oldNet + " | New value: " + newNet);
                            } else {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("FAIL: Old value: " + oldNet + " | New value: " + newNet);
                                System.out.println("FAIL: Old value: " + oldNet + " | New value: " + newNet);
                            }
                            Assert.assertEquals(oldNet, newNet);

                            // Exception Tax
                            String oldTax = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(3);
                            String newTax = newDailySalesReportTotalRowsValues.get(i+newDecrement+1).get(4);
                            if(oldTax.equals(newTax)) {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("PASS: Old value: " + oldTax + " | New value: " + newTax);
                                System.out.println("PASS: Old value: " + oldTax + " | New value: " + newTax);
                            } else {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("FAIL: Old value: " + oldTax + " | New value: " + newTax);
                                System.out.println("FAIL: Old value: " + oldTax + " | New value: " + newTax);
                            }
                            Assert.assertEquals(oldTax, newTax);

                            // Exception Gross
                            String oldGross = oldDailySalesReportExceptionRowsValues.get(exceptionRow).get(4);
                            String newGross = newDailySalesReportTotalRowsValues.get(i+newDecrement+1).get(5);
                            if(oldGross.equals(newGross)) {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("PASS: Old value: " + oldGross + " | New value: " + newGross);
                                System.out.println("PASS: Old value: " + oldGross + " | New value: " + newGross);
                            } else {
                                report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                                report.appendExport("FAIL: Old value: " + oldGross + " | New value: " + newGross);
                                System.out.println("FAIL: Old value: " + oldGross + " | New value: " + newGross);
                            }
                            Assert.assertEquals(oldGross, newGross);

                            newDecrement = newDecrement +1;
                            exceptionRow = exceptionRow + 1;
                            exceptionRowNext = false;
                        }
                    }
                }
            } else { // Final Row
                // Minus exceptions, minus categories (decrement value) + 2 to avoid Adjustments Total and Sales Total
                int newReportRow = i + newDecrement - decrement + 2;
                int oldSize = oldDailySalesReportTotalRowsValues.get(i).size();
                int newSize = newDailySalesReportTotalRowsValues.get(newReportRow).size();

                // Net
                String oldNet = oldDailySalesReportTotalRowsValues.get(i).get(oldSize-3);
                String newNet = newDailySalesReportTotalRowsValues.get(newReportRow).get(newSize-3);
                if(oldNet.equals(newNet)) {
                    report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                    report.appendExport("PASS: Sales Total - Net. Old value: " + oldNet + " | New value: " + newNet);
                    System.out.println("PASS: Sales Total - Net. Old value: " + oldNet + " | New value: " + newNet);
                } else {
                    report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                    report.appendExport("FAIL: Sales Total - Net. Old value: " + oldNet + " | New value: " + newNet);
                    System.out.println("FAIL: Sales Total - Net. Old value: " + oldNet + " | New value: " + newNet);
                }
                Assert.assertEquals(oldNet, newNet);

                // Tax
                String oldTax = oldDailySalesReportTotalRowsValues.get(i).get(oldSize-2);
                String newTax = newDailySalesReportTotalRowsValues.get(newReportRow).get(newSize-2);
                if(oldTax.equals(newTax)) {
                    report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                    report.appendExport("PASS: Sales Total - Tax. Old value: " + oldTax + " | New value: " + newTax);
                    System.out.println("PASS: Sales Total - Tax. Old value: " + oldTax + " | New value: " + newTax);
                } else {
                    report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                    report.appendExport("FAIL: Sales Total - Tax. Old value: " + oldTax + " | New value: " + newTax);
                    System.out.println("FAIL: Sales Total - Tax. Old value: " + oldTax + " | New value: " + newTax);
                }
                Assert.assertEquals(oldTax, newTax);

                // Gross
                String oldGross = oldDailySalesReportTotalRowsValues.get(i).get(oldSize-1);
                String newGross = newDailySalesReportTotalRowsValues.get(newReportRow).get(newSize-1);
                if(oldGross.equals(newGross)) {
                    report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/pass/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                    report.appendExport("PASS: Sales Total - Gross. Old value: " + oldGross + " | New value: " + newGross);
                    System.out.println("PASS: Sales Total - Gross. Old value: " + oldGross + " | New value: " + newGross);
                } else {
                    report.setLocation("src/testdata/" + getIteration() + "/quick/checkReports/fail/" + getTestData() + "_" + slug + "_" + startDate +  "_Report_Export.txt");
                    report.appendExport("FAIL: Sales Total - Gross. Old value: " + oldGross + " | New value: " + newGross);
                    System.out.println("FAIL: Sales Total - Gross. Old value: " + oldGross + " | New value: " + newGross);
                }
                Assert.assertEquals(oldGross, newGross);
            }
        }

        // Audit off?

        System.out.println("*****\nEnd of test.\n*****\n");
        Thread.sleep(5000);
    }

    public void startOfTestPageRouting(final String user, String testDataLoginPath) {
        if (user.equals("staff-admin") || user.equals("staff-support") || user.equals("staff-manager") || user.equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        else if (user.equals("group-admin")){
            // groupRatesOverviewPage = login.groupRatesOverviewPage;
            // groupRatesOverviewPage.validatePage(environment);
            // driver.get(environment + "/hotels");
        }
        else if (user.equals("group-manager") || user.equals("hotelier-manager") || user.equals("hotelier-front-desk")) {
            JSONArray hotelDetails = (JSONArray) data.returnJSONArray(testDataLoginPath, new String[]{"hotels"}).get(0);
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
                driver.get(getEnvironment() + "/hotels");
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (user.equals("hotelier-terminal")){
            JSONArray hotelDetails = (JSONArray) data.returnJSONArray(testDataLoginPath, new String[]{"hotels"}).get(0);
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
                driver.get(getEnvironment() + "/hotels");
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (user.equals("guest") || user.equals("corporation")) {
            // availabilityPage = login.availabilityPage;
            // availabilityPage.validatePage(environment);
            // driver.get(environment + "/hotels");
        }
    }

    /*
     * Set the data to use to be random data, data from a file or data in a chain for a chain of tests
     *
     */
    public void setDataCheckReports() {
        final String[] reportsValues = new String[]{"slug", "startDate", "endDate", "tempBeforeDate"};
        System.out.println("INFO: Using data in file: " + getDataToUse());
        final JSONArray reportsDetails = data.returnJSONArray("/src/testdata/" + getIteration() + "/quick/checkReports/" + getDataToUse() + ".json", reportsValues);
        slug = reportsDetails.get(0).toString();
        startDate = reportsDetails.get(1).toString();
        endDate = reportsDetails.get(2).toString();
        tempBeforeDate = reportsDetails.get(3).toString();
    }

    public void selectDate(String date, List<WebElement> calWeeksDiv) {
        String year = date.substring(0,4);
        String month = date.substring(5,7);
        String day = date.substring(8,10);

        if(Integer.parseInt(day) < 10) {
            day = day.substring(1,2);
        }
        String monthText = "";
        if(Integer.parseInt(month) == 1) {
            monthText = "January";
        } else if(Integer.parseInt(month) == 2) {
            monthText = "February";
        } else if(Integer.parseInt(month) == 3) {
            monthText = "March";
        } else if(Integer.parseInt(month) == 4) {
            monthText = "April";
        } else if(Integer.parseInt(month) == 5) {
            monthText = "May";
        } else if(Integer.parseInt(month) == 6) {
            monthText = "June";
        } else if(Integer.parseInt(month) == 7) {
            monthText = "July";
        } else if(Integer.parseInt(month) == 8) {
            monthText = "August";
        } else if(Integer.parseInt(month) == 10) {
            monthText = "September";
        } else if(Integer.parseInt(month) == 10) {
            monthText = "October";
        } else if(Integer.parseInt(month) == 11) {
            monthText = "November";
        } else if(Integer.parseInt(month) == 12) {
            monthText = "December";
        }

        // get current datetime in real life
        // GET CURRENT YEAR
        // CURRENT YEAR - YEAR WANTED, navigate back and forth 12 months

        // GET CURRENT MONTH
        // navigate back and forth 1 month

        boolean foundDate = false;
        for(int i=0; i<calWeeksDiv.size(); i++) {
            List<WebElement> calWeekXDivs = calWeeksDiv.get(i).findElements(By.xpath("./div"));
            System.out.println("calWeekXDivs: " + calWeekXDivs.size());
            for(int j=0; j<calWeekXDivs.size(); j++) {
                WebElement e = calWeekXDivs.get(j);
                String fullDate = e.getAttribute("aria-label");
                String[] parts = fullDate.split(" ");
                String monthFound = parts[2];
                String dayFound = parts[3];
                if(monthFound.equals(monthText)) {
                    if(dayFound.substring(0,dayFound.length()-3).equals(day)) {
                        e.click();
                        foundDate=true;
                    }
                }
                if(foundDate)
                    break;
            }
            if(foundDate)
                break;
        }
    }
}