import org.json.simple.JSONObject;
import org.testng.annotations.Test;
import org.json.simple.JSONArray;

public class QuickCreateHotelier   extends BaseTest {
    // required classes
    GenericMethods genericMethods;
    Data data;
    StaffLoginPage staffLoginPage;
    HotelsPage hotelsPage;
    HoteliersCreatePage hoteliersCreatePage;

    @Test
    public void QuickCreateHotelierTC1() throws InterruptedException  {
        genericMethods = new GenericMethods(report);
        data = new Data();
        staffLoginPage = new StaffLoginPage(getDriver(), report, getEnvironment());
        hotelsPage = new HotelsPage(getDriver(), report, getEnvironment(), new JSONObject());
        hoteliersCreatePage = new HoteliersCreatePage(getDriver(), report, getEnvironment(), "cardiff", "Cardiff Plaza");
        executeTest();
        Thread.sleep(3000);
    }

    // Navigate to the appropriate URL
    // Log in using valid credentials
    // Wait until the user is on the hotels page
    // Verify it is actually the hotels page
    // Navigate to the Create Hotel page
    public void executeTest() {
        driver.get(staffLoginPage.pageUrl); 
        final String[] loginValues = new String[]{"email", "password"};
        final JSONArray loginDetails = data.returnJSONArray("/src/testdata/staffAdminLoginAlways.json", loginValues);
        staffLoginPage.loginAs(loginDetails.get(0).toString(), loginDetails.get(1).toString());
        genericMethods.waitForExpectedURL(driver, hotelsPage.pageUrl);
        genericMethods.assertMatchingURL(driver, hotelsPage.pageUrl);
        
        final String[] slugs = {
       "cardiff",
"london",
"glasgow",
"knujmtgrkc",
"ebdtnzgfpo",
"ugbmgdgfgn",
"lodmqeahox",
"suvtvyqnfv",
"bklgqbizdl",
"jfxgvgdxeg",
"zyskjdpivp",
"brlzjqfxnm",
"hxbfxpzfjt",
"roieunfgxa",
"etsueunaht",
"togzzpvigp",
"geheofpmmb",
"vtqjetfgbr",
"azkeekatad",
"ekuqyocmqk",
"urevhjttjb",
"hjhgiezgar",
"rmdhvjupno",
"hppbvzfeva",
"xsdzrfycuv",
"jlivttbaxi",
"zbtmhcqnia",
"bblptftmjv",
"oapgfejbhu",
"bmcuqdokth",
"pvdgsmnkgc",
"seixjzpqzd",
"kausfkzhpd",
"cnuaozpsbm",
"emtzkbzxbs",
"yubjlpmfyg",
"yklcnsdoqj",
"erluvnprip",
"ymhiyopibm",
"uysxfjqsbt",
"oospcuojnk",
"fsvvabshoc",
"ialvbkfuar",
"ojgnrsvcvn",
"ptqxjpgrfr",
"xohnkmelmr",
"mhdipgrpiz",
"ugfmsxbhiy",
"yyopjachzl",
"jsbjnedyya",
"njqruzmaxo",
"vvhmqosgtu",
"qjboihsfec",
"gcxetnudmf",
"bhptevifpz",
"nbgjrzmgvt",
"jfjnyrptph",
"teyiirstyp",
"nehjifapqd",
"imtbivlcmh",
"pihinhkboh",
"kqjhgjpcle",
"ndqsipikzj",
"mffvvdmczy"
        
        };
        for(int i=0; i<slugs.length;i++) {
            driver.get("http://localhost:8000/hotels/" + slugs[i] +"/hoteliers/create");
            hoteliersCreatePage.typeHotelierName("Manager Test");
            hoteliersCreatePage.typeHotelierRole("Manager");
            hoteliersCreatePage.typeHotelierEmail("<EMAIL>");
            hoteliersCreatePage.typeHotelierConfirmationEmail("<EMAIL>");
            hoteliersCreatePage.clickSubmitButton();
        }
       
    }
}