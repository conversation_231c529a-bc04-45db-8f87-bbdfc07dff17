import org.testng.annotations.Test;
import org.json.simple.JSONArray;
import org.openqa.selenium.By;

public class CreateLoadTestingHotelTwo extends BaseTest {

    // required classes
    GenericMethods genericMethods;
    Data data;
    StaffLoginPage staffLoginPage;
    HotelsPage hotelsPage;
    RoomTypesCreatePage roomTypesCreatePage;
    RoomsCreatePage roomsCreatePage;
    RatesCreatePage ratesCreatePage;

    String[] roomTypeLetters = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O"};
    @Test()
    public void setupHotel() throws InterruptedException {
        genericMethods = new GenericMethods(report);
        data = new Data();
        roomTypesCreatePage = new RoomTypesCreatePage(getDriver(), report, "http://localhost:8000", "fxmakxmifb", "hotel name" );
        roomsCreatePage = new RoomsCreatePage(getDriver(), report, "http://localhost:8000", "fxmakxmifb", "hotel name" );

        executeTest();
        Thread.sleep(3000);
    }

    // Navigate to the appropriate URL
    // Log in using valid credentials
    // Wait until the user is on the hotels page
    // Verify it is actually the hotels page
    // Navigate to the Create Hotel page
    public void executeTest() {
        /*
         * Staff Login page > Hotels page
         */
        driver.get("http://localhost:8000/staff/login");
        staffLoginPage = new StaffLoginPage(driver, report, getEnvironment());
        String[] loginValues = new String[]{"email", "password"};
        JSONArray loginDetails = data.returnJSONArray("/src/testdata/templogin.json", loginValues);
        hotelsPage = staffLoginPage.loginAs(loginDetails.get(0).toString(), loginDetails.get(1).toString());

        /*
         * Hotels page > Create Hotel page
         */
 /*
        createHotelPage = hotelsPage.clickCreateHotelLink();
        // CREATE HOTEL -
        createHotelPage.selectStatus("Live");
        String slug = genericMethods.getRandomString(10, "lowercase");
        createHotelPage.typeSlug(slug);
        createHotelPage.typeName(genericMethods.getRandomName("hotelName") + "_" + genericMethods.getRandomString(5, "numeric"));
        JSONObject randomAddress = genericMethods.getRandomAddress();        
        createHotelPage.typeAddress(randomAddress.get("line1").toString());
        createHotelPage.typePostcode(randomAddress.get("postcode").toString());
        createHotelPage.typeCountry(randomAddress.get("country").toString());
        createHotelPage.typePhone(genericMethods.getRandomString(10, "numeric"));
        createHotelPage.typeEmail(genericMethods.getRandomString(10, "lowercase") + "@example.com");
        createHotelPage.typeDescription(genericMethods.getRandomString(50, "any"));
        createHotelPage.typePrivacyLink("https://www.high-level-software.com");
        createHotelPage.typeTermsLink(genericMethods.getRandomString(50, "any"));
        createHotelPage.typeCancellationLink(genericMethods.getRandomString(50, "any"));
        createHotelPage.clearCheckinFrom();
        createHotelPage.typeCheckinFrom(genericMethods.getRandomMinutes("AM"));
        createHotelPage.clearCheckoutBy();
        createHotelPage.typeCheckoutBy(genericMethods.getRandomMinutes("PM"));     
        createHotelPage.typeTaxRate("20");
        createHotelPage.typeTaxNumber(genericMethods.getRandomString(5, "numeric"));
*/
        /*
         * Create Hotel page > Dashboard page
         */

 //       dashboardPage = createHotelPage.clickSubmit();

        // roomTypesCreatePage = dashboardPage.navigateToRoomTypes

        // BAD NAVIGATION

        // CREATE SOME ROOM TYPES
/*
        for(int i=0; i < roomTypeLetters.length; i++) {
            driver.get("http://localhost:8000/hotels/" + slug +"/room-types/create");
            waitForPageLoad();
            roomTypesCreatePage.typeType("Load test room");
            roomTypesCreatePage.typeCode(roomTypeLetters[i]);
            roomTypesCreatePage.typeTitle("Load test room " + roomTypeLetters[i]);
            roomTypesCreatePage.typeDescription("Load test room");
            roomTypesCreatePage.typeLetter(roomTypeLetters[i]);
            roomTypesCreatePage.typePrice("1");
            roomTypesCreatePage.typePoints("1");
            roomTypesCreatePage.typeMaxNumOccupants("2");
            roomTypesCreatePage.typeMaxNumAdults("2");
            roomTypesCreatePage.typeMaxNumChildren("0");
            roomTypesCreatePage.clickSubmit();
            waitForPageLoad();
        }

        // CREATE SOME ROOMS
        for(int i=1; i < roomTypeLetters.length + 1; i++) { // starts at 1 due to selectRoomType never being 0
            driver.get("http://localhost:8000/hotels/" + slug +"/rooms/create");
            waitForPageLoad();
            roomsCreatePage.typeRoomNo(Integer.toString(i));
            roomsCreatePage.typeDirections("over there" + i);
            roomsCreatePage.selectRoomType(i); 
            roomsCreatePage.clickSubmit();
            waitForPageLoad();
        }
        */

        //driver.get("http://localhost:8000/hotels/" + slug +"/v2/rates/create");
        driver.get("http://localhost:8000/hotels/" + "fxmakxmifb" +"/v2/rates/overview");
        genericMethods.waitForPageLoad(driver);
        for(int r=101; r<105; r++) {

        driver.findElement(By.xpath("/html/body/div[1]/div/div/div[1]/div/div/div/div/div[1]/div[3]/div[2]/a")).click();
        ratesCreatePage = new RatesCreatePage(getDriver(), getReport(), getEnvironment(), "fxmakxmifb", "not sure");
        genericMethods.waitForPageLoad(driver);
        ratesCreatePage.typeName("Rate " + r + " " + "fxmakxmifb");
        ratesCreatePage.typeRateCode("lr" + r);
        ratesCreatePage.typeDescription("Load testing rate " + r + " " + "fxmakxmifb");
        ratesCreatePage.selectAddARoomSection();
        for(int i=1; i < roomTypeLetters.length + 1; i++) {   // starts at 1 due to selectRoomType never being 0
            ratesCreatePage.selectRoomType(i + 1);
            ratesCreatePage.typeRoomTypePrice(roomTypeLetters[i-1],"10" + r + i);
        } 
        ratesCreatePage.clickSave();
        genericMethods.waitForPageLoad(driver);
        }
    }

}