import org.testng.annotations.Test;
import org.json.simple.JSONArray;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriverException;
import org.testng.Assert;
import java.util.ArrayList;
import java.util.List;
import org.openqa.selenium.WebElement;

public class CheckAvailability_OLD extends BaseTest {

    // required classes
    GenericMethods genericMethods;
    Data data;
    StaffLoginPage staffLoginPage;
    HotelsPage hotelsPage;

    @Test
    public void checkAvailability() throws InterruptedException {
        genericMethods = new GenericMethods(report);
        data = new Data();
        executeTest();
        Thread.sleep(3000);
    }

    // Navigate to the appropriate URL
    // Log in using valid credentials
    // Wait until the user is on the hotels page
    // Verify it is actually the hotels page
    // Navigate to the Create Hotel page
    public void executeTest() {

        String[] slugs = {
             "stephenshotel"
            // "joshshotel"
            // "gatewaycarvanpark" CHECKED
        };

        /*
         * Staff Login page > Hotels page
         */
       

         for(int i=0; i<slugs.length;i++) {
            int totalRoomTypes = 26;
            ArrayList<ArrayList<String>> rates = new ArrayList<>(totalRoomTypes);
            for(int a=0; a < totalRoomTypes; a++) {
                    rates.add(new ArrayList<>());
            }

            ArrayList<ArrayList<String>> oldRateByDate = new ArrayList<>(totalRoomTypes);
            for(int c=0; c < totalRoomTypes; c++) {
                    oldRateByDate.add(new ArrayList<>());
            }

            ArrayList<ArrayList<String>> newRates = new ArrayList<>(totalRoomTypes);
            for(int b=0; b < totalRoomTypes; b++) {
                    newRates.add(new ArrayList<>());
            }

            ArrayList<ArrayList<String>> newRateByDate = new ArrayList<>(totalRoomTypes);
            for(int d=0; d < totalRoomTypes; d++) {
                    newRateByDate.add(new ArrayList<>());
            }

            driver.get("https://develop.dev.high-level-software.com/staff/login");
            staffLoginPage = new StaffLoginPage(driver, report, getEnvironment());
            String[] loginValues = new String[]{"email", "password"};
            JSONArray loginDetails = data.returnJSONArray("/src/testdata/staffAdminLoginAlways.json", loginValues);
            hotelsPage = staffLoginPage.loginAs(loginDetails.get(0).toString(), loginDetails.get(1).toString());

            driver.get("https://develop.dev.high-level-software.com/hotels/" + slugs[i] +"/room-types");
             genericMethods.waitForPageLoad(driver);
            List<WebElement> rooms = driver.findElements(By.xpath("/html/body/div[2]/div/div[1]//div[@class='box model']"));

             int totalRooms = rooms.size();
             if(totalRooms >9) {
                totalRooms = 9;
            }
            System.out.println("ROOMS: " + totalRooms + ", " + rooms.size());

            driver.get("https://develop.dev.high-level-software.com/hotels/" + slugs[i] +"/restrictions");
             genericMethods.waitForPageLoad(driver);
            int row = 2;
            int column = 3;
            int columns = 14;
            for(int k=0; k<totalRooms;k++) {
                // For each potential room type row
                for(int j=0; j<columns;j++) {
                    // For each column want to get
                    String availability = driver.findElement(By.xpath("/html/body/div[1]/div/div[2]/form/table/tbody/tr[" + row + "]/td[" + column + "]/input")).getAttribute("value");
                    //availability = "1 / 1";
                    String availabilityActual;
                    if (availability.length() == 0) {
                        availabilityActual = "0";
                    } else {
                        availabilityActual = availability.substring(0,1);
                    }
                    //System.out.println(availability + ", " + availabilityActual);
                    rates.get(k).add(availabilityActual);
                    column=column+1;
                }
                column=3;
                row = row+6;
            }

            driver.get("https://develop.dev.high-level-software.com/hotels/" + slugs[i] +"/room-types/rateByDate");
             genericMethods.waitForPageLoad(driver);
            int rateByDateRow = 2;
            int rateByDateColumn = 3;
            for(int k=0; k<totalRooms;k++) {
                // For each potential room type row
                for(int j=0; j<columns;j++) {
                    // For each column want to get
                    String rateVal = "";
                    rateVal = driver.findElement(By.xpath("/html/body/div[1]/div/div[2]/form/table/tbody/tr[" + rateByDateRow + "]/td[" + rateByDateColumn + "]/input")).getAttribute("value");
                    //availability = "1 / 1";
                    String rateValActual = "";

                    if (rateVal.length() == 0) {
                        rateValActual = driver.findElement(By.xpath("/html/body/div[1]/div/div[2]/form/table/tbody/tr[" + rateByDateRow + "]/td[" + rateByDateColumn + "]/input")).getAttribute("placeholder");
                        if (rateValActual.length() > 2) {
                            rateValActual = rateValActual.substring(0,rateValActual.length()-2) + "." + rateValActual.substring(rateValActual.length()-2,rateValActual.length());
                        }
                    } else {
                        rateValActual = rateVal;
                         if (rateValActual.length() > 2) {
                            rateValActual = rateValActual.substring(0,rateValActual.length()-2) + "." + rateValActual.substring(rateValActual.length()-2,rateValActual.length());
                         }
                    }
                    
                    // Trim zeros
                    if (rateValActual.length() > 2) {
                        if (rateValActual.substring(rateValActual.length()-1, rateValActual.length()).equals("0")) {
                            rateValActual = rateValActual.substring(0,rateValActual.length()-1);
                        }

                        if (rateValActual.substring(rateValActual.length()-1, rateValActual.length()).equals("0")) {
                            rateValActual = rateValActual.substring(0,rateValActual.length()-2);
                        }
                    }

                    //System.out.println(rateVal + ", " + rateValActual);
                    oldRateByDate.get(k).add(rateValActual);
                    rateByDateColumn=rateByDateColumn+1;
                }
                rateByDateColumn=3;
                rateByDateRow = rateByDateRow+4;
            }

            // Onto UAT
            driver.get("https://uat.dev.high-level-software.com/staff/login");
            staffLoginPage = new StaffLoginPage(driver, report, getEnvironment());
            
            hotelsPage = staffLoginPage.loginAs(loginDetails.get(0).toString(), loginDetails.get(1).toString());

            driver.get("https://uat.dev.high-level-software.com/hotels/" + slugs[i] +"/v2/rates/grid");
            genericMethods.waitForPageLoad(driver);

            int newRow = 1;
            int newColumn = 1;
            //int columns = 14;
            for(int k=0; k<totalRooms;k++) {
                // For each potential room type row
                for(int j=0; j<columns;j++) {
                    // For each column want to get

                    // Availability row
                    WebElement element = driver.findElement(By.cssSelector("div.rate-triage-row-wrapper:nth-child(" + newRow + ") > div:nth-child(1) > div:nth-child(3) > div:nth-child(" + newColumn + ")"));
                    
                    int n=6;
                    for (int ta=1; ta<=n; ta++)
                    {   
                        try {
                            try {
                                element.click();
                                break;
                            } catch(WebDriverException driverException) {
                                System.out.println("Click on element failed. Attempt: "+ta+"/"+n);
                                Thread.sleep(2000);
                            }
                            if(ta==n) {
                                Assert.fail("Failed to click "+n+" times");
                            }
                        }
                        catch(InterruptedException e) {
                            System.out.println("thread interrupted eep");
                        }
                    }

                    String availability = element.getText();
                    
                    String availabilityActual;
                    if (availability.length() == 0) {
                        availabilityActual = "0";
                    } else {
                        availabilityActual = availability.substring(0,1);
                    }
                    //System.out.println("Column: " +  newColumn + " Availability: " + availabilityActual);
                    newRates.get(k).add(availabilityActual);


                    // Rates row
                    int nextRow = newRow + 1;
                    WebElement element2 = driver.findElement(By.cssSelector("div.rate-triage-row-wrapper:nth-child(" + nextRow + ") > div:nth-child(1) > div:nth-child(3) > div:nth-child(" + newColumn + ") > input:nth-child(2)"));

                    for (int ta=1; ta<=n; ta++)
                    {   
                        try {
                            try {
                                element2.click();
                                break;
                            } catch(WebDriverException driverException) {
                                System.out.println("Click on element failed. Attempt: "+ta+"/"+n);
                                Thread.sleep(2000);
                            }
                            if(ta==n) {
                                Assert.fail("Failed to click "+n+" times");
                            }
                        }
                        catch(InterruptedException e) {
                            System.out.println("thread interrupted eep");
                        }
                    }

                    String rateValue = element2.getAttribute("value");
                    
                    
                    // System.out.println("Column: " +  newColumn + " Availability: " + availabilityActual + " || " + rateValue);
                    newRateByDate.get(k).add(rateValue);

                    newColumn=newColumn+1;
                }
                newColumn=1;
                newRow = newRow+2;
            }

            // Compare arraylists

            for(int y=0; y<totalRooms; y++) {
                for(int z=0; z<columns; z++) {
                    System.out.println("Old, Slug: " + slugs[i] + " Row: " + y + " Column: " + z + " Avail Value: " + rates.get(y).get(z));
                    System.out.println("New, Slug: " + slugs[i] + " Row: " + y + " Column: " + z + " Avail Value: " + newRates.get(y).get(z));
                    boolean avail = false;
                    if(rates.get(y).get(z).equals(newRates.get(y).get(z)))
                        avail = true;
                    System.out.println("Availability match: " + avail);

                    System.out.println("Old, Slug: " + slugs[i] + " Row: " + y + " Column: " + z + " Rate Value: " + oldRateByDate.get(y).get(z));
                    System.out.println("New, Slug: " + slugs[i] + " Row: " + y + " Column: " + z + " Rate Value: " + newRateByDate.get(y).get(z));
                    boolean rateBool = false;
                    if(oldRateByDate.get(y).get(z).equals(newRateByDate.get(y).get(z)))
                        rateBool = true;
                    System.out.println("Rate match: " + rateBool);
                   
                   Assert.assertEquals(newRates.get(y).get(z), rates.get(y).get(z));
                   Assert.assertEquals(oldRateByDate.get(y).get(z), newRateByDate.get(y).get(z));
                }
            }
         }
    }
}