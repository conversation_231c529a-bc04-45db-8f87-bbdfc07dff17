import io.restassured.RestAssured;
import org.json.simple.JSONObject;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class TerminalUserPost extends BaseTest
{
    // testNG soft assetion
    SoftAssert softAssert;

    // required classes
    SoftAssertions softAssertions;
    GenericMethods genericMethods;
    HotelierLoginPage hotlierLoginPage;
    HotelsPage hotelsPage;

    protected String environment = "";
  
    @Parameters({"environment"})
    @Test
    public void hotelierLoginTests(final String environment)
    {
        softAssert = new SoftAssert();
        softAssertions = new SoftAssertions(getDriver(), getReport(), softAssert);
        genericMethods = new GenericMethods(report);
        hotlierLoginPage = new HotelierLoginPage(getDriver(), report, getEnvironment());
        hotelsPage = new HotelsPage(getDriver(), report, getEnvironment(), new JSONObject());

        this.environment = environment;
    
        executeTest();
    }

    // this test assumes we are logging in as a hotelier that is only attached to the hotel "Cardiff Plaza"
    public void executeTest() {
        //RestAssured.get("http://localhost:8000/hotels/cardiff/aztec");
        System.out.println("RESPONSE: " + RestAssured.given().contentType("application/json").headers("username", "<EMAIL>", "password", "shady").when().get("http://localhost:8000/hotels/cardiff/aztec").andReturn().asString());

    }
}