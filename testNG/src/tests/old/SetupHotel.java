import org.testng.annotations.Test;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import java.util.concurrent.TimeUnit;

public class SetupHotel   extends BaseTest {
    // required classes
    GenericMethods genericMethods;
    Data data;
    StaffLoginPage staffLoginPage;
    HotelsPage hotelsPage;
    CreateHotelPage createHotelPage;
    RoomTypesCreatePage roomTypesCreatePage;
    RoomsCreatePage roomsCreatePage;
    RatesCreatePage ratesCreatePage;
    CategoriesCreatePage categoriesCreatePage;
    ConsumablesCreatePage consumablesCreatePage;
    HireablesCreatePage hireablesCreatePage;
    OutletsCreatePage outletsCreatePage;
    VenuesCreatePage venuesCreatePage;

    @Test
    public void setupHotel() throws InterruptedException  {
        genericMethods = new GenericMethods(report);
        data = new Data();
        staffLoginPage = new StaffLoginPage(getDriver(), report, getEnvironment());
        hotelsPage = new HotelsPage(getDriver(), report, getEnvironment(), new JSONObject());
        createHotelPage = new CreateHotelPage(getDriver(), report, getEnvironment());

        executeTest();
        Thread.sleep(3000);
    }

    // Navigate to the appropriate URL
    // Log in using valid credentials
    // Wait until the user is on the hotels page
    // Verify it is actually the hotels page
    // Navigate to the Create Hotel page
    public void executeTest() {
        driver.get(staffLoginPage.pageUrl); 
        String[] loginValues = new String[]{"email", "password"};
        JSONArray loginDetails = data.returnJSONArray("/src/testdata/staffAdminLoginAlways.json", loginValues);
        staffLoginPage.loginAs(loginDetails.get(0).toString(), loginDetails.get(1).toString());
        genericMethods.waitForExpectedURL(driver, hotelsPage.pageUrl);
        genericMethods.assertMatchingURL(driver, hotelsPage.pageUrl);

        createHotelPage = hotelsPage.clickCreateHotelLink();

        genericMethods.waitForExpectedURL(driver, createHotelPage.pageUrl);
        genericMethods.assertMatchingURL(driver, createHotelPage.pageUrl);
        //driver.manage().timeouts().implicitlyWait(5, TimeUnit.SECONDS);
        //WebDriverWait wait = new WebDriverWait(driver, 15);
        //wait.until(ExpectedConditions.elementToBeClickable(By.cssSelector("#status")));

        //WebDriverWait wait = new WebDriverWait(webDriver, timeoutInSeconds);
        //wait.until(ExpectedConditions.visibilityOfElementLocated(By.id<locator>));

        createHotelPage.selectStatus("Live");
        String slug = data.getRandomString(10, "lowercase");
        createHotelPage.typeSlug(slug);
        String hotelName = data.getRandomName("hotelName") + "_" + data.getRandomString(5, "numeric");
        createHotelPage.typeName(hotelName);
        
        JSONObject randomAddress = data.getRandomAddress();
        createHotelPage.typeAddress(randomAddress.get("line1").toString());
        createHotelPage.typePostcode(randomAddress.get("postcode").toString());
        createHotelPage.typeCountry(randomAddress.get("country").toString());

        createHotelPage.typePhone(data.getRandomString(10, "numeric"));

        createHotelPage.typeEmail(data.getRandomString(10, "lowercase") + "@example.com");

        createHotelPage.typeDescription(data.getRandomString(50, "any"));

        createHotelPage.typePrivacyLink("https://www.high-level-software.com");
        createHotelPage.typeTermsLink(data.getRandomString(50, "any"));
        createHotelPage.typeCancellationLink(data.getRandomString(50, "any"));

        createHotelPage.clearCheckinFrom();
        createHotelPage.typeCheckinFrom(data.getRandomMinutes("AM"));

        createHotelPage.clearCheckoutBy();
        createHotelPage.typeCheckoutBy(data.getRandomMinutes("PM"));

        createHotelPage.typeTaxRate("20");
        createHotelPage.typeTaxNumber(data.getRandomString(5, "numeric"));

        // Don't enter an Rdx Code

        createHotelPage.clickSubmit();
        categoriesCreatePage = new CategoriesCreatePage(getDriver(), getReport(), getEnvironment(), slug, hotelName);
        consumablesCreatePage = new ConsumablesCreatePage(getDriver(), getReport(), getEnvironment(), slug, hotelName);
        hireablesCreatePage = new HireablesCreatePage(getDriver(), getReport(), getEnvironment(), slug, hotelName);
        outletsCreatePage = new OutletsCreatePage(getDriver(), getReport(), getEnvironment(), slug, hotelName);
        ratesCreatePage = new RatesCreatePage(getDriver(), getReport(), getEnvironment(), slug, hotelName);
        roomsCreatePage = new RoomsCreatePage(getDriver(), getReport(), getEnvironment(), slug, hotelName);
        venuesCreatePage = new VenuesCreatePage(getDriver(), getReport(), getEnvironment(), slug, hotelName);

        // Navigate to the room types create page
        driver.get("http://localhost:8000/hotels/" + slug +"/room-types/create");
        // Create a room type
        roomTypesCreatePage = new RoomTypesCreatePage(getDriver(), report, "http://localhost:8000", slug, hotelName);
        roomTypesCreatePage.typeType("Double Room");
        roomTypesCreatePage.typeCode("dbl");
        roomTypesCreatePage.typeTitle("Double Room");
        roomTypesCreatePage.typeDescription("Double Room Description");
        roomTypesCreatePage.typeLetter("D");
        roomTypesCreatePage.typePrice("1");
        roomTypesCreatePage.typePoints("1");
        roomTypesCreatePage.typeMaxNumOccupants("2");
        roomTypesCreatePage.typeMaxNumAdults("2");
        roomTypesCreatePage.typeMaxNumChildren("0");
        roomTypesCreatePage.clickSubmit();

        // Create 4 rooms
        for(int i=1; i<5;i++) {
            driver.get("http://localhost:8000/hotels/" + slug +"/rooms/create");
            roomsCreatePage.typeRoomNo(Integer.toString(i));
            roomsCreatePage.typeDirections("over there" + i);
            roomsCreatePage.selectRoomType("1"); // first room type created, provindg unsorted
            roomsCreatePage.clickSubmit();
        }

        driver.get("http://localhost:8000/hotels/" + slug +"/categories/create");
        categoriesCreatePage.typeCode("FOOD");
        categoriesCreatePage.typeTitle("FOOD");
        categoriesCreatePage.clickSubmit();

        driver.get("http://localhost:8000/hotels/" + slug +"/categories/create");
        categoriesCreatePage.typeCode("DRINK");
        categoriesCreatePage.typeTitle("DRINK");
        categoriesCreatePage.clickSubmit();

        driver.get("http://localhost:8000/hotels/" + slug +"/categories/create");
        categoriesCreatePage.typeCode("MISC");
        categoriesCreatePage.typeTitle("MISC");
        categoriesCreatePage.clickSubmit();

        driver.get("http://localhost:8000/hotels/" + slug +"/consumables/create");
        consumablesCreatePage.typeType("Packed Lunch Tax");
        consumablesCreatePage.typeCode("TLUN");
        consumablesCreatePage.typeDescription("Packed Lunch Tax");
        consumablesCreatePage.typePrice("6");
        consumablesCreatePage.typeTax("Yes");
        // FOOD is 1
        consumablesCreatePage.selectCategory(1);
        consumablesCreatePage.clickSubmit();

        driver.get("http://localhost:8000/hotels/" + slug +"/consumables/create");
        consumablesCreatePage.typeType("Latte Taxed");
        consumablesCreatePage.typeCode("TLAT");
        consumablesCreatePage.typeDescription("Latte Taxed");
        consumablesCreatePage.typePrice("3.43");
        consumablesCreatePage.typeTax("Yes");
        // DRINK is 2
        consumablesCreatePage.selectCategory(2);
        consumablesCreatePage.clickSubmit();

        driver.get("http://localhost:8000/hotels/" + slug +"/consumables/create");
        consumablesCreatePage.typeType("Lunch not taxed");
        consumablesCreatePage.typeCode("NTLUN");
        consumablesCreatePage.typeDescription("Lunch not taxed");
        consumablesCreatePage.typePrice("2.65");
        consumablesCreatePage.typeTax("No");
        // FOOD is 1
        consumablesCreatePage.selectCategory(1);
        consumablesCreatePage.clickSubmit();

        driver.get("http://localhost:8000/hotels/" + slug +"/consumables/create");
        consumablesCreatePage.typeType("Latte not taxed");
        consumablesCreatePage.typeCode("NTLAT");
        consumablesCreatePage.typeDescription("Latte not taxed");
        consumablesCreatePage.typePrice("7");
        consumablesCreatePage.typeTax("No");
        // DRINK is 2
        consumablesCreatePage.selectCategory(2);
        consumablesCreatePage.clickSubmit();

        // Hire Creations

        driver.get("http://localhost:8000/hotels/" + slug +"/hireables/create");
        consumablesCreatePage.typeType("Cot taxable");
        consumablesCreatePage.typeCode("TCOT");
        consumablesCreatePage.typeDescription("Cot taxable");
        consumablesCreatePage.typePrice("17.90");
        consumablesCreatePage.typeTax("Yes");
        // MISC is 3
        consumablesCreatePage.selectCategory(3);
        consumablesCreatePage.clickSubmit();

        driver.get("http://localhost:8000/hotels/" + slug +"/hireables/create");
        consumablesCreatePage.typeType("Cot not taxable");
        consumablesCreatePage.typeCode("NTCOT");
        consumablesCreatePage.typeDescription("Cot not taxable");
        consumablesCreatePage.typePrice("17.90");
        consumablesCreatePage.typeTax("No");
        // MISC is 3
        consumablesCreatePage.selectCategory(3);
        consumablesCreatePage.clickSubmit();

        driver.get("http://localhost:8000/hotels/" + slug +"/hireables/create");
        consumablesCreatePage.typeType("Coffee Machine tax");
        consumablesCreatePage.typeCode("TCOF");
        consumablesCreatePage.typeDescription("Coffee Machine tax");
        consumablesCreatePage.typePrice("30");
        consumablesCreatePage.typeTax("Yes");
        // DRINK is 2
        consumablesCreatePage.selectCategory(2);
        consumablesCreatePage.clickSubmit();

        driver.get("http://localhost:8000/hotels/" + slug +"/hireables/create");
        consumablesCreatePage.typeType("Teapot not taxable");
        consumablesCreatePage.typeCode("NTTEA");
        consumablesCreatePage.typeDescription("Teapot not taxable");
        consumablesCreatePage.typePrice("5");
        consumablesCreatePage.typeTax("No");
        // DRINK is 2
        consumablesCreatePage.selectCategory(2);
        consumablesCreatePage.clickSubmit();


        // Create Outlet
        driver.get("http://localhost:8000/hotels/" + slug +"/outlets/create");
        outletsCreatePage.typeType("Bar");
        outletsCreatePage.typeCode("Bar");
        outletsCreatePage.clickSubmit();

        // Create Venue
        driver.get("http://localhost:8000/hotels/" + slug +"/venues/create");
        venuesCreatePage.typeType("Garden");
        venuesCreatePage.typeCode("Garden");
        venuesCreatePage.typeTitle("Garden");
        venuesCreatePage.typeDescription("Garden Party");
        venuesCreatePage.typeMaxNumOccupants("100");
        venuesCreatePage.clickSubmit();

        // Go to Extras page
        // Create extras


        
        // Go to Rate Plan
        // Create Rate Plan
        driver.get("http://localhost:8000/hotels/" + slug +"/v2/rates/create");
        driver.manage().timeouts().implicitlyWait(5, TimeUnit.SECONDS);

        // NEED TO PASS ROOM CODE

        ratesCreatePage.typeName("Double Rate");
        ratesCreatePage.typeRateCode("dr2");
        ratesCreatePage.typeDescription("Double Rate Desc");
        ratesCreatePage.selectAddARoomSection();
        ratesCreatePage.selectRoomType(1);
        ratesCreatePage.typeRoomTypePrice("dbl","59");
        ratesCreatePage.clickSave();
 
    }
}