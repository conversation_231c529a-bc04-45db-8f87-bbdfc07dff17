import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.testng.annotations.Test;

    /**
     *
     * All the tests relating to the creation of Hoteliers
     *
     * AC1: A Staff Admin user must be able to create a new hotelier
     * AC2: A Staff Support user must be able to create a new hotelier
     * AC3: A Staff Manager user must be able to create a new hotelier
     * AC4: A Staff Sales user must be able to create a new hotelier
     *
     * AC5: A Group Admin must be able to create a new hotelier, that has access to that hotel
     * AC6: A Group Manager must be able to create a new hotelier, that has access to that hotel
     * AC7: A Hotelier Manager must be able to create a new hotelier, that has access to that hotel
     *
     * AC8: A Hotelier Front Desk user must not be able to create a new hotelier, that has access to that hotel
     * AC9: A Terminal user must not be able to create a new hotelier, that has access to that hotel
     * AC10: A Guest with account user must not be able to create a new hotelier, that has access to that hotel
     * AC11: A Corporation user must not be able to create a new hotelier, that has access to that hotel
     *
     * AC12: A Group Admin user must not be able to create a new hotelier, that does not have access to that hotel
     * AC12: A Group Manager user must not be able to create a new hotelier, that does not have access to that hotel
     * AC12: A Hotelier Manager user must not be able to create a new hotelier, that does not have access to that hotel
     * AC12: A Hotelier Front Desk user must not be able to create a new hotelier, that does not have access to that hotel
     * AC12: A Terminal user must not be able to create a new hotelier, that does not have access to that hotel
     * AC12: A Guest with account user must not be able to create a new hotelier, that does not have access to that hotel
     * AC12: A Corporation user must not be able to create a new hotelier, that does not have access to that hotel
     * AC12: A Guest without an account user must not be able to create a new hotelier, that does not have access to that hotel
     *
     */
public class HoteliersCreate extends BaseTest {

    // Required classes
    GenericMethods genericMethods;
    Login login;
    Assertion assertion;
    Data data;
    CurrentData currentData;
    HotelierLoginPage hotelierLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;
    HoteliersPage hoteliersPage;
    HoteliersCreatePage hoteliersCreatePage;

    // Variables used throughout test
    String testDataHotelPath = "";
    String hotelName = "";
    String hotelSlug = "";
    int hoteliers = 0;

    /**
     *
     * This test covers the acceptance criteria of:
     *
     * AC1: A Staff Admin user must be able to create a new hotelier
     * AC2: A Staff Support user must be able to create a new hotelier
     * AC3: A Staff Manager user must be able to create a new hotelier
     * AC4: A Staff Sales user must be able to create a new hotelier
     *
     * AC5: A Group Admin must be able to create a new hotelier, that has access to that hotel
     * AC6: A Group Manager must be able to create a new hotelier, that has access to that hotel
     * AC7: A Hotelier Manager must be able to create a new hotelier, that has access to that hotel
     * *
     * This test covers the test cases of:
     * TC1: Verify that a user with the right permission is able to create a hotelier on the Create Hotelier page
     *
     * TC1 requires the following test data to cover it:
     * TD1: A Staff Admin user, that is able to log in
     * TD2: A Staff Support user, that is able to log in
     * TD3: A Staff Manager user, that is able to log in
     * TD4: A Staff Sales user, that is able to log in
     * TD5: A Group Admin user, that has access to a hotel, that is able to log in
     * TD6: A Group Manager, that has access to a hotel, that is able to log in
     * TD7: A Hotelier Manager, that has access to a hotel, that is able to log in
     *
     */
    @Test(groups={"hoteliersCreateTC1"})
    public void hoteliersCreateTC1() {
        // Get the test data path
        String testDataPath = "/src/testdata/" + getIteration() + "/pms/hoteliersCreate/" + getTestData();

        currentData = new CurrentData(getReport(), getIteration());
        // Set the hotel data
        testDataHotelPath = testDataPath + "Hotel.json";
        testDataHotelPath = currentData.getHotel(testDataHotelPath);
        setHotelData();

        // Parse the login details
        String testDataLoginPath = testDataPath + "Login.json";
        String[] loginValues = new String[]{"email", "password", "user"};
        JSONArray loginDetails = data.returnJSONArray(testDataLoginPath, loginValues);
        String loginEmail = loginDetails.get(0).toString();
        String loginPassword = loginDetails.get(1).toString();
        String loginUser = loginDetails.get(2).toString();

        getReport().setLocation("src/reports/" + getIteration() + "/" + getBrowserToUse() +  "/failed/pms/hoteliersCreate/" + getTestData() + "Report_Export.txt");

        getReport().appendExport("*****");
        getReport().appendExport("Start of test:");
        getReport().appendExport("TC1: Verify that a user with the right permission is able to create a hotelier on the Create Hotelier page");
        getReport().appendExport("Covers AC of:");
        getReport().appendExport("AC1: A Staff Admin user must be able to create a new hotelier");
        getReport().appendExport("AC2: A Staff Support user must be able to create a new hotelier");
        getReport().appendExport("AC3: A Staff Manager user must be able to create a new hotelier");
        getReport().appendExport("AC4: A Staff Sales user must be able to create a new hotelier");
        getReport().appendExport("AC5: A Group Admin must be able to create a new hotelier, that has access to that hotel");
        getReport().appendExport("AC6: A Group Manager must be able to create a new hotelier, that has access to that hotel");
        getReport().appendExport("AC7: A Hotelier Manager must be able to create a new hotelier, that has access to that hotel");
        getReport().appendExport("*****");

        genericMethods = new GenericMethods(getReport());
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        assertion = new Assertion(getReport());
        data = new Data();

        // Log in
        login.loginRouting();
        startOfTestPageRouting(loginUser, testDataLoginPath);

        hotelsPage.typeHotelNameInSearch(hotelName);
        hotelsPage = hotelsPage.clickSearch();
        hotelsPage.validatePage();
        dashboardPage = hotelsPage.selectHotel(hotelSlug, hotelName);
        dashboardPage.validatePage();

        hoteliers =  currentData.getHotelierCount(testDataHotelPath);
        getReport().appendExport("INFO: Hoteliers count: " + hoteliers);
        if(hoteliers > 0) {
            hoteliersPage = dashboardPage.horizontalOptionsPage.navigateToHoteliersPage();
            hoteliersPage.validatePage();
            hoteliersCreatePage = hoteliersPage.clickCreateHotelier();
        } else {
            hoteliersCreatePage = dashboardPage.horizontalOptionsPage.navigateToHoteliersCreatePageRedirect();
        }
        hoteliersCreatePage.validatePage();

        // ALL TYPES - manager, FD, Terminal

//        // Set the data for the test
//        setData();
//        // Insert the data into page for the test
//        typeData();
//        // Assert that the values inserted are actually there
//        assertValuesInserted();
//
//        // Navigate to Staff page
//        hoteliersPage = hoteliersCreatePage.clickSubmit();
//        hoteliersPage.validatePage();
//
//        // Set uuid
//        setDataUuid();
//
//        // Add the data set to an export file
//        addDataToExportFile();
//
//        // Export the file
//        export.exportFile(testDataExportPath);
//
//        // Export current data file
//        export.exportFileCurrentData(exportCurrentDataPath + role + "_" + uuid);

        getReport().setLocation("src/reports/" + getIteration() + "/" + getBrowserToUse() +  "/passed/pms/hoteliersCreate/" + getTestData() + "Report_Export.txt");
    }




    public void startOfTestPageRouting(final String user, String testDataLoginPath) {
        if (user.equals("staff-admin") || user.equals("staff-support") || user.equals("staff-manager") || user.equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
        else if (user.equals("group-admin")){
            // groupRatesOverviewPage = login.groupRatesOverviewPage;
            // groupRatesOverviewPage.validatePage(environment);
            // driver.get(environment + "/hotels");
            // hotelsPage = new HotelsPage(driver);
        }
        else if (user.equals("group-manager") || user.equals("hotelier-manager") || user.equals("hotelier-front-desk")) {
            JSONArray hotelDetails = (JSONArray) data.returnJSONArray(testDataLoginPath, new String[]{"hotels"}).get(0);
            // If only one hotel, enter url with /hotels, else multiple user should be on hotels link already
            if(hotelDetails.size()==1) {
                dashboardPage = login.dashboardPage;
                dashboardPage.validatePage();
                driver.get(getEnvironment() + "/hotels");
                hotelsPage = new HotelsPage(driver, report, getEnvironment(), new JSONObject());
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (user.equals("hotelier-terminal")){
            JSONArray hotelDetails = (JSONArray) data.returnJSONArray(testDataLoginPath, new String[]{"hotels"}).get(0);
            // If only one hotel, terminal user doesn't get into pms, else multiple user should be on hotels link already
            if(hotelDetails.size()==1) {
                hotelierLoginPage = login.hotelierLoginPage;
                hotelierLoginPage.validatePage();
                driver.get(getEnvironment() + "/hotels");
                hotelsPage = new HotelsPage(driver, report, getEnvironment(), new JSONObject());
            } else {
                hotelsPage = login.hotelsPage;
                hotelsPage.validatePage();
            }
        }
        else if (user.equals("guest") || user.equals("corporation")) {
            // availabilityPage = login.availabilityPage;
            // availabilityPage.validatePage(environment);
            // driver.get(environment + "/hotels");
            // hotelsPage = new HotelsPage(driver);
        }
    }

    public void setHotelData() {
        String[] hotelValues = new String[]{"slug", "name"};
        JSONArray hotelDetails = data.returnJSONArray(testDataHotelPath, hotelValues);
        hotelSlug = hotelDetails.get(0).toString();
        hotelName = hotelDetails.get(1).toString();
    }


}