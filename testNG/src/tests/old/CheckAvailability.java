import org.testng.Assert;
//import org.testng.annotations.DataProvider;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

import org.json.simple.JSONArray;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

public class CheckAvailability extends BaseTest {
    // required classes
    GenericMethods genericMethods;
    Data data;
    Login login;
    StaffLoginPage staffLoginPage;
    HotelsPage hotelsPage;
    DashboardPage dashboardPage;

    // Parameter variables
    protected String browserToUse = "";
    protected String dataToUse = "random";
    protected String environment = "";
    protected String testData = "";
    protected String slugProvided = "";

    @Parameters({"browserToUse", "dataToUse", "environment", "testData", "slugProvided"})
    @Test(groups={"quick_check"})
    public void CheckAvailabilityTC1(final String browserToUse, final String dataToUse, final String environment, final String testData, String slugProvided)    {

        this.browserToUse = browserToUse;
        this.environment = environment;
        this.dataToUse = dataToUse;
        this.testData = testData;
        this.slugProvided = slugProvided;

        genericMethods = new GenericMethods(report);
        data = new Data();
        
        // Parse the login details
        String testDataLoginPath = "/src/testdata/pms/createStaffUser/" + testData + "Login.json";
        String[] loginValues = new String[]{"email", "password", "user"};
        JSONArray loginDetails = data.returnJSONArray("/src/testdata/templogin.json", loginValues);
        String email = loginDetails.get(0).toString();
        String password = loginDetails.get(1).toString();
        String user = loginDetails.get(2).toString();

        // Log in
        login = new Login(getDriver(), getReport(), getEnvironment(), getIteration(), testDataLoginPath);
        login.loginRouting();
        startOfTestPageRouting(user, testDataLoginPath);

        // Potentially parse in a dataprovider
        final String[] slugs = {
            // "01test",
            // "10test",
            // "011test",
            // "012test",
            // "02test",
            // "03test",
            // "04test",
            // "05test",
            // "06test",
            // "07test",
            // "08test",
            // "09test",
            // "zztest",
            // "32170",
            // "rdx1",
            // "london",
            // "Thebesthotel",
            // "glasgow",
            // "crdx3",
            // "cardiffinn",
            // "cardiff",
            // "chris",
            // "happyhotel",
            // "hotel100",
            // "hotel101",
            // "hotel13",
            // "import-sync",
            // "NeethuHotel",
            // "new",
            // "Newhotel",
            // "pentest",
            // "rhtest",
            // "scenario1",
            // "scenario10",
            // "Scenario11",
            // "scenario12",
            // "Scenario13",
            // "scenario14",
            // "scenario2",
            // "scenario3",
            // "scenario4",
            // "scenario5",
            // "Scenario7",
            // "Scenario8",
            // "scenariohurlde",
            // "30037",
            // "test100",
            // "test55",
            // "test89",
            // "test99",
            // "1testavail1",
            // "testderived",
            // "testimport1",
            // "latest2",
            // "matt11"

            "chimneys",
             "stephenshotel",
            "gatewaycarvanpark",
            "printworks",                     
            "patricks", 
            "castlelodge", 
             "waves",
            "haveli",                   
             "brooklyn",
             "leonardos",                      
             "moortown",
             "corncroft", 
             "guards",
             "wrens",                          
              "lincoln",                               
             "windermerebout",                 
              "gardeners",     
              "meadowcroft",                  
            "tollgate",         
              "heywood",                  
              "butlers",
              "boundary",                     
             "blacklion",                      
             "glenville",                      
             "brathay",                        
             "amberley",                       
             "eltermere",                      
             "blossoms",                       
             "hundred",                        
             "craigadam",                      
             "mumbles",                    
             "savoy",                          
            "sommerin",                       
            "greathouse",                     
            "stmellons",                      
            "kingshead", 
            "boxmoor",                        
            "marlborough",                    
            "muckrach",                       
            "port",                           
            "batemans",                       
             "podworks",                       
             "drakes",                         
            "claremont",                      
            "blacksmith",                     
            "tunnel",                 
            "caecourt",                       
            "neeld",                          
            "brookside",                      
             "sandringham1",               
            "manor",                          
            "mllodge",                        
            "scourie",                        
            "cabbage",                        
            "barbican",                       
            "thameside",                      
            "whitehart",                      
            "jester",                         
            "swan",                           
            "sheldon",                        
            "combe",                          
            "rafters",                        
            "johnshouse",                     
            "whodathoughtit",                 
            "ashburnham",                    
            "abbey",
            "globe",                          
            "falcon",                         
            "stonehouse",                     
             "whitelodge",      
            "plashafod",                      
            "grasmere",                       
            "willington",                     
            "gresham",                        
            "woodridge",                      
            "crescent",                       
            "heathfield",                     
            "lagaffe",                        
            "saucy",                          
            "holland",                        
            "malthouse",                      
            "blackaddie",                     
            "sligachan",                      
            "railway",                        
            "craigdarroch",                   
            "black",                          
            "atlanta",                        
            "themanor",                       
            "lochness",                       
            "kingsland",                      
            "waterhouse",                     
            "blacksmithsclayworth",           
            "ducksinn",                       
            "mowbray",                        
            "marineparade",                   
            "oakwood",                        
            "crugglas",                       
            "davron",    
            "londonstar",
            "glenmorangie",                   
            "coesfaenspa",                    
            "frasers",                        
            "afonrhaiadr",                    
            "kemps",                          
            "blueinn",                        
            "kildrummy",                      
            "gatwickbelmont",                 
            "angel",                          
            "buckhouse",                      
            "horse",                          
            "gales",                          
            "greendragon",          
            "kilns",                          
            "whiterock",                      
            "grangeboutiquehotel-cumbria",    
            "llysmeddyg",                     
            "oxfordtownhouse",                
            "parklands",                      
            "glenarroch",                     
            "hoteldevie",                     
            "georgehotel",                    
            "moorsinn575858",                 
            "simonstonehall",                 
            "pendragon",                      
            "connaughthouse",                 
            "ravenstone575864",               
            "westgrange",                     
            "kingsheadusk575866",             
            "picture",                        
            "saltydog",                       
            "castlelodgetredegar",            
            "hortongrange",                   
            "camdenenterprise",               
            "thedorset",                      
            "hlshotel-cardiff",               
            "hlszonal",                       
            // "greystreethotel",            
             "newnorthumbriahotel",          
            "bedingfeld-swaffham",            
            "panoramahotel",                  
            "porterhousegrill",               
            "atlantichouse-bude",             
            "stargazy",                       
            "fencegate",             
            "theslate-bangor",                
            "coachguesthouse",                
            "rothaymanorhotel",               
            "hotelrelish",                    
            "thecrown-newtonstewart",         
            "coastalparkhotel-lanelli",       
            "yorkhouse-wakefield",            
            "devonshire",                     
            "rhodes-london",                  
            "paradise-gambia",                
            "theconcorde",                    
            "theshipinn-elie",                
            "thebridgeinn-ratho",             
            "strawberrybank-coventry",        
            "cambrian",                       
            "manorcourt-bridlington",         
            "churston-ferrers",               
            "manorinn-galmpton",              
            "airporttavern-bristol",          
            "worshipfarm",                    
            "lletycynin",                     
            "crownlodge-harlech",             
            "goldings-norfolk",               
            "wynnstayarms-wrexham",
            "thesaracensheadinn",             
            "axeandcompass-leightonbuzzard",  
            "hoteluna",                       
            "tyncellarbarns-porttalbot",      
            "roseandcrown-porthcawl",         
            "twelveknights-porttalbot",       
            "bullhotel-anglesey",
            "doveyinn-aberdovey",             
            "penheligarmshotel-aberdovey",    
            "whitelionroyalhotel-bala",       
            "lordnelson-milfordhaven",        
            "savoycountryinn-carmarthen",     
            "thegrovehotel-pembs",            
            "lambandflaginn-abergavenny",     
            "plasderwen-abergavenny",         
            "punchhouse-monmouth",            
            "wellington-brecon",              
            "maltsters-cardiff",              
            "monachty-aberaeron",             
            "shipinn-cardigan",               
            "thehotelpenwig-newquay",         
            "thomasarms-llanelli",            
            "ferryroyal-stromness",           
            "theoldvicarage",                 
            "victory-mersea",                 
            "saffron-hotel",                  
            "theshore",                       
            "hbthegeorge",                    
            "hurtarms-ambergate",             
            "highwayinn-burford",             
            "aylestone-hereford",             
            "strettonhouse-stratford",        
            "roseandcrown-elham",             
            "oldguesthouse-wiltshire",        
            "bedlinoginn",                    
            "grenfellarms-maidenhead",        
            "64cathedralroad-cardiff",        
            "gosforthhallhotel-cumbria",      
            "cadmorelakeside-tenbury",        
            "birleyarms-preston",             
            "galaxie-oxford",                 
            "theglastonburyhotel-eastbourne", 
            "hammethouse-pembrokeshire",      
            "wheelhouse-cornwall",            
            "bayhorse-hereford",              
            "elmhotel-retford",               
            "craigynoscastle-swansea",        
            "Henrys-oxford",                  
            "kegworthhouse-derbyshire",       
            "horseandjockeyinn-knighton",     
            "thewainstoneshotel",             
            "lovesgrove-pembroke",            
            "thenewswanhotel-swansea",        
            "hbthemitre",                     
            "threekingsstudio-chester",       
            "hbhorseandfarrier",              
            "oroccopierhotel",                
            "raven-shropshire",               
            "therisingsuninn-bath",           
            "thestaggerinn-furness",          
            "thetownhouse-cumbria",           
            "theduneshotel-cumbria",          
            "orletoninn-shropshire",          
            "ynysmeudwy-swansea",             
            "thechurchillarms-campden",       
            "harryshotel-aberystwyth",        
            "thefoxcrawley-winchester",       
            "churchfarmlodge-northampton",    
            "stokelodge-dartmouth",           
            "highleveldemo",                  
            "countylodge-carnforth",          
            "moretonparkhotel-wrexham",       
            "thehoublonarms-lincolnshire",    
            "thepytchleyinn-northampton",     
            "hotelconrah-aberystwyth",        
            "Canaston-Pembrokshire",          
            "davidwilson-newcastle",          
            "brigandsinn-mallwyd",            
            "lils-york",                      
            "burtoncourt-herefordshire",      
            "redlodge-norfolk",               
            "goldenfleece-york",              
            "sevenstars-dartmouth",           
            "george-dolgellau",               
            "resolutionhotel-whitby",         
            "greyhound-cromford",             
            "crookson-glasgow",               
            "newinn-clapham",                 
            "roseandcrown-carmarthen",        
            "Gazelle-MenaiBridge",            
            "george-longbridge",              
            "bunk-Thatcham",                  
            "Hawk-Andover",                   
            "Gordleton-Lymington",            
            "Navigator-Southampton",          
            "PeatSpade-Stockbridge",          
            "RunningHorse-Winchester",        
            "StationHouse-Haslemere",         
            "SwanInn-Godalming",              
            "WhiteHart-WestSussex",           
            "WhiteHartH-Hampshire",           
            "ThomasLord-Meon",                
            "WinningPost-Windsor",            
            "lochmelfort-Argyll",             
            "Seacroft-Holyhead",              
            "frocester",                      
            "Victoria-Walshaw",               
            "wheatsheaf-corbridge",           
            "OldVicarage-Somerset",           
            "WeightCareCentre",               
            "lifeboat-staustell",             
            "gatwickturret-Surrey",           
            "angel-surrey",                   
            "Cricket-SALISBURY",              
            "Elm-Pembrokshire",               
            "Beadnell-Northumberland",        
            "swan-whalley",                   
            "redlion-hawkshead",              
            "Sun-Windermere",                 
            "vale-Carlton",                   
            "george-Nunney",                  
            "Porlock-Weir",                   
            "Grange-Hotel-ltd-Somerset",      
            "george-banbury",                 
            "printworks-Liverpool",           
            "elan-Rhayader",                  
            "blackbull-moffat",               
            "Rowbarge-WOKING",                
            "blackhat-Ilkley",                
            "cookie-Alnwick",                 
            "Shepherds-cleator",              
            "Locanda-Porlock",                
            "merrie-Compton",                 
            "cliff-belle",                    
            "picton-Llanddowror",             
            "Llety-Llandre",                  
            "Castle-Spofforth",               
            "Windsor-Merthyr",                
            "Chavasse",                       
            "Littleover-Derby",               
            "blion-Lampeter",                 
            "Pine-Pitlochry",                 
            "talbot-Ceredigion",              
            "bells-Ramsbury",                 
            "royal-Brynmawr",                 
            "Fox-Wroughton",                  
            "CrossKeysHotel-Knutsford",       
            "Swan-Talbot-Wetherby",           
            "Plas-bala",                      
            "blacklion-cardigan",             
            "standing-Stromness",             
            "YorkStreet-TBLB",                
            "raeburn-Edinburgh",              
            "royal-Coldfield",                
            "saintpaul-Birmingham",           
            "Clifton-Oxfordshire",            
            "oldship-Hackney",                
            "bull-devonshire",                
            "Harcourt-Oxford",                
            "white-Hampshire",                
            "Oxnoble-Manchester",             
            "Legh-Macclesfield",              
            "Feathered-Oxfordshire",          
            "Cuttlebridge-Birmingham",        
            "oak-Hockley",                    
            "Hostelrie-Goodrich",             
            "Distillery-London",              
            "Swan-Fittleworth",               
            "Duke-Wellington",                
            "Bateman-Leominster",             
            "dinas-liverpool",                
            "Tredegar-Gwent",                 
            "EI-Midland-Leeds",               
            "lornadoone",                     
            "Alvanley-Cheshire",              
            "Marquis-Dover",                  
            "Langass-Lodge",                  
            "Hamersay-House",                 
            "molesworth-wadebridge",          
            "bess-merth",                     
            "roast-Painscastle",              
            "hebridean-harrapool",            
             "Maesmawr-Powys",       
             "oldbarn-Warrington",         
             "bridge-lodsworth",              
             "crab-Chichester",
             "Wyndham-coleford",      
             "Crumplebury-Worcester",            
             "Cathedral-Canterbury",     
            "Avon-Causeway-Wadworth",         
            "Bartons-Mill-Wadworth",          
            "Beckford-Inn-Wadworth",          
            "Bird-in-Hand-Wadworth",          
            "Black-Swan-Wadworth",            
            "Cherry-Tree-Wadworth",           
            "Colesbourne-Inn-Wadworth",       
            "Cotswold-Gateway-Wadworth",      
            "Crown-Inn-Wadworth",             
            "Dean-Park-Inn-Wadworth",         
            "Falkland-Arms-Wadworth",         
            "George-Hotel-Wadworth",          
            "George-Inn-Wadworth",            
            "George-Inn-Bath-Wadworth",       
            "Green-Dragon-Wadworth",          
            "Hadley-Bowling-Green-Wadworth",  
            "High-Corner-Wadworth",           
            "Inn-in-the-Park-Wadworth",       
            "Kings-Arms-Hotel-Wadworth",      
            "Old-Bell-Wadworth",              
            "Red-Lion-Wadworth",              
            "Stag-Wadworth",                  
            "Talbot-Hotel-Wadworth",          
            "White-Hart-Wadworth",            
            "Balavoulin-Crabbitmidge",        
            "station-whitby",                 
            "theeagletavern",                 
            "Hoste-CityPubGroup",             
            "crown-kemerton",                 
            "old-hall-coventry",              
            "greatnorth-malhotra",            
            "Maytime-burford",                
            "Swan-Butcombe",                  
            "gellihaf",                       
            "HorseGroom-Butcombe",            
            "uig_skye",                       
            "The-Old-Hall-Inn-Whitehough",    
            "flying-bull-liss"
        };

        // final String[] slugs = {
        //     "cardiff",
        //     "london",
        //     "glasgow"
        // };

        final String[] availCheckInDate = {
            // "28/05/2020"
            // "29/05/2020",
            //  "30/05/2020",
            // "31/05/2020"
            // StageUAT
            // "01/11/2020"
            // "02/11/2020"
            // "03/11/2020"
            // "04/11/2020"
            "05/11/2020"
            // "15/11/2020"
            // "01/12/2020"
            // "15/12/2020"
            // "14/01/2021"
            // "30/01/2021"
            // "14/02/2021"
            // "15/02/2021",
            // "16/02/2021",
            // "01/03/2021",
            // "02/03/2021"
            // "07/03/2021"
            // "15/03/2021"
            // "01/04/2021"
        };

        final String[] availCheckOutDate = {
            // "29/05/2020"
            // "30/05/2020",
            // "31/05/2020",
            //  "01/06/2020"
            // StageUAT
            // "02/11/2020"
            // "03/11/2020"
            // "04/11/2020"
            // "05/11/2020"
            "06/11/2020"
            // "16/11/2020"
            // "02/12/2020"
            // "16/12/2020"
            // "15/01/2021"
            // "31/01/2021"
            // "15/02/2021"
            // "16/02/2021",
            // "17/02/2021",
            // "02/03/2021",
            // "03/03/2021"
            // "08/03/2021"
            // "16/03/2021"
            // "02/04/2021"
        };

        final String[] dashboardDate = {
            // "2020-05-28"
            // "2020-05-29",
            //  "2020-05-30",
            // "2020-05-31"
            // StageUAT
            // "2020-11-01"
            // "2020-11-02"
            // "2020-11-03"
            // "2020-11-04"
            "2020-11-05"
            // "2020-11-15"
            // "2020-12-01"
            // "2020-12-15"
            // "2021-01-14"
            // "2021-01-30"
            // "2021-02-14"
            // "2021-02-15",
            // "2021-02-16",
            // "2021-03-01",
            // "2021-03-02"
            // "2021-03-07"
            // "2021-03-15"
            // "2021-04-01"
        };

        // ADD DATE TO URL ?date=2020-05-21
      for(int i=0; i<slugs.length;i++) {
            for(int date=0; date<availCheckInDate.length; date++) {
                // Navigate to a hotels dashboard page
                driver.get(environment + "/hotels/" + slugs[i] + "?date=" + dashboardDate[date]);
                //dashboardPage =  new DashboardPage(driver, slugs[i], hotelNames[i]);
                //dashboardPage.validatePage(environment);
                List<WebElement> tables = driver.findElements(By.xpath("/html/body/div[1]/div/div[2]//div[@class='box table']"));
                //WebElement eTable = tables.get(tables.size()-1);
                List<WebElement> roomTypeAvailabilityTableRows = driver.findElements(By.cssSelector("body > div.body > div > div.col2 > div:nth-child(" + tables.size() + ") > table > tbody tr"));
                System.out.println("Number of room types: " +roomTypeAvailabilityTableRows.size());
                
                // Define array here
                ArrayList<Integer> dashboardAvailability = new ArrayList<>(roomTypeAvailabilityTableRows.size()-1);
                for(int j=2; j<roomTypeAvailabilityTableRows.size()+1; j++) {
                    // Add a dates availability to an array
                    dashboardAvailability.add(j-2, Integer.valueOf(driver.findElement(By.cssSelector("body > div.body > div > div.col2 > div:nth-child(" + tables.size() + ") > table > tbody > tr:nth-child(" + j + ") > td:nth-child(2)")).getAttribute("innerHTML")));
                }

                // driver.get(environment + "/hotels/" + slugs[i] + "/room-types");
                // List<WebElement> rooms = driver.findElements(By.xpath("/html/body/div[2]/div/div[1]//div[@class='box model']"));
                // List<String> roomsTitles = new ArrayList<String>();
                // for(int r=1; r<rooms.size()+1; r++) {
                //     rooms.get(r-1).findElement(By.xpath("/html/body/div[2]/div/div[1]/div[" + r + "]/h2/a")).click();
                //     // Add a dates availability to an array
                //     roomsTitles.add(driver.findElement(By.cssSelector("#title")).getText());
                //     driver.get(environment + "/hotels/" + slugs[i] + "/room-types");
                //     System.out.println("Title: " + roomsTitles.get(r-1));
                // }
                
                // Go to availability page and search for date
                driver.get(environment + "/hotels/" + slugs[i] + "/availability");
                driver.findElement(By.cssSelector("#dateFrom")).clear();
                driver.findElement(By.cssSelector("#dateFrom")).sendKeys(availCheckInDate[date]);
                driver.findElement(By.cssSelector("#dateTo")).clear();
                driver.findElement(By.cssSelector("#dateTo")).sendKeys(availCheckOutDate[date]);
                driver.findElement(By.cssSelector(".secondary > span:nth-child(1)")).click();
                genericMethods.waitForPageLoad(driver);
                System.out.println();
                System.out.println("Slug: " + slugs[i] + " Date: " + availCheckInDate[date]);
                // Get rooms for date
                List<WebElement> rooms2 = driver.findElements(By.xpath("/html/body/main/section/div[1]/div[1]/div/div//div[@class='room']"));
                int roomSelected = 0;
                for(int k=0; k<dashboardAvailability.size(); k++) {
                    if(dashboardAvailability.get(k) != 0) {
                        WebElement e =driver.findElement(By.xpath("//*[@id=\"" + rooms2.get(roomSelected).getAttribute("id") + "\"]"));
                        WebElement e1 = e.findElement(By.xpath(".//select[@class='small number-of-rooms']"));
                        List<WebElement> roomsAvailable = e1.findElements(By.xpath(".//option"));
                        System.out.println("Assert availability: " + String.valueOf(roomsAvailable.size()) + " " + dashboardAvailability.get(k));
                        
                        //Assert.assertEquals(e.findElement(By.cssSelector("div:nth-child(1) > div:nth-child(1) > h3:nth-child(1)")).getAttribute("innerHTML"), rooms.get(k));
                        Assert.assertEquals(String.valueOf(roomsAvailable.size()), String.valueOf(dashboardAvailability.get(k)));
                        roomSelected = roomSelected +1;
                    }
                }
            }
       }
    }

    public void startOfTestPageRouting(final String user, String testDataLoginPath) {
        if (user.equals("staff-admin") || user.equals("staff-support") || user.equals("staff-manager") || user.equals("staff-sales")) {
            hotelsPage = login.hotelsPage;
            hotelsPage.validatePage();
        }
    }

    // @DataProvider(name="SlugProvider", parallel=true)
    // public Object[] getDataFromDataprovider(){
    // return new Object[] 
    // 	{
    //         "cardiff",
    //         "london",
    //          "glasgow" 
    //     };
    // }
}