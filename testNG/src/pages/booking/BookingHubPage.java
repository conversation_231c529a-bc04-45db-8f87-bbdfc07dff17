import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.testng.Assert;

import java.io.FileReader;
import java.io.IOException;

/**
 * Page object for the Booking Hub page
 */
public class BookingHubPage extends BasePage {
    // Page elements
    public String pageUrl = "/booking/hub";
    
    // Selectors
    private By paymentStatusHeader = By.cssSelector(".payment-status-header");
    private By expandBookingButton = By.cssSelector(".expand-booking-button");
    private By reservationDetails = By.cssSelector(".reservation-details");
    private By paymentAmount = By.cssSelector(".payment-amount");
    private By balanceAmount = By.cssSelector(".balance-amount");
    private By guestDetailsSection = By.cssSelector(".guest-details-section");
    private By bookingDates = By.cssSelector(".booking-dates");
    private By creditCardDetails = By.cssSelector(".credit-card-details");
    private By bookedDate = By.cssSelector(".booked-date");
    private By bookingReference = By.cssSelector(".booking-reference");
    
    /**
     * Constructor
     */
    public BookingHubPage(WebDriver driver, Report report, String environment) {
        super(driver, report, environment);
    }
    
    /**
     * Assert that the payment status is as expected
     */
    public BookingHubPage assertPaymentStatus(String expectedStatus) {
        report.appendExport("INFO: Asserting payment status is: " + expectedStatus);
        WebElement status = wait.until(ExpectedConditions.visibilityOfElementLocated(paymentStatusHeader));
        String statusText = status.getText().toLowerCase();
        Assert.assertTrue(statusText.contains(expectedStatus.toLowerCase()), 
                         "Expected payment status to be: " + expectedStatus + 
                         ", but got: " + statusText);
        return this;
    }
    
    /**
     * Click the Expand Booking button
     */
    public BookingHubPage clickExpandBookingButton() {
        report.appendExport("INFO: Clicking Expand Booking button");
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(expandBookingButton));
        button.click();
        return this;
    }
    
    /**
     * Assert that the reservation details contain the expected room type and amount
     */
    public BookingHubPage assertReservationDetails(String expectedRoomType, String expectedAmount) {
        report.appendExport("INFO: Asserting reservation details contain room type: " + expectedRoomType + 
                           " and amount: " + expectedAmount);
        WebElement details = wait.until(ExpectedConditions.visibilityOfElementLocated(reservationDetails));
        String detailsText = details.getText();
        Assert.assertTrue(detailsText.contains(expectedRoomType), 
                         "Expected reservation details to contain room type: " + expectedRoomType + 
                         ", but got: " + detailsText);
        Assert.assertTrue(detailsText.contains(expectedAmount), 
                         "Expected reservation details to contain amount: " + expectedAmount + 
                         ", but got: " + detailsText);
        return this;
    }
    
    /**
     * Assert that the payment amount is as expected
     */
    public BookingHubPage assertPaymentAmount(double expectedAmount) {
        report.appendExport("INFO: Asserting payment amount is: " + expectedAmount);
        WebElement amount = wait.until(ExpectedConditions.visibilityOfElementLocated(paymentAmount));
        String amountText = amount.getText().replaceAll("[^0-9.]", "");
        double actualAmount = Double.parseDouble(amountText);
        Assert.assertEquals(actualAmount, expectedAmount, 0.01, 
                           "Expected payment amount to be: " + expectedAmount + 
                           ", but got: " + actualAmount);
        return this;
    }
    
    /**
     * Assert that the balance amount is as expected
     */
    public BookingHubPage assertBalanceAmount(double expectedAmount) {
        report.appendExport("INFO: Asserting balance amount is: " + expectedAmount);
        WebElement amount = wait.until(ExpectedConditions.visibilityOfElementLocated(balanceAmount));
        String amountText = amount.getText().replaceAll("[^0-9.]", "");
        double actualAmount = Double.parseDouble(amountText);
        Assert.assertEquals(actualAmount, expectedAmount, 0.01, 
                           "Expected balance amount to be: " + expectedAmount + 
                           ", but got: " + actualAmount);
        return this;
    }
    
    /**
     * Assert that the guest details match the expected details from a JSON file
     */
    public BookingHubPage assertGuestDetails(String filePath) {
        report.appendExport("INFO: Asserting guest details match file: " + filePath);
        
        try {
            JSONParser parser = new JSONParser();
            JSONObject guestDetails = (JSONObject) parser.parse(new FileReader(filePath));
            
            String firstName = (String) guestDetails.get("firstName");
            String lastName = (String) guestDetails.get("lastName");
            String email = (String) guestDetails.get("email");
            
            WebElement details = wait.until(ExpectedConditions.visibilityOfElementLocated(guestDetailsSection));
            String detailsText = details.getText();
            
            Assert.assertTrue(detailsText.contains(firstName), 
                             "Expected guest details to contain first name: " + firstName + 
                             ", but got: " + detailsText);
            Assert.assertTrue(detailsText.contains(lastName), 
                             "Expected guest details to contain last name: " + lastName + 
                             ", but got: " + detailsText);
            Assert.assertTrue(detailsText.contains(email), 
                             "Expected guest details to contain email: " + email + 
                             ", but got: " + detailsText);
        } catch (IOException | ParseException e) {
            report.appendExport("ERROR: Failed to read guest details from file: " + e.getMessage());
        }
        
        return this;
    }
    
    /**
     * Assert that the booking dates match the expected dates
     */
    public BookingHubPage assertBookingDates(String checkInDate, String checkOutDate) {
        report.appendExport("INFO: Asserting booking dates are check-in: " + checkInDate + 
                           " and check-out: " + checkOutDate);
        WebElement dates = wait.until(ExpectedConditions.visibilityOfElementLocated(bookingDates));
        String datesText = dates.getText();
        Assert.assertTrue(datesText.contains(checkInDate), 
                         "Expected booking dates to contain check-in date: " + checkInDate + 
                         ", but got: " + datesText);
        Assert.assertTrue(datesText.contains(checkOutDate), 
                         "Expected booking dates to contain check-out date: " + checkOutDate + 
                         ", but got: " + datesText);
        return this;
    }
    
    /**
     * Get the booking reference
     */
    public String getBookingReference() {
        report.appendExport("INFO: Getting booking reference");
        WebElement reference = wait.until(ExpectedConditions.visibilityOfElementLocated(bookingReference));
        String referenceText = reference.getText().trim();
        report.appendExport("INFO: Booking reference is: " + referenceText);
        return referenceText;
    }
}
