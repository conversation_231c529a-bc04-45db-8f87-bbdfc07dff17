const fs = require('fs');

try {
  // Read the current package.json
  const packageJsonPath = '/var/www/test-automation/package.json';
  const packageJson = fs.readFileSync(packageJsonPath, 'utf8');
  
  // Create a backup
  fs.writeFileSync(`${packageJsonPath}.bak`, packageJson);
  console.log('✅ Created backup at package.json.bak');
  
  // Fix common JSON syntax errors
  let fixedJson = packageJson
    // Fix missing commas between script entries
    .replace(/"\s*\n\s*"/g, '",\n  "')
    // Fix trailing commas in arrays and objects
    .replace(/,(\s*[\]}])/g, '$1')
    // Fix missing quotes around property names
    .replace(/([{,]\s*)([a-zA-Z0-9_]+)(\s*:)/g, '$1"$2"$3');
  
  // Write the fixed JSON
  fs.writeFileSync(packageJsonPath, fixedJson);
  console.log('✅ Wrote fixed package.json');
  
  // Validate the fixed JSON
  try {
    JSON.parse(fixedJson);
    console.log('✅ Fixed package.json is valid JSON');
  } catch (parseError) {
    console.error('❌ Fixed JSON is still invalid:', parseError.message);
    console.log('Please manually edit package.json to fix the remaining issues');
  }
} catch (error) {
  console.error('❌ Error:', error.message);
}