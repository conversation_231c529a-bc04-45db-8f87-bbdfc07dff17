#!/bin/bash

# This script updates all Cypress test files in the booking-engine directory
# to use the new robust reseeding command instead of direct cy.request calls

# Find all test files in the booking-engine directory
TEST_FILES=$(find cypress/e2e/booking-engine -name "*.cy.js")

# Counter for modified files
MODIFIED_COUNT=0

# Process each file
for file in $TEST_FILES; do
  echo "Processing $file..."
  
  # Check if the file contains a direct reseeding request
  if grep -q "cy.request.*reseedHotel" "$file"; then
    # Create a backup of the file
    cp "$file" "${file}.bak"
    
    # Replace direct reseeding requests with the robust command
    # This handles the common pattern with hotelSlug variable
    sed -i 's/cy.request(`automation\/tests\/reseedHotel\/${hotelSlug}`)/cy.reseedHotel(hotelSlug)/g' "$file"
    sed -i 's/cy.request(`\/automation\/tests\/reseedHotel\/${hotelSlug}`)/cy.reseedHotel(hotelSlug)/g' "$file"
    
    # Handle the pattern with direct hotel ID
    sed -i -E 's/cy.request\(["'\'']automation\/tests\/reseedHotel\/([^"'\'']+)["'\'']\)/cy.reseedHotel("\1")/g' "$file"
    sed -i -E 's/cy.request\(["'\'']\/automation\/tests\/reseedHotel\/([^"'\'']+)["'\'']\)/cy.reseedHotel("\1")/g' "$file"
    
    # Handle the pattern with template literals and direct hotel ID
    sed -i -E 's/cy.request\(`automation\/tests\/reseedHotel\/([^`]+)`\)/cy.reseedHotel("\1")/g' "$file"
    sed -i -E 's/cy.request\(`\/automation\/tests\/reseedHotel\/([^`]+)`\)/cy.reseedHotel("\1")/g' "$file"
    
    # Check if the file was modified
    if ! diff -q "$file" "${file}.bak" > /dev/null; then
      echo "  Modified: $file"
      MODIFIED_COUNT=$((MODIFIED_COUNT + 1))
    else
      echo "  No changes needed in $file"
      # Restore the backup if no changes were made
      mv "${file}.bak" "$file"
    fi
  else
    echo "  No reseeding requests found in $file"
  fi
done

echo "Completed! Modified $MODIFIED_COUNT files."
