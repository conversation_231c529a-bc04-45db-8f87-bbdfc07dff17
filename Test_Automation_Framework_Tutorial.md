# Test Automation Framework Tutorial

## Introduction

This tutorial provides a comprehensive guide to the test automation framework used for testing the hotel booking system. The framework is built using Java with Selenium WebDriver and follows the Page Object Model (POM) design pattern. This tutorial will help you understand the architecture, key components, and how to create and run tests.

## Table of Contents

1. [Page Object Foundation (BasePage)](#page-object-foundation-basepage)
2. [Booking Page Objects](#booking-page-objects)
3. [Test Case Implementation](#test-case-implementation)
4. [Test Data Management](#test-data-management)
5. [Generic Methods and Utilities](#generic-methods-and-utilities)
6. [Test Execution](#test-execution)
7. [Best Practices](#best-practices)

## Page Object Foundation (BasePage)

The `BasePage` class serves as the foundation for all page objects in the framework. It encapsulates common functionality that all pages share, such as waiting for elements, navigating, and interacting with web elements.

```java
public class BasePage {
    protected WebDriver driver;
    protected WebDriverWait wait;
    
    public BasePage(WebDriver driver) {
        this.driver = driver;
        this.wait = new WebDriverWait(driver, Duration.ofSeconds(10));
    }
    
    protected WebElement waitForElementVisible(By locator) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(locator));
    }
    
    protected WebElement waitForElementClickable(By locator) {
        return wait.until(ExpectedConditions.elementToBeClickable(locator));
    }
    
    protected void click(By locator) {
        waitForElementClickable(locator).click();
    }
    
    protected void sendKeys(By locator, String text) {
        waitForElementVisible(locator).sendKeys(text);
    }
    
    protected String getText(By locator) {
        return waitForElementVisible(locator).getText();
    }
    
    protected boolean isElementDisplayed(By locator) {
        try {
            return driver.findElement(locator).isDisplayed();
        } catch (NoSuchElementException e) {
            return false;
        }
    }
}
```

## Booking Page Objects

The framework includes several page objects that represent different pages in the booking flow:

### BookingHubPage

The `BookingHubPage` represents the main booking hub where users can start the booking process.

```java
public class BookingHubPage extends BasePage {
    private By bookNowButton = By.id("book-now-button");
    private By loginButton = By.id("login-button");
    
    public BookingHubPage(WebDriver driver) {
        super(driver);
    }
    
    public CalendarPage clickBookNow() {
        click(bookNowButton);
        return new CalendarPage(driver);
    }
    
    public void clickLogin() {
        click(loginButton);
    }
}
```

### CalendarPage

The `CalendarPage` allows users to select check-in and check-out dates.

```java
public class CalendarPage extends BasePage {
    private By checkInDate = By.cssSelector(".check-in-date");
    private By checkOutDate = By.cssSelector(".check-out-date");
    private By continueButton = By.id("continue-button");
    
    public CalendarPage(WebDriver driver) {
        super(driver);
    }
    
    public void selectDates(String checkIn, String checkOut) {
        // Implementation to select dates
    }
    
    public YourStayPage clickContinue() {
        click(continueButton);
        return new YourStayPage(driver);
    }
}
```

### YourStayPage, GuestDetailsPage, ExtrasPage, PayPage

Similar page objects exist for other steps in the booking flow, each extending the `BasePage` class and implementing page-specific functionality.

## Test Case Implementation

Test cases are implemented using the page objects to interact with the application. Here's an example of a simple booking test:

```java
public class SimpleBookingTest {
    private WebDriver driver;
    private BookingHubPage bookingHubPage;
    
    @BeforeMethod
    public void setUp() {
        System.setProperty("webdriver.chrome.driver", "path/to/chromedriver");
        driver = new ChromeDriver();
        driver.manage().window().maximize();
        driver.get("https://hotel-booking-url.com");
        bookingHubPage = new BookingHubPage(driver);
    }
    
    @Test
    public void testSimpleBooking() {
        // Navigate through booking flow
        CalendarPage calendarPage = bookingHubPage.clickBookNow();
        calendarPage.selectDates("2023-06-01", "2023-06-05");
        
        YourStayPage yourStayPage = calendarPage.clickContinue();
        yourStayPage.selectRoomType("Deluxe");
        
        GuestDetailsPage guestDetailsPage = yourStayPage.clickContinue();
        guestDetailsPage.enterGuestDetails("John", "Doe", "<EMAIL>", "1234567890");
        
        ExtrasPage extrasPage = guestDetailsPage.clickContinue();
        
        PayPage payPage = extrasPage.clickContinue();
        payPage.enterCardDetails("John Doe", "****************", "12", "2025", "123");
        
        BookingsPage bookingsPage = payPage.clickPayNow();
        
        // Verify booking confirmation
        Assert.assertTrue(bookingsPage.isBookingConfirmed());
    }
    
    @AfterMethod
    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }
}
```

## Test Data Management

Test data is managed using JSON files to store test data such as guest details and card information. This approach allows for easy maintenance and updates to test data without changing the test code.

```json
// guestDetails.json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "1234567890"
}

// cardDetails.json
{
  "cardholderName": "John Doe",
  "cardNumber": "****************",
  "expiryMonth": "12",
  "expiryYear": "2025",
  "securityCode": "123"
}
```

## Generic Methods and Utilities

The framework includes a `GenericMethods` class that provides utility methods for common operations:

```java
public class GenericMethods {
    private WebDriver driver;
    
    public GenericMethods(WebDriver driver) {
        this.driver = driver;
    }
    
    public void switchToIframe(String frameId) {
        driver.switchTo().frame(frameId);
    }
    
    public void switchToDefaultContent() {
        driver.switchTo().defaultContent();
    }
    
    public void scrollToElement(WebElement element) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", element);
    }
    
    public void waitForPageLoad() {
        new WebDriverWait(driver, Duration.ofSeconds(30)).until(
            webDriver -> ((JavascriptExecutor) webDriver).executeScript("return document.readyState").equals("complete"));
    }
    
    public String readJsonFile(String filePath) {
        // Implementation to read JSON file
        return "";
    }
}
```

## Test Execution

Tests can be executed using TestNG XML files that define test suites and test cases to run:

```xml
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Booking Test Suite">
    <test name="Simple Booking Test">
        <classes>
            <class name="tests.booking.SimpleBookingTest"/>
        </classes>
    </test>
</suite>
```

## Best Practices

1. **Maintain Page Objects**: Keep page objects up-to-date with changes in the application UI.
2. **Use Explicit Waits**: Always use explicit waits instead of implicit waits or Thread.sleep().
3. **Handle Iframes Properly**: Use the GenericMethods utility to handle iframes correctly.
4. **Separate Test Data**: Keep test data separate from test code using JSON files.
5. **Implement Proper Assertions**: Use meaningful assertions to verify test results.
6. **Handle Exceptions**: Implement proper exception handling in tests.
7. **Clean Up Resources**: Always clean up resources in the tearDown method.
8. **Use Meaningful Test Names**: Give tests meaningful names that describe what they're testing.
9. **Keep Tests Independent**: Each test should be independent and not rely on other tests.
10. **Document Tests**: Add comments and documentation to explain complex test logic.

## Conclusion

This tutorial has provided an overview of the test automation framework for the hotel booking system. By following the Page Object Model pattern and best practices outlined here, you can create maintainable and reliable automated tests for the application.
