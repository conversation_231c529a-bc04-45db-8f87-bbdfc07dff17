# Cypress Test Reliability Guide

This document provides guidance on improving the reliability of Cypress tests in AWS and other remote environments, with a focus on handling common issues like timeouts and server errors.

## Table of Contents

1. [Common Reliability Issues](#common-reliability-issues)
2. [Timeout Settings](#timeout-settings)
3. [Error Handling](#error-handling)
4. [Environment Configuration](#environment-configuration)
5. [Reseeding Test Data](#reseeding-test-data)
6. [Running Tests in AWS](#running-tests-in-aws)
7. [Troubleshooting](#troubleshooting)

## Common Reliability Issues

When running Cypress tests in AWS or other remote environments, several common issues can affect test reliability:

1. **Network Latency**: Higher latency between the test runner and the application can cause timeouts
2. **Resource Constraints**: Limited CPU or memory resources can slow down test execution
3. **Server Errors**: Temporary server errors or unavailability of endpoints
4. **Test Data Inconsistency**: Issues with test data setup or reseeding
5. **Browser Compatibility**: Different behavior across browsers or browser versions

## Timeout Settings

Increasing timeout settings is one of the most effective ways to improve test reliability in remote environments.

### Global Timeout Settings

Update your `cypress.config.js` file with increased timeout values:

```javascript
module.exports = defineConfig({
  e2e: {
    // Increase default command timeout (default is 4000ms)
    defaultCommandTimeout: 180000,
    
    // Increase request timeout (default is 5000ms)
    requestTimeout: 180000,
    
    // Increase response timeout (default is 30000ms)
    responseTimeout: 180000,
    
    // Other settings...
  }
});
```

### Per-Command Timeout Settings

For specific commands that might take longer, you can set timeouts individually:

```javascript
// Wait longer for a specific element
cy.get('#slow-loading-element', { timeout: 60000 })

// Wait longer for a specific request
cy.request({
  url: '/api/slow-endpoint',
  timeout: 120000
})
```

## Error Handling

### Handling Status Code Errors

To prevent tests from failing due to non-2xx status codes, use the `failOnStatusCode: false` option:

```javascript
cy.request({
  url: '/api/endpoint',
  failOnStatusCode: false
})
```

This is particularly useful for reseeding endpoints that might return 404 or 500 errors but shouldn't necessarily fail the test.

### Handling Uncaught Exceptions

To prevent tests from failing due to uncaught exceptions in the application:

```javascript
// In cypress/support/e2e.js
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false prevents Cypress from failing the test
  return false
})
```

### Retry Logic

For flaky tests, consider adding retry logic:

```javascript
// In cypress.config.js
module.exports = defineConfig({
  e2e: {
    retries: {
      runMode: 2,      // Retry failed tests in CI
      openMode: 1      // Retry once in open mode
    }
  }
});
```

## Environment Configuration

### Environment-Specific Configuration

Create environment-specific configuration files in `cypress/env/`:

```json
// cypress/env/uat.json
{
    "baseUrl": "https://uat.example.com",
    "video": false,
    "env": {
        "staff_email": "<EMAIL>",
        "staff_password": "password123",
        "environment": "UAT"
    }
}
```

### Dynamic Configuration

Load the appropriate configuration based on the environment:

```javascript
// In cypress.config.js
setupNodeEvents(on, config) {
  const environmentName = config.env.environmentName || 'local'
  const environmentFilename = `./cypress/env/${environmentName}.json`
  const settings = require(environmentFilename)
  
  if (settings.baseUrl) {
    config.baseUrl = settings.baseUrl
  }
  if (settings.env) {
    config.env = {
      ...config.env,
      ...settings.env,
    }
  }
  return config
}
```

## Reseeding Test Data

### Handling Reseeding Errors

Modify reseeding requests to handle errors gracefully:

```javascript
before('seed hotel', () => {
  cy.request({
    url: `automation/tests/reseedHotel/${hotelSlug}`,
    failOnStatusCode: false,
    timeout: 180000
  })
  cy.clearCookies();
  cy.clearLocalStorage();
})
```

### Batch Updating Test Files

Create a script to update all test files with improved reseeding logic:

```bash
#!/bin/bash

# Find all test files
TEST_FILES=$(find cypress/e2e -name "*.cy.js")

# Process each file
for file in $TEST_FILES; do
  # Create a backup
  cp "$file" "${file}.bak"
  
  # Replace direct reseeding requests with more robust version
  sed -i 's/cy.request(`automation\/tests\/reseedHotel\/${hotelSlug}`)/cy.request({\n      url: `automation\/tests\/reseedHotel\/${hotelSlug}`,\n      failOnStatusCode: false,\n      timeout: 180000\n    })/g' "$file"
  
  # Check if the file was modified
  if ! diff -q "$file" "${file}.bak" > /dev/null; then
    echo "Modified: $file"
  else
    # Restore the backup if no changes were made
    mv "${file}.bak" "$file"
  fi
done
```

## Running Tests in AWS

### Headless Mode

Run tests in headless mode for better performance in CI/CD pipelines:

```bash
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

### Specific Browser

Specify a browser for testing:

```bash
npx cypress run --browser chrome --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

### Headed Mode (for Debugging)

Run tests in headed mode for debugging:

```bash
npx cypress run --headed --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

### Running Specific Tests

Run specific test suites to reduce execution time:

```bash
# Run a specific category of tests
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/B1 D0 guestNoLogin*/**/*.cy.js"

# Run a single test file
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/B1 D0 guestNoLoginDepositPartial/B1 D0 guestNoLoginDepositPartial.spec.cy.js"
```

## Troubleshooting

### Common Issues and Solutions

1. **Connection Timeout**
   - Increase timeout settings in cypress.config.js
   - Check network connectivity to the AWS environment
   - Verify the baseUrl in the environment config is correct

2. **Authentication Failures**
   - Verify credentials in the environment config
   - Check if the user account is active in the environment
   - Ensure the user has the necessary permissions

3. **Element Not Found Errors**
   - Update selectors if the UI has changed
   - Add explicit waits for elements that take longer to appear
   - Increase the defaultCommandTimeout

4. **Reseeding Failures**
   - Add `failOnStatusCode: false` to reseeding requests
   - Implement alternative reseeding methods
   - Consider using database snapshots or fixtures

### Debugging with Logs

Enable Cypress debug logs for troubleshooting:

```bash
DEBUG=cypress:* npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

### Checking Test Screenshots

Review screenshots of failed tests in the `cypress/screenshots` directory to identify issues.

## Conclusion

Improving the reliability of Cypress tests in AWS environments requires a combination of:

1. Increased timeout settings
2. Robust error handling
3. Environment-specific configuration
4. Reliable test data management
5. Appropriate test execution strategies

By implementing these recommendations, you can significantly improve the stability and reliability of your Cypress tests in AWS and other remote environments.
