# Cypress Test Automation Framework User Guide

## Introduction

This user guide provides comprehensive instructions for using the Cypress test automation framework to test the hotel booking system. It covers setup, test creation, execution, and best practices.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Framework Architecture](#framework-architecture)
3. [Creating Tests](#creating-tests)
4. [Page Object Model](#page-object-model)
5. [Test Data Management](#test-data-management)
6. [Running Tests](#running-tests)
7. [Payment Testing](#payment-testing)
8. [If<PERSON><PERSON> Handling](#iframe-handling)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm (v6 or later)
- Git

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/test-automation.git
   cd test-automation
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp cypress.env.example.json cypress.env.json
   # Edit cypress.env.json with your environment-specific values
   ```

## Framework Architecture

The framework follows a structured architecture:

```
cypress/
├── e2e/                  # Test files organized by feature
│   ├── 001 Default/      # Basic tests like login
│   ├── 004 booking-management/ # Booking management tests
│   ├── 010 payment-processing/ # Payment processing tests
│   └── ...
├── fixtures/             # Test data
├── pages/                # Simple page objects
└── support/              # Support files
    ├── commands/         # Custom Cypress commands
    ├── forms/            # Form interaction helpers
    ├── functions/        # Utility functions
    └── pageObjectModel/  # Advanced page objects
```

## Creating Tests

### Test Structure

Tests follow a standard structure:

```javascript
import accounts from '../../fixtures/accounts';
import TestFilters from '../../support/filterTests';

TestFilters(['P2Sanity'], () => {
  describe('Feature: Login', () => {
    beforeEach(() => {
      // Setup code
    });
    
    it('should allow staff to log in', () => {
      // Test steps
      cy.fn_login('staff', accounts.cypress_a.staff.email, accounts.cypress_a.staff.password);
      cy.get('.success-message').should('contain', 'Logged in successfully');
    });
    
    // More test cases...
  });
});
```

### Test Naming Conventions

- Test files: `[feature-name]/[user-role].spec.cy.js`
- Test descriptions: Should clearly describe the functionality being tested
- Test filters: Use tags like `P1`, `P2Sanity`, `Regression` to categorize tests

### Creating a New Test

1. Identify the feature and user role you want to test
2. Create a new file in the appropriate directory
3. Import necessary page objects and test data
4. Write your test cases using the standard structure

Example:

```javascript
// cypress/e2e/004 booking-management/manageBookings/hotelier-manager.spec.cy.js
import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';
import { bookingHub } from '../../../support/pageObjectModel/Pages/bookingHub';

TestFilters(['P2Sanity'], () => {
  describe('Hotelier Manager - Manage Bookings', () => {
    beforeEach(() => {
      cy.fn_login('hotelier',
        accounts.cypress_a.hotelier.manager.email,
        accounts.cypress_a.hotelier.manager.password,
        accounts.cypress_a.slug
      );
      cy.visit('/bookings');
    });
    
    it('can view booking details', () => {
      bookingHub.clickFirstBooking();
      bookingHub.assertBookingDetailsVisible();
    });
  });
});
```

## Page Object Model

The framework implements the Page Object Model (POM) pattern in two ways:

### Simple Page Objects

Located in `cypress/pages/`, these are basic page objects with element selectors and simple actions.

Example:

```javascript
// cypress/pages/login.js
class Login {
  elements = {
    emailInput: () => cy.get('#email'),
    passwordInput: () => cy.get('#password'),
    loginButton: () => cy.get('button[type="submit"]')
  }

  typeEmail(email) {
    this.elements.emailInput().clear().type(email);
    return this;
  }

  typePassword(password) {
    this.elements.passwordInput().clear().type(password);
    return this;
  }

  clickLogin() {
    this.elements.loginButton().click();
    return this;
  }
}

export default Login;
```

### Advanced Page Objects

Located in `cypress/support/pageObjectModel/`, these are more complex page objects with advanced functionality.

Example:

```javascript
// cypress/support/pageObjectModel/Pages/bookingHub.js
export const bookingHub = {
  selectors: {
    addPaymentButton: '.add-payment-button',
    paymentStatusLabel: '.payment-status',
    bookingHistoryLink: '[data-cy=booking-history-link]'
  },

  clickAddPaymentButton() {
    cy.get(this.selectors.addPaymentButton).click();
    return this;
  },

  assertPaymentStatus(status) {
    cy.get(this.selectors.paymentStatusLabel).should('contain', status);
    return this;
  }
};
```

### Creating a New Page Object

1. Identify the page or component you want to model
2. Decide whether to use a simple or advanced page object
3. Create a new file in the appropriate directory
4. Define selectors and methods for interacting with the page

## Test Data Management

Test data is managed using JavaScript modules in the `cypress/fixtures/` directory.

### Account Data

```javascript
// cypress/fixtures/accounts.js
export default {
  cypress_a: {
    hotelier: {
      manager: {
        email: '<EMAIL>',
        password: 'password123'
      }
    },
    slug: 'cardiff',
    title: 'Cardiff Plaza'
  }
};
```

### Payment Card Data

```javascript
// cypress/fixtures/cards/paymentDetailsBarclays.js
export const validBarclaysCards = {
  visaCredit: {
    cardHolderName: 'Test User',
    cardNumber: '****************',
    expirationMonth: '12',
    expirationYear: '2025',
    ccv: '123',
    challengePassword: 'password'
  }
};
```

### Adding New Test Data

1. Identify the type of test data you need
2. Create or update the appropriate file in the `cypress/fixtures/` directory
3. Export the data as a JavaScript object or module

## Running Tests

### Using Cypress Test Runner

```bash
# Open Cypress Test Runner
npx cypress open
```

### Running Tests from Command Line

```bash
# Run all tests
npx cypress run

# Run specific test file
npx cypress run --spec "cypress/e2e/001 Default/001_login.cy.js"

# Run tests with specific tags
npx cypress run --env grepTags=P2Sanity
```

### Running Tests in Different Environments

```bash
# Run tests in staging environment
npx cypress run --config baseUrl=https://staging.example.com

# Run tests with specific environment variables
npx cypress run --env staff_email=<EMAIL>,staff_password=password123
```

## Payment Testing

The framework includes specialized support for payment testing.

### Payment Gateways

The framework supports multiple payment gateways:

- **Barclays Gateway**: Uses nested iframes for payment forms
- **JudoPay Gateway**: Uses a single iframe for payment forms

### Testing Payment Flows

1. **Direct Payment**:
   ```javascript
   it('Verify user can complete payment with Barclays gateway', () => {
     // Navigate to payment page
     bookingHub.clickAddPaymentButton();
     
     // Fill payment amount
     cy.get('input[name*="amount"]').clear().type('10');
     
     // Submit the form
     payByLinkForm.submitAndOpenPBL(paymentAmount);
     
     // Fill card details in the iframe
     paymentIframe.fillBarclaysDetails(card);
     
     // Verify successful payment
     payByLinkPage.assertSuccessfulPayment(paymentAmountFormatted);
   });
   ```

2. **3D Secure Challenge**:
   ```javascript
   it('Verify user can complete 3DS challenge payment', () => {
     // Navigate to payment page and fill amount
     bookingHub.clickAddPaymentButton();
     cy.get('input[name*="amount"]').clear().type('10');
     payByLinkForm.submitAndOpenPBL(paymentAmount);
     
     // Fill card details and handle 3DS challenge
     paymentIframe.fillBarclaysDetailsWithChallenge(card);
     
     // Verify successful payment
     payByLinkPage.assertSuccessfulPayment(paymentAmountFormatted);
   });
   ```

## Iframe Handling

The framework uses several approaches to handle iframes:

### Using the Cypress iframe plugin

```javascript
cy.iframe('#card-capture-for-cardUuid').find('#cardholderName').type('Test User');
```

### Using jQuery for nested iframes

```javascript
cy.window().then(win => {
  const $nestedIframe = win.jQuery('#barclays-iframe');
  const $nameField = win.jQuery($nestedIframe[0]).contents().find('#cardholderName');
  $nameField.val(card.cardHolderName).trigger('input');
});
```

### Using the Cypress enter command

```javascript
cy.enter(paymentIframe.selectors.iframe).then(getBody => {
  getBody().find(paymentIframe.selectors.judoPayCardholderName).type(card.cardHolderName);
});
```

## Best Practices

1. **Use Page Objects**: Encapsulate page elements and actions in page objects.
2. **Organize Test Data**: Keep test data in fixtures and separate from test logic.
3. **Handle Iframes Properly**: Use the appropriate approach for iframe interactions.
4. **Implement Robust Selectors**: Use multiple selector strategies for better resilience.
5. **Add Proper Waits**: Use explicit waits instead of fixed timeouts when possible.
6. **Handle Errors Gracefully**: Add error handling for complex operations like payment processing.
7. **Use Test Filters**: Organize tests with tags for better test suite management.
8. **Keep Tests Independent**: Each test should be independent and not rely on other tests.
9. **Document Complex Logic**: Add comments to explain complex test logic, especially for iframe handling.
10. **Implement Chainable Methods**: Use method chaining for more readable test code.

## Troubleshooting

### Common Issues

1. **Selector Not Found**: 
   - Check if the selector is correct
   - Add a longer timeout: `cy.get('.selector', { timeout: 10000 })`
   - Use a more robust selector strategy

2. **Iframe Interaction Issues**:
   - Make sure the iframe is fully loaded before interacting with it
   - Try different iframe handling approaches
   - Add explicit waits before and after iframe interactions

3. **Test Data Issues**:
   - Verify that the test data is correctly imported
   - Check for typos in property names
   - Ensure the test data is appropriate for the test scenario

4. **Payment Processing Issues**:
   - Check if the payment gateway is available in the test environment
   - Verify that the test card details are correct
   - Add longer waits for payment processing
   - Check for iframe handling issues

### Getting Help

If you encounter issues that you can't resolve, you can:

1. Check the Cypress documentation: https://docs.cypress.io/
2. Search for similar issues in the project's issue tracker
3. Ask for help from the team's test automation experts

## Conclusion

This user guide provides a comprehensive overview of how to use the Cypress test automation framework for testing the hotel booking system. By following the guidelines and best practices outlined in this guide, you can create robust and maintainable automated tests for the application.
