# Cypress Test Automation Framework Tutorial

## Introduction

This tutorial provides a comprehensive guide to the Cypress test automation framework used for testing the hotel booking system. This tutorial was generated using PocketFlow Tutorial Generator with the Gemini API.

## Table of Contents

1. [Page Object Model (POM)](#page-object-model-pom)
2. [Fixtures (Test Data)](#fixtures-test-data)
3. [Cypress Custom Commands & Helper Functions](#cypress-custom-commands--helper-functions)
4. [Environment Configuration](#environment-configuration)
5. [User Role-Based Testing](#user-role-based-testing)
6. [Permissions Matrix (Staff Matrix)](#permissions-matrix-staff-matrix)
7. [Booking Engine Workflow](#booking-engine-workflow)
8. [Payment Gateway Integration & PayByLink](#payment-gateway-integration--paybylink)
9. [Unified Notes Page](#unified-notes-page)
10. [Test Organization & Execution](#test-organization--execution)

## Page Object Model (POM)

The test automation framework implements the Page Object Model (POM) pattern to create a structured and maintainable test suite. The POM pattern separates the test logic from the page structure, making tests more readable and easier to maintain.

### Page Objects Structure

Page objects are organized in two main directories:

1. **Simple Page Objects** (`cypress/pages/`): Basic page objects with element selectors and simple actions.
2. **Advanced Page Objects** (`cypress/support/pageObjectModel/`): More complex page objects with advanced functionality.

### Simple Page Object Example

```javascript
// cypress/pages/login.js
class Login {
  elements = {
    emailInput: () => cy.get('#email'),
    passwordInput: () => cy.get('#password'),
    loginButton: () => cy.get('button[type="submit"]')
  }

  typeEmail(email) {
    this.elements.emailInput().clear().type(email);
    return this;
  }

  typePassword(password) {
    this.elements.passwordInput().clear().type(password);
    return this;
  }

  clickLogin() {
    this.elements.loginButton().click();
    return this;
  }

  login(email, password) {
    this.typeEmail(email);
    this.typePassword(password);
    this.clickLogin();
  }
}

export default Login;
```

### Advanced Page Object Example

```javascript
// cypress/support/pageObjectModel/Pages/bookingHub.js
export const bookingHub = {
  selectors: {
    addPaymentButton: '.add-payment-button',
    paymentStatusLabel: '.payment-status',
    bookingHistoryLink: '[data-cy=booking-history-link]'
  },

  clickAddPaymentButton() {
    cy.get(this.selectors.addPaymentButton).click();
    return this;
  },

  clickBookingHistoryLink() {
    cy.get(this.selectors.bookingHistoryLink).click();
    return this;
  },

  assertPaymentStatus(status) {
    cy.get(this.selectors.paymentStatusLabel).should('contain', status);
    return this;
  }
};
```

## Fixtures (Test Data)

Test data is managed using JavaScript modules in the `cypress/fixtures/` directory, making it easy to maintain and reuse across tests.

### Account Data

```javascript
// cypress/fixtures/accounts.js
export default {
  cypress_a: {
    hotelier: {
      manager: {
        email: '<EMAIL>',
        password: 'password123'
      },
      terminal: {
        email: '<EMAIL>',
        password: 'password123'
      }
    },
    slug: 'cardiff',
    title: 'Cardiff Plaza'
  }
};
```

### Payment Card Data

```javascript
// cypress/fixtures/cards/paymentDetailsBarclays.js
export const validBarclaysCards = {
  visaCredit: {
    cardHolderName: 'Test User',
    cardNumber: '****************',
    expirationMonth: '12',
    expirationYear: '2025',
    ccv: '123',
    challengePassword: 'password'
  }
};
```

## Cypress Custom Commands & Helper Functions

The framework extends Cypress with custom commands and helper functions to simplify test creation and maintenance.

### Custom Commands

```javascript
// cypress/support/commands/iframe-commands.js
Cypress.Commands.add('iframe', { prevSubject: 'element' }, ($iframe, selector) => {
  return cy
    .wrap($iframe)
    .should(iframe => expect(iframe.contents().find('body')).to.exist)
    .then(iframe => cy.wrap(iframe.contents().find(selector)));
});

// cypress/support/functions/login.js
Cypress.Commands.add('fn_login', (type, email, password, slug = null, options = {}) => {
  const defaultOptions = { failOnStatusCode: true };
  const mergedOptions = { ...defaultOptions, ...options };

  if (type === 'staff') {
    cy.visit('/staff/login', mergedOptions);
    cy.get('#email').type(email);
    cy.get('#password').type(password);
    cy.get('button[type="submit"]').click();
  } else if (type === 'hotelier') {
    cy.visit('/login/', mergedOptions);
    cy.get('#email').type(email);
    cy.get('#password').type(password);
    cy.get('button[type="submit"]').click();
  }
});
```

### Helper Functions

```javascript
// cypress/support/helpers/interceptors.js
export const interceptors = {
  interceptBookingRequests() {
    cy.intercept('POST', '**/api/bookings').as('createBooking');
    cy.intercept('GET', '**/api/bookings/*').as('getBooking');
    return this;
  },
  
  waitForBookingCreation() {
    cy.wait('@createBooking').its('response.statusCode').should('eq', 201);
    return this;
  }
};
```

## Environment Configuration

The framework uses environment configuration files to manage different test environments (development, staging, production).

```javascript
// cypress/env/staging.json
{
  "baseUrl": "https://staging.example.com",
  "staff_email": "<EMAIL>",
  "staff_password": "password123",
  "apiUrl": "https://api-staging.example.com"
}
```

## User Role-Based Testing

The framework implements role-based testing to verify functionality for different user roles.

```javascript
// cypress/e2e/003 room-management/manageRoomTypes/hotelier-manager.spec.cy.js
import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {
  describe('Hotelier Manager - Room Type Management', () => {
    beforeEach(() => {
      cy.fn_login('hotelier',
        accounts.cypress_a.hotelier.manager.email,
        accounts.cypress_a.hotelier.manager.password,
        accounts.cypress_a.slug
      );
    });
    
    it('can create a new room type', () => {
      // Test implementation
    });
    
    it('can edit an existing room type', () => {
      // Test implementation
    });
  });
});
```

## Permissions Matrix (Staff Matrix)

The framework tests permissions across different user roles to ensure proper access control.

```javascript
// Example of testing the same functionality across different roles
// cypress/e2e/003 room-management/manageRoomTypes/staff-admin.spec.cy.js
// cypress/e2e/003 room-management/manageRoomTypes/hotelier-manager.spec.cy.js
// cypress/e2e/003 room-management/manageRoomTypes/hotelier-terminal.spec.cy.js
// cypress/e2e/003 room-management/manageRoomTypes/hotelier-front-desk.spec.cy.js
// cypress/e2e/003 room-management/manageRoomTypes/guest.spec.cy.js
// cypress/e2e/003 room-management/manageRoomTypes/corporation.spec.cy.js
```

## Booking Engine Workflow

The framework tests the complete booking engine workflow from calendar selection to payment.

```javascript
// cypress/e2e/features/booking-engine/completeBooking.spec.cy.js
it('completes a booking with payment', () => {
  // Navigate to booking calendar
  cy.visit('/book');
  
  // Select dates
  calendar.selectCheckInDate('10');
  calendar.selectCheckOutDate('15');
  calendar.clickContinue();
  
  // Select room
  yourStay.selectRoomType('Deluxe');
  yourStay.clickContinue();
  
  // Enter guest details
  guestDetails.fillGuestForm('John', 'Doe', '<EMAIL>', '1234567890');
  guestDetails.clickContinue();
  
  // Skip extras
  extras.clickContinue();
  
  // Complete payment
  pay.fillCardDetails(validCards.visaCredit);
  pay.clickPayNow();
  
  // Verify confirmation
  thankYou.assertBookingConfirmed();
});
```

## Payment Gateway Integration & PayByLink

The framework includes specialized handling for payment gateways, particularly for complex scenarios like iframe forms and 3D Secure authentication.

### Payment Iframe Handling

```javascript
// cypress/support/pageObjectModel/Components/paymentIFrame.js
export const paymentIframe = {
  selectors: {
    iframe: '#card-capture-for-cardUuid',
    barclaysIFrame: "#barclays-iframe",
    barclaysIframeCardholderName:'#cardholderName',
    barclaysIframeCardNumber:'#cardNumber',
    barclaysExpiryMonth: '#expiryMonth',
    barclaysExpiryYear: '#expiryYear',
    barclaysSecurityCode: '#csc'
  },

  fillBarclaysDetails: (card) => {
    // Wait for the iframe to be fully loaded
    cy.wait(3000);
    
    // Handle nested iframe scenario
    cy.get('iframe#card-capture-for-cardUuid').then($outerIframe => {
      cy.iframe('#card-capture-for-cardUuid').then($outerIframeBody => {
        // Check if there's a nested iframe with ID 'barclays-iframe'
        const nestedIframe = $outerIframeBody.find('#barclays-iframe');
        
        if (nestedIframe.length > 0) {
          // Use jQuery to access and fill the nested iframe
          cy.window().then(win => {
            const $nestedIframe = win.jQuery('#barclays-iframe');
            
            // Fill in the form fields
            const $nameField = win.jQuery($nestedIframe[0]).contents().find('#cardholderName');
            const $cardField = win.jQuery($nestedIframe[0]).contents().find('#cardNumber');
            const $monthSelect = win.jQuery($nestedIframe[0]).contents().find('#expiryMonth');
            const $yearSelect = win.jQuery($nestedIframe[0]).contents().find('#expiryYear');
            const $securityField = win.jQuery($nestedIframe[0]).contents().find('#csc');
            
            $nameField.val(card.cardHolderName).trigger('input');
            $cardField.val(card.cardNumber).trigger('input');
            $monthSelect.val(card.expirationMonth).trigger('change');
            $yearSelect.val(card.expirationYear).trigger('change');
            $securityField.val(card.ccv).trigger('input');
            
            // Submit the form
            const $submitButton = win.jQuery($nestedIframe[0]).contents().find('button[type="submit"]');
            $submitButton.click();
          });
        }
      });
    });
  }
};
```

## Unified Notes Page

The framework includes tests for the unified notes functionality, which allows staff to add and view notes across the system.

```javascript
// cypress/support/pageObjectModel/Pages/unifiedNotesPage.js
export const unifiedNotesPage = {
  selectors: {
    addNoteButton: '[data-cy=add-note-button]',
    noteTextarea: '[data-cy=note-textarea]',
    saveNoteButton: '[data-cy=save-note-button]',
    notesList: '[data-cy=notes-list]',
    noteItem: '[data-cy=note-item]'
  },

  clickAddNote() {
    cy.get(this.selectors.addNoteButton).click();
    return this;
  },

  enterNoteText(text) {
    cy.get(this.selectors.noteTextarea).clear().type(text);
    return this;
  },

  saveNote() {
    cy.get(this.selectors.saveNoteButton).click();
    return this;
  },

  assertNoteExists(text) {
    cy.get(this.selectors.notesList)
      .find(this.selectors.noteItem)
      .should('contain', text);
    return this;
  }
};
```

## Test Organization & Execution

### Test Organization

Tests are organized by feature and functionality:

```
cypress/
├── e2e/
│   ├── 001 Default/           # Basic tests like login
│   ├── 002 user-management/   # User management tests
│   ├── 003 room-management/   # Room management tests
│   ├── features/              # Feature-specific tests
│   │   ├── booking-engine/    # Booking engine tests
│   │   ├── payments/          # Payment tests
│   │   └── ...
```

### Test Filtering

Tests can be filtered using tags:

```javascript
// cypress/support/filterTests.js
const TestFilters = (tags, testFn) => {
  if (Cypress.env('grepTags')) {
    const envTags = Cypress.env('grepTags').split(',');
    const shouldRun = tags.some(tag => envTags.includes(tag));
    if (shouldRun) {
      testFn();
    }
  } else {
    testFn();
  }
};

export default TestFilters;
```

### Running Tests

Tests can be run using the Cypress Test Runner or from the command line:

```bash
# Open Cypress Test Runner
npx cypress open

# Run tests in headless mode
npx cypress run

# Run specific test file
npx cypress run --spec "cypress/e2e/001 Default/001_login.cy.js"

# Run tests with specific tags
npx cypress run --env grepTags=P2Sanity
```

## Conclusion

The Cypress test automation framework provides a robust solution for testing the hotel booking system, with specialized handling for complex scenarios like payment processing and 3D Secure authentication. By following the patterns and practices outlined in this tutorial, you can create maintainable and reliable automated tests for the application.
