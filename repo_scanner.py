#!/usr/bin/env python3
"""
Repository Scanner for Test Automation Framework

This script scans a repository for specific keywords and patterns to generate
a comprehensive overview of the test automation framework.
"""

import os
import re
import argparse
import json
from collections import defaultdict, Counter

# Define categories and their associated keywords
CATEGORIES = {
    "Page Objects": [
        "page", "component", "element", "selector", "locator", "POM", 
        "pageObject", "Page Object", "getBy", "findBy"
    ],
    "Test Fixtures": [
        "fixture", "test data", "mock", "stub", "fake", "seed", 
        "testData", "test-data", "sample data"
    ],
    "Payment Processing": [
        "payment", "card", "credit", "debit", "transaction", "gateway",
        "3D Secure", "3DS", "iframe", "Barclays", "JudoPay", "PayByLink",
        "checkout", "billing", "CVV", "CVC", "expiry", "cardholder"
    ],
    "Authentication": [
        "login", "logout", "auth", "authenticate", "credentials", 
        "password", "username", "email", "sign in", "signin"
    ],
    "Booking Flow": [
        "booking", "reservation", "check-in", "checkout", "guest", 
        "hotel", "room", "stay", "calendar", "date picker"
    ],
    "Test Utilities": [
        "helper", "utility", "util", "function", "command", "custom command",
        "intercept", "stub", "mock", "wait", "timeout", "retry"
    ],
    "Iframe Handling": [
        "iframe", "frame", "nested", "contentDocument", "contentWindow",
        "cross-origin", "jQuery", "contents"
    ],
    "Form Handling": [
        "form", "input", "select", "checkbox", "radio", "dropdown",
        "submit", "validation", "field", "label", "button"
    ],
    "API Testing": [
        "api", "request", "response", "endpoint", "REST", "HTTP",
        "GET", "POST", "PUT", "DELETE", "intercept", "route"
    ],
    "Test Configuration": [
        "config", "environment", "env", "setup", "teardown", "before",
        "after", "beforeEach", "afterEach", "hook"
    ]
}

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Scan repository for test automation framework patterns')
    parser.add_argument('--dir', required=True, help='Path to the repository directory')
    parser.add_argument('--include', nargs='+', default=['*.js', '*.ts', '*.jsx', '*.tsx', '*.json'], 
                        help='File patterns to include (default: *.js *.ts *.jsx *.tsx *.json)')
    parser.add_argument('--exclude', nargs='+', default=['node_modules/*', 'dist/*', 'build/*'],
                        help='File patterns to exclude (default: node_modules/* dist/* build/*)')
    parser.add_argument('--output', default='repo_scan_results.json', 
                        help='Output file for scan results (default: repo_scan_results.json)')
    parser.add_argument('--summary', default='repo_scan_summary.md',
                        help='Output file for summary markdown (default: repo_scan_summary.md)')
    return parser.parse_args()

def should_include_file(file_path, include_patterns, exclude_patterns):
    """Check if a file should be included based on patterns."""
    # Check exclude patterns first
    for pattern in exclude_patterns:
        if pattern.startswith('*'):
            if file_path.endswith(pattern[1:]):
                return False
        elif pattern.endswith('*'):
            if file_path.startswith(pattern[:-1]):
                return False
        elif pattern in file_path:
            return False
    
    # Then check include patterns
    for pattern in include_patterns:
        if pattern.startswith('*'):
            if file_path.endswith(pattern[1:]):
                return True
        elif pattern.endswith('*'):
            if file_path.startswith(pattern[:-1]):
                return True
        elif pattern in file_path:
            return True
    
    return False

def scan_file(file_path, categories):
    """Scan a file for keywords in each category."""
    results = defaultdict(list)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            for category, keywords in categories.items():
                for keyword in keywords:
                    # Case insensitive search
                    matches = re.finditer(re.escape(keyword), content, re.IGNORECASE)
                    for match in matches:
                        # Get some context around the match
                        start = max(0, match.start() - 50)
                        end = min(len(content), match.end() + 50)
                        context = content[start:end].replace('\n', ' ').strip()
                        
                        results[category].append({
                            'keyword': keyword,
                            'line_number': content[:match.start()].count('\n') + 1,
                            'context': context
                        })
    except Exception as e:
        print(f"Error scanning file {file_path}: {e}")
    
    return results

def scan_repository(repo_dir, include_patterns, exclude_patterns, categories):
    """Scan the entire repository for patterns."""
    all_results = {}
    file_count = 0
    
    for root, _, files in os.walk(repo_dir):
        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, repo_dir)
            
            if should_include_file(rel_path, include_patterns, exclude_patterns):
                print(f"Scanning {rel_path}...")
                file_results = scan_file(file_path, categories)
                
                if any(file_results.values()):
                    all_results[rel_path] = file_results
                    file_count += 1
    
    print(f"Scanned {file_count} files.")
    return all_results

def generate_summary(scan_results, repo_dir):
    """Generate a markdown summary of the scan results."""
    # Count occurrences of each category
    category_counts = defaultdict(int)
    category_files = defaultdict(set)
    keyword_counts = Counter()
    
    for file_path, file_results in scan_results.items():
        for category, matches in file_results.items():
            category_counts[category] += len(matches)
            category_files[category].add(file_path)
            
            for match in matches:
                keyword_counts[match['keyword']] += 1
    
    # Generate markdown
    summary = f"# Test Automation Framework Scan Results\n\n"
    summary += f"Repository: `{os.path.basename(repo_dir)}`\n\n"
    summary += f"Scanned {len(scan_results)} files with matches.\n\n"
    
    # Overall category summary
    summary += "## Categories Overview\n\n"
    summary += "| Category | Occurrences | Files |\n"
    summary += "|----------|-------------|-------|\n"
    
    for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
        summary += f"| {category} | {count} | {len(category_files[category])} |\n"
    
    # Top keywords
    summary += "\n## Top Keywords\n\n"
    summary += "| Keyword | Occurrences |\n"
    summary += "|---------|-------------|\n"
    
    for keyword, count in keyword_counts.most_common(20):
        summary += f"| `{keyword}` | {count} |\n"
    
    # Category details
    summary += "\n## Category Details\n\n"
    
    for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
        summary += f"### {category} ({count} occurrences)\n\n"
        summary += "Found in files:\n\n"
        
        for file_path in sorted(category_files[category]):
            summary += f"- `{file_path}`\n"
        
        summary += "\n"
    
    return summary

def main():
    """Main function."""
    args = parse_args()
    
    print(f"Scanning repository: {args.dir}")
    print(f"Include patterns: {args.include}")
    print(f"Exclude patterns: {args.exclude}")
    
    scan_results = scan_repository(args.dir, args.include, args.exclude, CATEGORIES)
    
    # Save detailed results as JSON
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(scan_results, f, indent=2)
    
    print(f"Detailed results saved to {args.output}")
    
    # Generate and save summary
    summary = generate_summary(scan_results, args.dir)
    with open(args.summary, 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print(f"Summary saved to {args.summary}")

if __name__ == "__main__":
    main()
