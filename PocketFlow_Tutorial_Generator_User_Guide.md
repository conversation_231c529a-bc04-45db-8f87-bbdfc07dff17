# PocketFlow Tutorial Generator User Guide

## Introduction

PocketFlow Tutorial Generator is a powerful tool that uses AI to analyze GitHub repositories or local codebases and generate beginner-friendly tutorials. It identifies key abstractions, analyzes their relationships, and creates a structured tutorial that explains how the code works.

This guide will walk you through the setup process and show you how to use the tool effectively.

## Prerequisites

- Python 3.8 or higher
- Git (for cloning repositories)
- An API key for one of the supported LLM providers:
  - Google Gemini API key
  - Anthropic Claude API key (optional)
  - OpenAI API key (optional)

## Setup Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/The-Pocket/PocketFlow-Tutorial-Codebase-Knowledge.git
cd PocketFlow-Tutorial-Codebase-Knowledge
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure API Keys

Create a `.env` file in the root directory of the project:

```bash
touch .env
```

Add your API key(s) to the `.env` file:

```
GEMINI_API_KEY=your_gemini_api_key
GITHUB_TOKEN=your_github_token  # Optional, for higher GitHub API rate limits
ANTHROPIC_API_KEY=your_anthropic_api_key  # Optional
OPENAI_API_KEY=your_openai_api_key  # Optional
```

### 4. Configure LLM Provider (Optional)

By default, the tool uses Google's Gemini Pro model. If you want to use a different LLM provider, you'll need to modify the `utils/call_llm.py` file.

For Anthropic Claude:
1. Open `utils/call_llm.py`
2. Comment out the default Gemini implementation
3. Uncomment the Anthropic Claude implementation

For OpenAI:
1. Open `utils/call_llm.py`
2. Comment out the default Gemini implementation
3. Uncomment the OpenAI implementation

## Using the Tool

### Basic Usage

To generate a tutorial for a GitHub repository:

```bash
python main.py --repo https://github.com/username/repo
```

To generate a tutorial for a local directory:

```bash
python main.py --dir /path/to/your/codebase
```

### Working with Local Repositories

When working with local repositories like the test-automation codebase, you'll need to:

1. **Identify the repository location**: Determine the full path to the repository (e.g., `/var/www/test-automation`)

2. **Analyze the codebase structure**: Before generating a tutorial, examine the directory structure to understand what files are most important:
   ```bash
   # List top-level directories
   ls -la /var/www/test-automation

   # Count files by extension to identify primary languages
   find /var/www/test-automation -type f | grep -v "node_modules" | grep -v ".git" | grep -E "\.[a-zA-Z0-9]+$" | sed 's/.*\.//' | sort | uniq -c | sort -nr
   ```

3. **Choose appropriate include/exclude patterns**: Based on the codebase structure, select patterns that focus on the most relevant code:
   - Include patterns for primary code files (e.g., `*.java`, `*.py`)
   - Exclude test directories, build artifacts, and configuration files

4. **Set appropriate size limits**: For large codebases, set a reasonable file size limit to avoid processing very large files:
   ```bash
   python main.py --dir /var/www/test-automation --include "*.java" --exclude "tests/*" --max-size 50000
   ```

5. **Use timeouts for large repositories**: For very large repositories, use the timeout command to prevent the process from running indefinitely:
   ```bash
   timeout 600 python main.py --dir /var/www/test-automation --include "*.java" --exclude "tests/*" --max-size 50000
   ```

### Advanced Options

#### File Filtering

You can specify which files to include or exclude:

```bash
# Include only Python and JavaScript files
python main.py --repo https://github.com/username/repo --include "*.py" "*.js"

# Exclude test files and build directories
python main.py --repo https://github.com/username/repo --exclude "tests/*" "build/*"
```

#### File Size Limit

You can set a maximum file size (in bytes) to process:

```bash
# Only process files smaller than 50KB
python main.py --repo https://github.com/username/repo --max-size 50000
```

#### Language Selection

You can generate tutorials in different languages:

```bash
# Generate a tutorial in Chinese
python main.py --repo https://github.com/username/repo --language "Chinese"
```

#### Chapter Count

You can specify the number of chapters to generate:

```bash
# Generate a 5-chapter tutorial
python main.py --repo https://github.com/username/repo --chapter-count 5
```

### Handling Large Repositories

For large repositories, consider these strategies:

1. **Use more specific include/exclude patterns** to focus on core code:
   ```bash
   python main.py --repo https://github.com/username/repo --include "src/*.py" --exclude "tests/*" "examples/*"
   ```

2. **Set a smaller file size limit** to skip large generated files:
   ```bash
   python main.py --repo https://github.com/username/repo --max-size 10000
   ```

3. **Use a timeout** to limit the total processing time:
   ```bash
   timeout 600 python main.py --repo https://github.com/username/repo
   ```

## Output

The tool generates several files:

- `tutorial_[timestamp].md`: The complete tutorial in Markdown format
- `logs/llm_calls_[date].log`: Log of all LLM calls made during generation
- `llm_cache.json`: Cache of LLM responses to speed up future runs

## Troubleshooting

### API Key Issues

If you encounter errors related to API keys:

1. Verify that your API key is correct
2. Check that the API key is properly set in the `.env` file
3. Make sure the LLM provider you're using is properly configured in `utils/call_llm.py`

### GitHub Rate Limiting

If you hit GitHub API rate limits:

1. Add a GitHub token to your `.env` file
2. Try analyzing a local copy of the repository instead:
   ```bash
   git clone https://github.com/username/repo
   python main.py --dir ./repo
   ```

### Memory Issues

If the tool runs out of memory:

1. Use more specific include/exclude patterns
2. Set a smaller file size limit
3. Process a subset of the repository

### Timeout Issues

If the tool times out:

1. Use the `timeout` command with a longer duration
2. Process a smaller subset of the repository
3. Use more specific include/exclude patterns

## Examples

### Generate a tutorial for a small repository:

```bash
python main.py --repo https://github.com/The-Pocket/PocketFlow-Template-Python
```

### Generate a tutorial for a specific part of a large repository:

```bash
python main.py --repo https://github.com/tensorflow/tensorflow --include "tensorflow/python/keras/*.py" --exclude "*test*" --max-size 20000
```

### Generate a tutorial in Chinese for a local directory:

```bash
python main.py --dir ./my-project --language "Chinese"
```

### Generate a tutorial for the test-automation repository:

```bash
# Assuming test-automation is at /var/www/test-automation
python main.py --dir /var/www/test-automation --include "*.java" "*.py" --exclude "tests/*" "build/*" --max-size 50000
```

For the test-automation repository specifically, you might want to focus on certain components:

```bash
# Focus on just the booking-related page objects
python main.py --dir /var/www/test-automation --include "src/pages/booking/*.java" --max-size 50000

# Focus on the test framework core (page objects and utilities)
python main.py --dir /var/www/test-automation --include "src/pages/*.java" "src/toolbox/*.java" --exclude "*Test.java" --max-size 50000
```

## Best Practices

1. **Start small**: Begin with smaller repositories or specific directories
2. **Use specific patterns**: Target core code files and exclude tests, examples, etc.
3. **Set reasonable limits**: Use file size limits to avoid processing large generated files
4. **Cache LLM responses**: The tool caches LLM responses, so subsequent runs will be faster
5. **Review and refine**: Review the generated tutorial and refine your parameters for better results
6. **Iterative approach**: For large codebases like test-automation, use an iterative approach:
   - Start with a small subset of files to generate a basic tutorial
   - Gradually expand the scope to include more files
   - Merge insights from multiple runs into a comprehensive tutorial
7. **Focus on architecture first**: Generate a tutorial focusing on architectural components first:
   ```bash
   python main.py --dir /var/www/test-automation --include "src/**/Base*.java" --max-size 50000
   ```
8. **Then explore specific features**: After understanding the architecture, explore specific features:
   ```bash
   python main.py --dir /var/www/test-automation --include "src/**/Booking*.java" --max-size 50000
   ```

## Specific Guide for test-automation Repository

The test-automation repository contains a Java-based test framework using Selenium WebDriver and follows the Page Object Model pattern. Here's a specific guide for generating a tutorial for this repository:

### Understanding the Repository Structure

The test-automation repository has the following key components:

- `src/pages/`: Contains page object classes
  - `src/pages/booking/`: Booking-specific page objects
  - `src/pages/BasePage.java`: Base class for all page objects
- `src/toolbox/`: Contains utility classes
- `src/tests/`: Contains test classes
- `src/testdata/`: Contains test data files
- `src/suite/`: Contains TestNG suite definitions

### Recommended Tutorial Generation Approach

For the test-automation repository, we recommend the following approach:

1. **Generate a tutorial for the core framework components**:
   ```bash
   python main.py --dir /var/www/test-automation --include "src/pages/BasePage.java" "src/toolbox/GenericMethods.java" --max-size 50000
   ```

2. **Generate a tutorial for the booking page objects**:
   ```bash
   python main.py --dir /var/www/test-automation --include "src/pages/booking/*.java" --max-size 50000
   ```

3. **Generate a tutorial for the test implementation**:
   ```bash
   python main.py --dir /var/www/test-automation --include "src/tests/booking/*.java" --max-size 50000
   ```

4. **Generate a tutorial for the test data management**:
   ```bash
   python main.py --dir /var/www/test-automation --include "src/testdata/booking/*.json" --max-size 50000
   ```

5. **Combine the insights from these tutorials** into a comprehensive guide that covers:
   - The Page Object Model architecture
   - Base classes and utilities
   - Specific page implementations
   - Test case implementation
   - Test data management
   - Test execution

### Example Complete Command

To generate a comprehensive tutorial for the test-automation repository in one go:

```bash
timeout 900 python main.py --dir /var/www/test-automation --include "src/pages/*.java" "src/pages/booking/*.java" "src/toolbox/*.java" "src/tests/booking/*.java" --exclude "node_modules/*" ".git/*" --max-size 50000 --chapter-count 7
```

This will generate a tutorial with 7 chapters covering the key aspects of the test automation framework.

## Conclusion

PocketFlow Tutorial Generator is a powerful tool for creating beginner-friendly tutorials from codebases. By following this guide, you should be able to set up and use the tool effectively to generate high-quality tutorials for any codebase, including the test-automation repository.

For more information, visit the [GitHub repository](https://github.com/The-Pocket/PocketFlow-Tutorial-Codebase-Knowledge).
