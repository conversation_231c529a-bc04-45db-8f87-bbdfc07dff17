#!/bin/bash

# This script updates all Cypress test files in the booking-engine directory
# to use the dynamic reseeding command that uses the current baseUrl

# Find all test files in the booking-engine directory
TEST_FILES=$(find cypress/e2e/booking-engine -name "*.cy.js")

# Counter for modified files
MODIFIED_COUNT=0

# Process each file
for file in $TEST_FILES; do
  echo "Processing $file..."
  
  # Create a backup of the file
  cp "$file" "${file}.bak"
  
  # Replace direct reseeding requests with the dynamic reseeding command
  # Pattern 1: Simple cy.request with template literal
  sed -i 's/cy.request(`automation\/tests\/reseedHotel\/${hotelSlug}`)/cy.dynamicReseed(hotelSlug)/g' "$file"
  sed -i 's/cy.request(`\/automation\/tests\/reseedHotel\/${hotelSlug}`)/cy.dynamicReseed(hotelSlug)/g' "$file"
  
  # Pattern 2: cy.request with options object
  sed -i 's/cy.request({[[:space:]]*url: `automation\/tests\/reseedHotel\/${hotelSlug}`,[[:space:]]*failOnStatusCode: false[[:space:]]*})/cy.dynamicReseed(hotelSlug)/g' "$file"
  sed -i 's/cy.request({[[:space:]]*url: `\/automation\/tests\/reseedHotel\/${hotelSlug}`,[[:space:]]*failOnStatusCode: false[[:space:]]*})/cy.dynamicReseed(hotelSlug)/g' "$file"
  
  # Pattern 3: cy.request with string literal
  sed -i -E 's/cy.request\(["'\'']automation\/tests\/reseedHotel\/([^"'\'']+)["'\'']\)/cy.dynamicReseed("\1")/g' "$file"
  sed -i -E 's/cy.request\(["'\'']\/automation\/tests\/reseedHotel\/([^"'\'']+)["'\'']\)/cy.dynamicReseed("\1")/g' "$file"
  
  # Pattern 4: cy.request with template literal and direct hotel ID
  sed -i -E 's/cy.request\(`automation\/tests\/reseedHotel\/([^`]+)`\)/cy.dynamicReseed("\1")/g' "$file"
  sed -i -E 's/cy.request\(`\/automation\/tests\/reseedHotel\/([^`]+)`\)/cy.dynamicReseed("\1")/g' "$file"
  
  # Check if the file was modified
  if ! diff -q "$file" "${file}.bak" > /dev/null; then
    echo "  Modified: $file"
    MODIFIED_COUNT=$((MODIFIED_COUNT + 1))
  else
    echo "  No changes needed in $file"
    # Restore the backup if no changes were made
    mv "${file}.bak" "$file"
  fi
done

echo "Completed! Modified $MODIFIED_COUNT files."
