# Running Cypress Tests in AWS Environments

This document provides step-by-step instructions for running the Cypress E2E booking engine tests in AWS environments like UAT, in both headed and headless modes.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Running Tests in Headless Mode](#running-tests-in-headless-mode)
4. [Running Tests in Headed Mode](#running-tests-in-headed-mode)
5. [Running Specific Test Suites](#running-specific-test-suites)
6. [Handling Test Results](#handling-test-results)
7. [Troubleshooting](#troubleshooting)
8. [CI/CD Integration](#cicd-integration)

## Prerequisites

Before running the tests, ensure you have the following:

- Node.js (v14 or later) installed
- npm (v6 or later) installed
- Access to the AWS environment (UAT, staging, etc.)
- Proper network connectivity to the AWS environment
- Sufficient permissions to access the application in the AWS environment

## Environment Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-organization/test-automation.git
cd test-automation
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Verify Environment Configuration

Check that the environment configuration file for your target environment exists and contains the correct settings:

```bash
cat cypress/env/uat.json
```

The file should contain the correct base URL and environment variables:

```json
{
    "baseUrl": "https://uat.dev.high-level-software.com",
    "video": false,
    "env": {
        "staff_email": "<EMAIL>",
        "staff_password": "Password39?",
        "environment": "UAT",
        "zonalAPIUsername": "1ee2ac1e-bc10-4f3c-8274-8b6a4800d605",
        "zonalAPIPassword": "EPlU5dydASSgOIn0"
    }
}
```

If you need to create or modify the environment configuration, use the following format:

```json
{
    "baseUrl": "https://your-aws-environment-url.com",
    "video": false,
    "env": {
        "staff_email": "<EMAIL>",
        "staff_password": "your-password",
        "environment": "UAT",
        "zonalAPIUsername": "your-api-username",
        "zonalAPIPassword": "your-api-password"
    }
}
```

Save this file as `cypress/env/uat.json` (or the appropriate environment name).

## Running Tests in Headless Mode

Headless mode runs tests without displaying a browser UI. This is ideal for CI/CD pipelines and automated testing.

### Running All Booking Engine Tests

```bash
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

This command:
- Uses `npx cypress run` to run Cypress in headless mode
- Sets the environment to UAT with `--env environmentName=uat`
- Specifies all booking engine tests with `--spec "cypress/e2e/booking-engine/**/*.cy.js"`

### Running with Specific Browser

By default, Cypress uses Electron in headless mode. To use a different browser:

```bash
npx cypress run --browser chrome --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

Supported browsers: `chrome`, `firefox`, `edge` (must be installed on the system)

## Running Tests in Headed Mode

Headed mode displays the browser UI during test execution, which is useful for debugging and visual verification.

### Running All Booking Engine Tests

```bash
npx cypress run --headed --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

This command:
- Uses `npx cypress run --headed` to run Cypress in headed mode
- Sets the environment to UAT with `--env environmentName=uat`
- Specifies all booking engine tests with `--spec "cypress/e2e/booking-engine/**/*.cy.js"`

### Running with Specific Browser

```bash
npx cypress run --headed --browser chrome --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

### Using Cypress Test Runner (Interactive Mode)

For a more interactive experience:

```bash
npx cypress open --env environmentName=uat
```

This opens the Cypress Test Runner, where you can:
1. Select E2E Testing
2. Choose a browser
3. Navigate to the booking-engine folder
4. Click on specific test files to run them

## Running Specific Test Suites

Instead of running all booking engine tests, you can run specific test suites or individual tests.

### Running a Specific Category of Tests

```bash
# Run all guest booking tests
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/B1 D0 guestNoLogin*/**/*.cy.js"

# Run all staff booking tests
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/B1 D0 staff*/**/*.cy.js"

# Run all package tests
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/packages/**/*.cy.js"
```

### Running a Single Test File

```bash
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/B1 D0 guestNoLoginDepositFull/B1 D0 guestNoLoginDepositFull.spec.cy.js"
```

## Handling Test Results

### Viewing Test Results in the Terminal

Test results are displayed in the terminal as tests complete. Each test file will show:
- Number of tests run
- Number of passing, failing, pending, and skipped tests
- Duration of the test run
- Path to the test file

### Generating HTML Reports

For better visualization of test results, you can use Cypress Mochawesome Reporter:

1. Install the reporter:
```bash
npm install --save-dev cypress-mochawesome-reporter
```

2. Update cypress.config.js to include the reporter:
```javascript
const { defineConfig } = require('cypress');

module.exports = defineConfig({
  reporter: 'cypress-mochawesome-reporter',
  reporterOptions: {
    charts: true,
    reportPageTitle: 'Booking Engine Test Results',
    embeddedScreenshots: true,
    inlineAssets: true,
  },
  e2e: {
    setupNodeEvents(on, config) {
      require('cypress-mochawesome-reporter/plugin')(on);
      // ... other setup code
    },
    // ... other e2e config
  },
});
```

3. Run tests with the reporter:
```bash
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

4. View the HTML report in `cypress/reports/html/index.html`

## Troubleshooting

### Common Issues and Solutions

1. **Connection Timeout**
   - Ensure you have proper network connectivity to the AWS environment
   - Check if the baseUrl in the environment config is correct
   - Increase the timeout values in cypress.config.js:
     ```javascript
     defaultCommandTimeout: 90000,
     requestTimeout: 90000,
     responseTimeout: 90000,
     ```

2. **Authentication Failures**
   - Verify that the staff_email and staff_password in the environment config are correct
   - Check if the user account is active in the UAT environment
   - Ensure the user has the necessary permissions

3. **Element Not Found Errors**
   - The UI might have changed; update the selectors in the test
   - Increase the defaultCommandTimeout in cypress.config.js
   - Add explicit waits for elements that take longer to appear

4. **Browser Launch Failures in Headed Mode**
   - Ensure the specified browser is installed on the system
   - Try using a different browser
   - Check if X11 forwarding is properly configured (for remote servers)

### Running Tests with Debug Logs

To get more detailed logs for troubleshooting:

```bash
DEBUG=cypress:* npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
```

## CI/CD Integration

### AWS CodeBuild Configuration

To run tests in AWS CodeBuild, create a buildspec.yml file:

```yaml
version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 14
    commands:
      - npm install
  pre_build:
    commands:
      - echo Installing Chrome...
      - curl -sS -o - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
      - echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
      - apt-get update
      - apt-get install -y google-chrome-stable
  build:
    commands:
      - echo Running Cypress tests...
      - npx cypress run --browser chrome --env environmentName=uat --spec "cypress/e2e/booking-engine/**/*.cy.js"
  post_build:
    commands:
      - echo Tests completed on `date`

artifacts:
  files:
    - cypress/reports/**/*
    - cypress/screenshots/**/*
  discard-paths: no
```

### GitHub Actions Configuration

To run tests in GitHub Actions, create a .github/workflows/cypress.yml file:

```yaml
name: Cypress Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      
      - name: Cypress run
        uses: cypress-io/github-action@v4
        with:
          browser: chrome
          spec: cypress/e2e/booking-engine/**/*.cy.js
        env:
          CYPRESS_environmentName: uat
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v2
        if: always()
        with:
          name: cypress-artifacts
          path: |
            cypress/reports
            cypress/screenshots
```

## Conclusion

This guide provides comprehensive instructions for running Cypress E2E booking engine tests in AWS environments like UAT, in both headed and headless modes. By following these steps, you can ensure consistent and reliable test execution across different environments.
