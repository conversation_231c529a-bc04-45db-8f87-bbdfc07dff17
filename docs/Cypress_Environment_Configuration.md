# Cypress Environment Configuration Guide

This guide explains how to configure environment-specific settings for Cypress tests and why these files are excluded from version control.

## Table of Contents

1. [Environment Files Overview](#environment-files-overview)
2. [Why Environment Files are Excluded from Git](#why-environment-files-are-excluded-from-git)
3. [Setting Up Your Local Environment](#setting-up-your-local-environment)
4. [Available Environment Configurations](#available-environment-configurations)
5. [Environment Variables Reference](#environment-variables-reference)
6. [Switching Between Environments](#switching-between-environments)

## Environment Files Overview

Cypress environment files are JSON configuration files located in the `cypress/env/` directory. Each file represents a different environment configuration (local, development, staging, etc.) and contains settings specific to that environment.

The main configuration file, `cypress.config.js`, loads the appropriate environment file based on the `environmentName` parameter.

## Why Environment Files are Excluded from Git

Environment files are excluded from version control for several important reasons:

1. **Security**: These files often contain sensitive information such as:
   - API keys and tokens
   - Usernames and passwords
   - Database connection strings
   - Internal URLs and endpoints

2. **Environment-specific configuration**: Different team members may need different local configurations based on their setup.

3. **Preventing conflicts**: Environment files change frequently based on local development needs and shouldn't be tracked to avoid unnecessary merge conflicts.

4. **Reducing repository size**: Maintaining multiple environment configurations in version control can unnecessarily increase the repository size.

## Setting Up Your Local Environment

To set up your environment:

1. Edit the environment `XXXXX.json` file with your specific configuration:
   {
       "baseUrl": "http://localhost:58000",
       "video": false,
       "env": {
           "staff_name": "Your Name",
           "staff_email": "<EMAIL>",
           "staff_password": "YourPassword",
           "environment": "Local",
           "zonalAPIUsername": "your-api-username",
           "zonalAPIPassword": "your-api-password"
       }
   }
   ```

3. Added the following files to `.gitignore`
   ```
   # Cypress environment files
   cypress/env/*.json
   !cypress/env/example.json
   cypress.env.json
   ```

## Available Environment Configurations

framework includes several predefined environment configurations:

- `local.json`: For local development (http://localhost:58000)
- `development.json`: For the development environment
- `staging.json`: For the staging environment
- `uat.json`: For the UAT environment
- `test.json`: For the test environment
- `alpha.json`: For the alpha environment

## Environment Variables Reference

The environment files can include the following settings:

### Top-Level Settings

| Setting | Description | Example |
|---------|-------------|---------|
| `baseUrl` | The base URL for the application | `"http://localhost:58000"` |
| `video` | Whether to record videos of test runs | `false` |
| `screenshotOnRunFailure` | Whether to take screenshots on test failure | `true` |
| `trashAssetsBeforeRuns` | Whether to delete assets before each run | `true` |

### Environment Variables (`env` object)

| Variable | Description | Example |
|----------|-------------|---------|
| `staff_name` | Staff member name for login | `"Chris Maggs"` |
| `staff_email` | Staff email for login | `"<EMAIL>"` |
| `staff_password` | Staff password for login | `"Password123?"` |
| `environment` | Environment name | `"Local"` |

## Switching Between Environments

To run tests against a specific environment, use the `environmentName` parameter:
### Running Tests in Headless Mode

You can use predefined npm scripts from your `package.json` to run tests against different environments in headless mode.

**Example npm scripts (from `package.json`):**
```json
// In package.json "scripts":
//   "cy:local": "npx cypress run --headless --env environmentName=local",
//   "cy:uat": "npx cypress run --headless --env environmentName=uat",
//   "cy:develop": "npx cypress run --headless --env environmentName=development",
//   "cy:scan": "npx cypress run --headless --env environmentName=scan",
//   "cy:test": "npx cypress run --headless --env environmentName=test"
//   "cy:alpha": "npx cypress run --headless --env environmentName=alpha"
//   "cy:stage": "Cypress_tags=P2Sanity npx cypress run --headless --env environmentName=staging"
```

To execute these scripts, use `npm run <script-name>`:
```bash
npm run cy:local
npm run cy:uat
```

> **NOTE (12/06):** Majority of the Cypress scripts are currently under review and maintenance. For now, avoid running the full pack using the general scripts above. Instead, execute specific suites or categories of tests (folder level) as shown below.

### Specifying Test Suites (Folders) in Headless Mode

To run all tests within a specific folder (e.g., `booking-engine`):
```bash
npx cypress run --spec "cypress/e2e/booking-engine/**/*.cy.js" --env environmentName=local
```

To run tests within another folder (e.g., `P1`):
```bash
npx cypress run --spec "cypress/e2e/P1/**/*.cy.js" --env environmentName=local
```
*Remember to always append `--env environmentName=<your_env_here>` to specify the target environment when not using a pre-configured npm script.*

### Specifying Individual Test Files in Headless Mode

To run a single test file:
```bash
npx cypress run --spec "cypress/e2e/booking-engine/B1 D0 calendarReservation/B1 D1 calendarReservationBlock.spec.cy.js" --browser chrome --env environmentName=local
```
> *Tip: You may find it useful to re-run failed tests individually due to known reseeding/spin up issues.*

### Running Tagged Tests in Headless Mode

If your project uses a tagging system (e.g., with a plugin like `cypress-grep`), you can run tests based on tags.

**Example using an npm script (from `package.json`):**
The `cy:stage` script runs tests tagged `P2Sanity` in the staging environment:
```json
// "cy:stage": "Cypress_tags=P2Sanity npx cypress run --headless --env environmentName=staging"
```
Run it with:
```bash
npm run cy:stage
```

**Example (direct command for multiple tags - Future Development):**
```bash
Cypress_tags=BookingEngine,P1Critical npx cypress run --env environmentName=local
```
> *NOTE: This requires the tagging plugin to be configured to handle multiple tags.*

### Running Tests in Headed Mode (Opening the Cypress Test Runner)

To open the Cypress Test Runner UI, which allows you to see tests run in real-time and use debugging tools:
```bash
npx cypress open
```
This command opens the Test Runner with the default environment settings from `cypress.config.js`.

You can also open the Test Runner pre-configured for a specific environment using the `*edit` scripts in `package.json` (e.g., `npm run cy:localedit`) or by passing the environment directly:
```bash
npx cypress open --env environmentName=local
```

Once the Cypress Runner is open:
1.  Select ‘E2E Testing’.
2.  Choose a Browser.
3.  Click ‘Start E2E Testing’.
4.  Select a specific test file to run it, or run all specs.

## Best Practices

1. **Never commit sensitive credentials** to version control
2. **Use environment variables** for sensitive information when possible
3. **Keep the example.json file updated** with the correct structure but dummy values
4. **Document any new environment variables** added to the configuration

---