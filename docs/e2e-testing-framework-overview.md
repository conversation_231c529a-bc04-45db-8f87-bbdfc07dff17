# E2E Testing Framework Overview

This document provides a comprehensive overview of the end-to-end (E2E) testing framework used in the project. It covers the framework structure, organization, key components, and best practices.

## Framework Structure

The E2E testing framework is built using Cypress and follows a structured organization to maintain clarity and scalability. The main components are:

```
/cypress
  /e2e                     # End-to-end test files
    /001 Default           # Basic functionality tests
    /002 account-management # Account-related tests
    /003 room-management   # Room management tests
    /004 booking-management # Booking-related tests
    /...                   # Other functional areas
    /features              # Feature-specific tests
      /payments            # Payment-related tests
      /50040-notes         # Notes feature tests
      /...                 # Other feature tests
    /booking-engine        # Booking engine tests
  /fixtures                # Test data
  /support                 # Support files
    /pageObjectModel       # Page Object Models
      /Pages               # Page definitions
    /commands              # Custom Cypress commands
```

## Test Organization

Tests are organized in a hierarchical structure:

1. **Functional Areas**: Tests are grouped by major functional areas (e.g., booking management, room management)
2. **Features**: Feature-specific tests are organized in the `/features` directory
3. **Test Types**: Within each area, tests are further categorized by test type or specific functionality

The naming convention for test files is:
- `[functionality].spec.cy.js` for general tests
- `[specific-scenario].spec.cy.js` for scenario-specific tests

## Key Components

### 1. Page Object Models

The framework uses the Page Object Model pattern to encapsulate page-specific elements and actions:

```javascript
export const bookingsPage = {
    selectors: {
        firstExistingBookingRef: '[data-cy="booking_0"]',
        // Other selectors
    },

    clickFirstExistingBooking: () => {
        cy.get(bookingsPage.selectors.firstExistingBookingRef).click()
    },

    // Other methods
}
```

Key page objects include:
- `bookingsPage`: Handles interactions with the bookings list page
- `bookingHubPage`: Handles interactions with the booking details page
- `payByLinkForm`: Handles interactions with the Pay by Link form
- And many others for different parts of the application

### 2. Custom Commands

Custom Cypress commands extend functionality for common operations:

```javascript
// Example custom command
Cypress.Commands.add('fn_login', (userType, email, password, hotelSlug, options = {}) => {
    // Login implementation
});
```

Key custom commands include:
- `fn_login`: Handles authentication
- `fn_logout`: Handles logging out
- `fn_safeVisit`: Safely navigates to URLs
- `iframe`: Provides enhanced iframe handling

### 3. Test Filters

Tests can be filtered using the `TestFilters` utility:

```javascript
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {
    describe('Booking Management: Corporation cannot edit a Booking', () => {
        // Test implementation
    });
});
```

Common filter categories include:
- `P1`, `P2Sanity`: Priority levels
- `Booking Engine`: Booking engine tests
- Feature-specific filters

### 4. Test Data Management

Test data is managed through:
- **Dynamic Reseeding**: Resetting the test environment to a known state using the `dynamicReseed` command
- **Fixtures**: JSON files in the `/fixtures` directory
- **Environment Variables**: Configured in `cypress.config.js`
- **Dynamic Data Creation**: Creating test data during test execution

The dynamic reseeding process is particularly important as it ensures each test starts with a clean, predictable environment. See the [Dynamic Reseeding Guide](./dynamic-reseeding-guide.md) for more details.

## Test Structure

Tests follow a consistent structure:

```javascript
describe('Feature: Specific functionality', () => {
    before('setup', () => {
        // Setup code
    });

    it('should perform specific action', () => {
        // Test steps
        // Assertions
    });

    // More test cases

    after('cleanup', () => {
        // Cleanup code
    });
});
```

Key elements in test structure:
- `describe` blocks for grouping related tests
- `before`/`beforeEach` hooks for setup
- `it` blocks for individual test cases
- `after`/`afterEach` hooks for cleanup

## Best Practices

### 1. Test Independence

Tests are designed to be independent and self-contained:
- Each test creates its own test data when needed
- Tests don't rely on the state from previous tests
- Proper cleanup is performed after tests

### 2. Robust Selectors

Multiple selector strategies are used to improve test reliability:

```javascript
// Example of robust selector strategy
const checkboxSelectors = [
    'input[type="checkbox"][name*="email"]',
    'input[type="checkbox"][id*="email"]',
    // Other selectors
];

// Try each selector
for (const selector of checkboxSelectors) {
    if ($body.find(selector).length > 0) {
        cy.get(selector).first().check({ force: true });
        break;
    }
}
```

### 3. Error Handling

Tests include robust error handling:
- Fallback strategies when elements aren't found
- Detailed logging for troubleshooting
- Screenshots at key points in the test flow

### 4. Visual Verification

Strategic screenshots are taken to aid in debugging:

```javascript
// Take screenshots at key points
cy.screenshot('before-payment-submission');
cy.get('button[type="submit"]').click();
cy.screenshot('after-payment-submission');
```

## Test Coverage

The framework provides comprehensive coverage across different areas:

1. **Account Management**: User registration, login, permissions
2. **Room Management**: Room types, room configuration, availability
3. **Booking Management**: Creating, editing, canceling bookings
4. **Payment Processing**: Various payment methods and gateways
5. **Booking Engine**: End-to-end booking flows
6. **Feature-Specific Tests**: Tests for specific features and user stories

## Conclusion

The E2E testing framework provides a robust foundation for testing the application across different functional areas. By following the established patterns and best practices, the framework ensures reliable and maintainable tests that effectively validate application functionality.

For more detailed information on specific areas, refer to the following documentation:
- [Test Structure and Organization](./e2e-testing-structure.md)
- [Page Object Models](./e2e-testing-page-objects.md)
- [Custom Commands](./e2e-testing-commands.md)
- [Test Execution Guide](./cypress-test-execution-guide.md)
- [Dynamic Reseeding Guide](./dynamic-reseeding-guide.md)
- [Payment Testing](./payment-testing-framework-overview.md)
- [Booking Engine Testing](./booking-engine-testing.md)
- [Test Coverage Analysis](./e2e-testing-coverage.md)
- [Best Practices](./e2e-testing-best-practices.md)
- [Future Improvements](./e2e-testing-future-improvements.md)
