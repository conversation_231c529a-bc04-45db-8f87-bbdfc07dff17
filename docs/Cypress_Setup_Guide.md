# Cypress Setup Guide

This guide provides comprehensive instructions for setting up Cypress for the test automation framework, including installation, configuration, and package management.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Node.js Installation](#nodejs-installation)
3. [Cypress Installation](#cypress-installation)
4. [Required Packages](#required-packages)
5. [Configuration](#configuration)
6. [Running Cypress Tests](#running-cypress-tests)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

Before setting up Cypress, ensure you have the following:

1. **Linux/Ubuntu environment** (WSL is supported)
2. **Git** for version control
3. **Chrome or Firefox browser** installed
4. **Sufficient disk space** (at least 1GB for Cypress and dependencies)

## Node.js Installation

Cypress requires Node.js (v14 or higher). We recommend using Node.js v23 via NVM (Node Version Manager) for optimal compatibility.

### Installing NVM

```bash
# Download and install NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.5/install.sh | bash

# Reload shell configuration
source ~/.bashrc  # or source ~/.zshrc if using zsh

# Verify NVM installation
nvm --version
```

### Installing Node.js v23

```bash
# Install Node.js v23
nvm install 23

# Set Node.js v23 as default
nvm alias default 23

# Verify Node.js installation
node --version
npm --version
```

## Cypress Installation

### Project Setup

First, navigate to your test automation project directory:

```bash
cd /var/www/test-automation
```

### Installing Cypress

There are two ways to install Cypress:

#### Method 1: Using package.json (Recommended)

If your project already has a package.json file with Cypress listed as a dependency:

```bash
# Clean install all dependencies
npm ci
```

If you need to create a new package.json or add Cypress:

```bash
# Initialize package.json if it doesn't exist
npm init -y

# Install Cypress as a dev dependency
npm install cypress --save-dev
```

#### Method 2: Direct Installation

```bash
# Install Cypress globally (not recommended for project-based testing)
npm install -g cypress
```

### Verifying Installation

```bash
# Using locally installed Cypress
npx cypress --version

# Or if installed globally
cypress --version
```

## Required Packages

The following packages are required for the test automation framework:

```bash
# Install all required packages
npm install --save-dev \
  @cypress/xpath \
  cypress-file-upload \
  cypress-iframe \
  cypress-mochawesome-reporter \
  cypress-multi-reporters \
  cypress-real-events \
  cypress-xpath \
  dayjs \
  mocha \
  mochawesome \
  mochawesome-merge \
  mochawesome-report-generator
```

### Package Descriptions

- **@cypress/xpath**: Adds XPath support to Cypress
- **cypress-file-upload**: Enables file upload testing
- **cypress-iframe**: Provides commands for working with iframes
- **cypress-mochawesome-reporter**: Generates detailed test reports
- **cypress-multi-reporters**: Allows using multiple reporters
- **cypress-real-events**: Simulates real user events like hover
- **cypress-xpath**: Alternative XPath implementation
- **dayjs**: Date manipulation library
- **mocha**: Test framework used by Cypress
- **mochawesome**: Reporter for Mocha
- **mochawesome-merge**: Merges multiple Mochawesome reports
- **mochawesome-report-generator**: Generates HTML reports from Mochawesome data

## Configuration

### Basic Configuration

Cypress configuration is stored in `cypress.config.js` in the project root:

```javascript
const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:58000',
    specPattern: 'cypress/e2e/**/*.spec.cy.js',
    supportFile: 'cypress/support/e2e.js',
    viewportWidth: 1280,
    viewportHeight: 720,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 30000,
    video: false,
    screenshotOnRunFailure: true,
    chromeWebSecurity: false,
    experimentalSessionAndOrigin: true,
  },
  env: {
    environmentName: 'local',
  },
});
```

### Environment-Specific Configuration

For different environments (local, dev, UAT, prod), you can create environment-specific configuration files:

```bash
# Create environment config files
touch cypress.env.local.json
touch cypress.env.dev.json
touch cypress.env.uat.json
touch cypress.env.prod.json
```

Example `cypress.env.local.json`:

```json
{
  "baseUrl": "http://localhost:58000",
  "apiUrl": "http://localhost:58000/api",
  "username": "<EMAIL>",
  "password": "Password123"
}
```

## Running Cypress Tests

### Opening Cypress Test Runner

```bash
# Open Cypress Test Runner in interactive mode
npm run cy:open

# Or using npx
npx cypress open
```

### Running Tests in Headless Mode

```bash
# Run all tests headlessly
npm run cy:run

# Or using npx
npx cypress run
```

### Running Specific Tests

```bash
# Run a specific test file
npx cypress run --spec "cypress/e2e/path/to/test.spec.cy.js"

# Run tests matching a pattern
npx cypress run --spec "cypress/e2e/**/*login*.spec.cy.js"
```

### Running Tests in Different Environments

```bash
# Run tests in a specific environment
npx cypress run --env environmentName=uat
```

### Running Tests with Different Browsers

```bash
# Run tests with Chrome
npx cypress run --browser chrome

# Run tests with Firefox
npx cypress run --browser firefox
```

## Troubleshooting

### Common Issues and Solutions

1. **Dependency Installation Failures**:
   ```bash
   # Clear npm cache
   npm cache clean --force
   
   # Try installing with legacy peer deps flag
   npm install --legacy-peer-deps
   ```

2. **Browser Detection Issues**:
   ```bash
   # Install required dependencies for browsers
   sudo apt-get install libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb
   ```

3. **Permission Issues**:
   ```bash
   # Fix permissions
   sudo chown -R $(whoami) $(npm config get prefix)/{lib/node_modules,bin,share}
   ```

4. **Cypress Binary Download Issues**:
   ```bash
   # Set Cypress binary cache directory
   export CYPRESS_CACHE_FOLDER=~/cypress_cache
   
   # Verify Cypress binary
   npx cypress verify
   ```

5. **Memory Issues**:
   ```bash
   # Increase Node.js memory limit
   export NODE_OPTIONS=--max-old-space-size=4096
   ```

### Debugging Cypress Tests

1. **Enable Debugging Logs**:
   ```bash
   # Run with debug logs
   DEBUG=cypress:* npx cypress run
   ```

2. **Use Cypress Dashboard**:
   ```bash
   # Record tests to Cypress Dashboard
   npx cypress run --record --key your-project-key
   ```

3. **Increase Timeouts**:
   ```bash
   # Run with increased timeouts
   npx cypress run --config defaultCommandTimeout=10000,responseTimeout=30000
   ```

---

By following this guide, you should have Cypress and all required packages installed and configured for the test automation framework.
