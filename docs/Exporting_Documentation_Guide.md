# Exporting Documentation Guide

This guide provides instructions for downloading and exporting Markdown documentation files from the test automation project.

## Table of Contents

1. [Command Line Methods](#command-line-methods)
   - [Using SCP](#using-scp)
   - [Using RSYNC](#using-rsync)
   - [Using SFTP](#using-sftp)
2. [Batch Exporting](#batch-exporting)
   - [Exporting All Documentation](#exporting-all-documentation)
   - [Exporting Specific Documentation](#exporting-specific-documentation)
3. [Converting to Other Formats](#converting-to-other-formats)
   - [Converting to PDF](#converting-to-pdf)
   - [Converting to HTML](#converting-to-html)
   - [Converting to DOCX](#converting-to-docx)
4. [Creating Documentation Archives](#creating-documentation-archives)
5. [Automating Documentation Export](#automating-documentation-export)

## Command Line Methods

### Using SCP

Secure Copy Protocol (SCP) is a simple way to download files from a remote server to your local machine.

**From Remote Server to Local Machine:**

```bash
# Basic syntax
scp username@remote_host:/path/to/remote/file /path/to/local/destination

# Example: Download a single documentation file
scp username@server:/var/www/test-automation/docs/IntelliJ_IDEA_Installation_Guide.md ~/Downloads/

# Example: Download all documentation files
scp username@server:/var/www/test-automation/docs/*.md ~/Downloads/docs/
```

**From Local Machine to Remote Server:**

```bash
# Basic syntax
scp /path/to/local/file username@remote_host:/path/to/remote/destination

# Example: Upload a documentation file
scp ~/Documents/MyGuide.md username@server:/var/www/test-automation/docs/
```

### Using RSYNC

RSYNC is more powerful than SCP and can synchronize directories while preserving file attributes.

```bash
# Basic syntax
rsync [options] source destination

# Example: Download all documentation files
rsync -avz username@server:/var/www/test-automation/docs/ ~/Downloads/docs/

# Example: Download only Markdown files
rsync -avz --include="*.md" --exclude="*" username@server:/var/www/test-automation/docs/ ~/Downloads/docs/
```

Common RSYNC options:
- `-a`: Archive mode (preserves permissions, timestamps, etc.)
- `-v`: Verbose output
- `-z`: Compress data during transfer
- `--progress`: Show progress during transfer

### Using SFTP

SFTP (SSH File Transfer Protocol) provides an interactive way to download files.

```bash
# Connect to the server
sftp username@server

# Navigate to the documentation directory
cd /var/www/test-automation/docs

# List files
ls

# Download a single file
get IntelliJ_IDEA_Installation_Guide.md

# Download multiple files
mget *.md

# Exit SFTP
exit
```

## Batch Exporting

### Exporting All Documentation

To export all documentation files at once, you can create a simple script:

```bash
#!/bin/bash

# Create a local directory for the documentation
mkdir -p ~/Downloads/test-automation-docs

# Download all Markdown files from the docs directory
rsync -avz username@server:/var/www/test-automation/docs/*.md ~/Downloads/test-automation-docs/

echo "All documentation files have been downloaded to ~/Downloads/test-automation-docs/"
```

Save this script as `download-docs.sh`, make it executable with `chmod +x download-docs.sh`, and run it with `./download-docs.sh`.

### Exporting Specific Documentation

To export specific documentation files based on keywords or categories:

```bash
#!/bin/bash

# Create a local directory for the documentation
mkdir -p ~/Downloads/test-automation-docs

# Download only files related to a specific topic (e.g., Gradle)
rsync -avz username@server:"/var/www/test-automation/docs/*Gradle*.md" ~/Downloads/test-automation-docs/

echo "Gradle-related documentation files have been downloaded to ~/Downloads/test-automation-docs/"
```

## Converting to Other Formats

### Converting to PDF

You can use Pandoc to convert Markdown files to PDF:

1. **Install Pandoc and LaTeX:**

```bash
# On Ubuntu/Debian
sudo apt-get update
sudo apt-get install pandoc texlive-latex-base texlive-fonts-recommended texlive-extra-utils texlive-latex-extra
```

2. **Convert a single file:**

```bash
pandoc -s IntelliJ_IDEA_Installation_Guide.md -o IntelliJ_IDEA_Installation_Guide.pdf
```

3. **Convert all files in a directory:**

```bash
for file in *.md; do
  pandoc -s "$file" -o "${file%.md}.pdf"
done
```

### Converting to HTML

Convert Markdown to HTML for web viewing:

```bash
# Convert a single file
pandoc -s IntelliJ_IDEA_Installation_Guide.md -o IntelliJ_IDEA_Installation_Guide.html

# Convert all files in a directory
for file in *.md; do
  pandoc -s "$file" -o "${file%.md}.html"
done
```

### Converting to DOCX

Convert Markdown to Microsoft Word format:

```bash
# Convert a single file
pandoc -s IntelliJ_IDEA_Installation_Guide.md -o IntelliJ_IDEA_Installation_Guide.docx

# Convert all files in a directory
for file in *.md; do
  pandoc -s "$file" -o "${file%.md}.docx"
done
```

## Creating Documentation Archives

You can create a compressed archive of all documentation files:

```bash
# Create a ZIP archive
cd /var/www/test-automation
zip -r docs.zip docs/*.md

# Create a TAR.GZ archive
cd /var/www/test-automation
tar -czvf docs.tar.gz docs/*.md
```

To download the archive:

```bash
# Download the ZIP archive
scp username@server:/var/www/test-automation/docs.zip ~/Downloads/

# Download the TAR.GZ archive
scp username@server:/var/www/test-automation/docs.tar.gz ~/Downloads/
```

## Automating Documentation Export

You can set up a cron job to automatically export documentation on a schedule:

1. **Create an export script:**

```bash
#!/bin/bash

# Set variables
REMOTE_USER="username"
REMOTE_SERVER="server"
REMOTE_PATH="/var/www/test-automation/docs"
LOCAL_PATH="/path/to/local/backup/docs"
DATE=$(date +%Y-%m-%d)

# Create a dated directory
mkdir -p "$LOCAL_PATH/$DATE"

# Download all documentation files
rsync -avz "$REMOTE_USER@$REMOTE_SERVER:$REMOTE_PATH/*.md" "$LOCAL_PATH/$DATE/"

# Create an archive
cd "$LOCAL_PATH"
tar -czvf "docs-$DATE.tar.gz" "$DATE"

# Optional: Remove the dated directory to save space
rm -rf "$LOCAL_PATH/$DATE"

echo "Documentation backup completed for $DATE"
```

2. **Set up a cron job to run the script weekly:**

```bash
# Edit crontab
crontab -e

# Add this line to run the script every Sunday at 1 AM
0 1 * * 0 /path/to/export-docs.sh
```

---

By following this guide, you should be able to download, export, and convert documentation files from the test automation project in various ways to suit your needs.
