# IntelliJ IDEA Installation Guide for Ubuntu WSL

This guide provides step-by-step instructions for installing IntelliJ IDEA on Ubuntu WSL (Windows Subsystem for Linux) for use with the test automation framework.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Installation Methods](#installation-methods)
   - [Method 1: Using the Snap Package](#method-1-using-the-snap-package)
   - [Method 2: Using the JetBrains Toolbox](#method-2-using-the-jetbrains-toolbox)
   - [Method 3: Manual Installation](#method-3-manual-installation)
3. [Post-Installation Setup](#post-installation-setup)
4. [Configuring IntelliJ IDEA for Test Automation](#configuring-intellij-idea-for-test-automation)
5. [Troubleshooting](#troubleshooting)
6. [Running IntelliJ IDEA](#running-intellij-idea)

## Prerequisites

Before installing IntelliJ IDEA, ensure you have the following:

1. **Ubuntu WSL** installed and configured on your Windows system
2. **Java Development Kit (JDK)** installed (version 11 or higher recommended)
3. **X Server** for Windows (such as VcXsrv, Xming, or X410) if you want to run IntelliJ IDEA with GUI
4. **Sufficient disk space** (at least 2.5 GB for the IDE and additional space for projects)

To check if Java is installed:

```bash
java -version
```

If Java is not installed, install it using:

```bash
sudo apt update
sudo apt install openjdk-11-jdk
```

## Installation Methods

### Method 1: Using the Snap Package

This is the simplest method if your WSL distribution supports snap:

```bash
# Install snapd if not already installed
sudo apt update
sudo apt install snapd

# Install IntelliJ IDEA Community Edition
sudo snap install intellij-idea-community --classic

# Or for Ultimate Edition (requires license)
sudo snap install intellij-idea-ultimate --classic
```

### Method 2: Using the JetBrains Toolbox

The JetBrains Toolbox App helps you manage JetBrains tools easily:

1. Download the JetBrains Toolbox App:

```bash
wget https://download.jetbrains.com/toolbox/jetbrains-toolbox-1.28.1.15219.tar.gz
```

2. Extract the archive:

```bash
tar -xzf jetbrains-toolbox-1.28.1.15219.tar.gz
```

3. Run the JetBrains Toolbox App:

```bash
cd jetbrains-toolbox-1.28.1.15219
./jetbrains-toolbox
```

4. Use the Toolbox App to install IntelliJ IDEA.

### Method 3: Manual Installation

This method involves downloading and extracting the IntelliJ IDEA package manually:

1. Create a directory for JetBrains products:

```bash
sudo mkdir -p /var/www/jetbrains
```

2. Download IntelliJ IDEA:

```bash
wget https://download.jetbrains.com/idea/ideaIC-2023.2.5.tar.gz -O /tmp/idea.tar.gz
```

3. Extract the archive to the installation directory:

```bash
sudo tar -xzf /tmp/idea.tar.gz -C /var/www/jetbrains
```

4. Create a symbolic link to the native launcher (recommended):

```bash
sudo ln -s /var/www/jetbrains/idea-IC-232.10227.8/bin/idea /usr/local/bin/intellij-idea
```

> **Note**: Using the native launcher (`bin/idea`) instead of the script launcher (`bin/idea.sh`) provides a better experience and avoids warnings in the IDE.

## Post-Installation Setup

After installing IntelliJ IDEA, you should:

1. **Set up environment variables**:

Add the following to your `~/.bashrc` or `~/.zshrc` file:

```bash
export IDEA_HOME=/var/www/jetbrains/idea-IC-232.10227.8
export PATH=$PATH:$IDEA_HOME/bin
```

Then reload your shell configuration:

```bash
source ~/.bashrc  # or source ~/.zshrc
```

2. **Configure display for WSL**:

Add the following to your `~/.bashrc` or `~/.zshrc` file:

```bash
export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
```

3. **Install required plugins**:

- Gradle plugin
- TestNG plugin
- Selenium plugin
- Java plugin

## Configuring IntelliJ IDEA for Test Automation

1. **Open the test automation project**:

```bash
cd /var/www/test-automation
intellij-idea .  # or use the full path to idea.sh
```

2. **Configure Gradle settings**:

- Go to File > Settings > Build, Execution, Deployment > Build Tools > Gradle
- Set "Build and run using" to "Gradle"
- Set "Run tests using" to "Gradle"
- Set "Gradle JVM" to the same JDK version used by your project

3. **Configure TestNG**:

- Go to File > Settings > Plugins
- Search for "TestNG" and install if not already installed
- Go to File > Settings > Build, Execution, Deployment > Testing > TestNG
- Configure TestNG settings as needed

## Troubleshooting

### Common Issues and Solutions

1. **"Cannot connect to X server" error**:
   - Ensure your X server is running on Windows
   - Verify the DISPLAY environment variable is set correctly
   - Try running `xeyes` to test X server connectivity

2. **"JDK not found" error**:
   - Ensure JDK is installed: `sudo apt install openjdk-11-jdk`
   - Set JAVA_HOME: `export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64`

3. **Performance issues**:
   - Increase memory allocation in `idea.vmoptions`
   - Disable unused plugins
   - Ensure your WSL has sufficient resources allocated

4. **File permission issues**:
   - Run IntelliJ IDEA with sudo if necessary
   - Check file ownership: `chown -R yourusername:yourusername /path/to/project`

5. **"IDE seems to be launched with a script launcher" warning**:
   - This warning appears when you launch IntelliJ IDEA using the `idea.sh` script instead of the native launcher
   - Solution: Use the native launcher instead: `/var/www/jetbrains/idea-IC-232.10227.8/bin/idea`
   - If you created a symbolic link, update it to point to the native launcher:
     ```bash
     sudo ln -sf /var/www/jetbrains/idea-IC-232.10227.8/bin/idea /usr/local/bin/intellij-idea
     ```
   - If the native launcher doesn't work, you can continue using the script launcher despite the warning

6. **Gradle deprecation warnings**:
   - Warning message: "Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0."
   - This is a common warning when using newer versions of IntelliJ IDEA with older Gradle projects
   - To see detailed warnings, run Gradle with the `--warning-mode all` flag:
     ```bash
     ./gradlew build --warning-mode all
     ```
   - For a complete solution, see the [Gradle Troubleshooting Guide](/var/www/test-automation/docs/Gradle_Troubleshooting_Guide.md#gradle-deprecation-warnings)
   - If the build completes successfully, you can continue working despite the warnings

## Running IntelliJ IDEA

You can run IntelliJ IDEA using one of the following methods:

1. **Using the native launcher (recommended)**:

```bash
# Using the native launcher for better experience
/var/www/jetbrains/idea-IC-232.10227.8/bin/idea
```

> **Note**: Using the native launcher (`bin/idea`) instead of the script launcher (`bin/idea.sh`) provides a better experience and avoids warnings in the IDE.

2. **Using the script launcher** (if the native launcher doesn't work):

```bash
# Using the script launcher
/var/www/jetbrains/idea-IC-232.10227.8/bin/idea.sh
```

3. **Opening a project directly**:

```bash
# With native launcher
/var/www/jetbrains/idea-IC-232.10227.8/bin/idea /var/www/test-automation

# Or with symbolic link (if created)
intellij-idea /var/www/test-automation
```

4. **Creating a desktop shortcut** (if using a desktop environment):

Create a file named `intellij-idea.desktop` in `~/.local/share/applications/`:

```
[Desktop Entry]
Version=1.0
Type=Application
Name=IntelliJ IDEA
Icon=/var/www/jetbrains/idea-IC-232.10227.8/bin/idea.png
# Use the native launcher instead of the script launcher
Exec="/var/www/jetbrains/idea-IC-232.10227.8/bin/idea" %f
Comment=Intelligent Java IDE
Categories=Development;IDE;
Terminal=false
StartupWMClass=jetbrains-idea
```

Then make it executable:

```bash
chmod +x ~/.local/share/applications/intellij-idea.desktop
```

---

By following this guide, you should have IntelliJ IDEA installed and configured for use with the test automation framework on Ubuntu WSL.
