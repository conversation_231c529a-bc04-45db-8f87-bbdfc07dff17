# TestNG Framework Setup Guide

This comprehensive guide explains how to set up the TestNG automation framework from scratch. It covers all aspects of the framework, from project structure to running tests.

## Table of Contents

1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Gradle Configuration](#gradle-configuration)
4. [Core Framework Components](#core-framework-components)
5. [Page Object Model](#page-object-model)
6. [Test Organization](#test-organization)
7. [Test Suite Configuration](#test-suite-configuration)
8. [Running Tests](#running-tests)
9. [Example: PMS Staff Login Test](#example-pms-staff-login-test)
10. [Example: Booking Engine Test](#example-booking-engine-test)
11. [Framework Improvements and Best Practices](#framework-improvements-and-best-practices)
12. [Framework Maintenance and Cleanup](#framework-maintenance-and-cleanup)
13. [Recommendations for Future Improvements](#recommendations-for-future-improvements)

## Introduction

The TestNG framework is a test automation framework built on top of TestNG and Selenium WebDriver. It provides a structured approach to creating and running automated tests for web applications. This guide will walk you through setting up the framework from scratch.

## Project Structure

Create the following directory structure for your TestNG framework:

```
testNG/
├── build.gradle                 # Gradle build configuration
├── gradlew                      # Gradle wrapper script for Unix
├── gradlew.bat                  # Gradle wrapper script for Windows
├── gradle/                      # Gradle wrapper files
│   └── wrapper/
│       ├── gradle-wrapper.jar
│       └── gradle-wrapper.properties
├── src/
│   ├── base/                    # Base classes for tests and pages
│   │   ├── BaseTest.java        # Base test class with common test functionality
│   │   └── BasePage.java        # Base page class with common page functionality
│   ├── pages/                   # Page object classes
│   │   ├── groups/              # Group-specific pages
│   │   ├── hotels/              # Hotel-specific pages
│   │   └── root/                # Root-level pages
│   ├── toolbox/                 # Utility classes
│   │   ├── GenericMethods.java  # Common methods for tests
│   │   ├── Data.java            # Data handling utilities
│   │   ├── Report.java          # Reporting utilities
│   │   └── requests/            # API request utilities
│   ├── tests/                   # Test classes
│   │   ├── adhoc/               # Ad-hoc tests
│   │   ├── bugs/                # Bug-specific tests
│   │   ├── pms/                 # PMS-specific tests
│   │   └── prerequisites/       # Prerequisite tests
│   ├── suite/                   # TestNG XML suite files
│   │   ├── feature/             # Feature test suites
│   │   └── regression/          # Regression test suites
│   ├── testdata/                # Test data files
│   │   ├── local-db-build-data/ # Local database test data
│   │   └── feature-final-data/  # Feature test data
│   └── examples/                # Example test classes
├── local.conf.json              # Local configuration file
└── awsProfile.json              # AWS profile configuration
```

## Gradle Configuration

Create a `build.gradle` file with the following configuration:

```gradle
plugins {
  id "com.adarshr.test-logger" version "1.6.0"
  id "java"
}

sourceCompatibility = 11
targetCompatibility = 11
version = '1.0'

testlogger {
    theme 'mocha-parallel'
    showExceptions true
    slowThreshold 10000
    showSummary true
    showPassed true
    showSkipped true
    showFailed true
    showStandardStreams false
    showPassedStandardStreams true
    showSkippedStandardStreams true
    showFailedStandardStreams true
}

repositories {
    mavenCentral()
    jcenter()
    google()
}

dependencies {
    implementation "org.slf4j:slf4j-simple:1.7.36"
    implementation "org.testng:testng:7.7.1"
    implementation "org.seleniumhq.selenium:selenium-java:4.15.0"
    implementation "io.github.bonigarcia:webdrivermanager:5.5.3"
    implementation "org.json:json:20231013"
    implementation "com.googlecode.json-simple:json-simple:1.1.1"
    implementation "com.browserstack:browserstack-local-java:1.0.6"
    implementation "io.rest-assured:rest-assured:5.3.2"
    implementation "com.google.guava:guava:32.1.2-jre"
}

configurations.all {
    resolutionStrategy {
        force 'com.google.guava:guava:32.1.2-jre'
        // Note: The attributes section has been removed to prevent conflicts
        // This simplification allows Gradle to use its default attribute handling
    }
}

sourceSets {
    main {
        java {
            srcDirs = ['src/base',
                       'src/pages/groups',
                       'src/pages/hotels',
                       'src/pages/root',
                       'src/toolbox',
                       'src/toolbox/requests']
        }
    }
    test {
        java {
            srcDirs = ['src/examples',
                       'src/tests',
                       'src/tests/adhoc',
                       'src/tests/bugs',
                       'src/tests/old',
                       'src/tests/page-verifications',
                       'src/tests/payment-gateway',
                       'src/tests/payments',
                       'src/tests/pms',
                       'src/tests/prerequisites',
                       'src/tests/rates',
                       'src/tests/release-3-14-0',
                       'src/tests/wip']
        }
    }
}

test {
    useTestNG()
    outputs.upToDateWhen { false }
}

task smokeTests(type: Test) {
    useTestNG() {
        includeGroups 'smoke'
    }
}

task regressionTests(type: Test) {
    useTestNG() {
        includeGroups 'regression'
    }
}
```

### Gradle Performance Optimization

To improve Gradle performance and avoid common issues like "No connection to gradle server", create a `gradle.properties` file in the root of your project:

```properties
# Gradle memory settings
org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# Gradle daemon settings
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# Kotlin settings
kotlin.incremental=true
```

These settings:
- Increase memory allocation to 2GB
- Enable the Gradle daemon for faster builds
- Enable parallel execution for better performance
- Enable build caching to speed up subsequent builds

### Gradle Wrapper Configuration

The Gradle wrapper allows you to run Gradle without having to install it. Update the Gradle wrapper properties in `gradle/wrapper/gradle-wrapper.properties`:

```properties
distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\://services.gradle.org/distributions/gradle-7.6-bin.zip
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
```

### Troubleshooting Gradle Issues

#### Common Gradle Issues and Solutions

1. **"No connection to gradle server" Error**
   - **Cause**: The IDE's Gradle integration loses connection to the Gradle daemon
   - **Solution**:
     - Click the "Restart" button in the error notification
     - If that doesn't work, kill existing Gradle processes: `pkill -f gradle`
     - Invalidate caches and restart the IDE: File > Invalidate Caches / Restart

2. **Gradle Build Failures**
   - **Cause**: Dependency conflicts or incompatible versions
   - **Solution**:
     - Add explicit resolution strategy in build.gradle:
       ```gradle
       configurations.all {
           resolutionStrategy {
               force 'com.google.guava:guava:32.1.2-jre'
           }
       }
       ```
     - Run with `--refresh-dependencies` flag: `./gradlew clean test --refresh-dependencies`

3. **Out of Memory Errors**
   - **Cause**: Insufficient memory allocation for Gradle
   - **Solution**:
     - Increase memory in gradle.properties:
       ```properties
       org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m
       ```
     - Run with explicit memory settings: `./gradlew test -Dorg.gradle.jvmargs="-Xmx2048m"`

4. **Slow Gradle Builds**
   - **Cause**: Gradle daemon not enabled or insufficient parallelism
   - **Solution**:
     - Enable Gradle daemon in gradle.properties:
       ```properties
       org.gradle.daemon=true
       org.gradle.parallel=true
       ```
     - Use the Gradle daemon: `./gradlew --daemon test`

#### Debugging Gradle

To get more detailed logs for troubleshooting Gradle issues:

```bash
# Run with debug logging
./gradlew test --debug

# Run with info logging
./gradlew test --info

# Run with stacktrace
./gradlew test --stacktrace
```

#### Updating Gradle

If you're experiencing issues with an older Gradle version, update the wrapper:

```bash
# Update to Gradle 7.6
./gradlew wrapper --gradle-version=7.6 --distribution-type=bin
```

## Core Framework Components

### 1. BaseTest Class

Create a `BaseTest.java` file in the `src/base` directory:

```java
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import java.time.Duration;

public class BaseTest {
    protected WebDriver driver;

    // Parameters
    private String browserToUse = "";
    private String headless = "true";
    private String environment = "";
    private String testData = "";
    private String dataToUse = "";
    private String testSuiteName = "";

    Report report = new Report();

    @Parameters({"browserToUse", "headless", "environment", "testData", "dataToUse", "testSuiteName"})
    @BeforeTest(alwaysRun=true)
    public void BeforeTest(String browserToUse, String headless, String environment,
                          @Optional String testData, @Optional String dataToUse,
                          @Optional String testSuiteName) {
        if (browserToUse != null) {setBrowserToUse(browserToUse);}
        if (headless != null) {setHeadless(headless);}
        if (environment != null) {setEnvironment(environment);}
        if (testData != null) {setTestData(testData);}
        if (dataToUse != null) {setDataToUse(dataToUse);}
        if (testSuiteName != null) {setTestSuiteName(testSuiteName);}

        // Setup WebDriver
        if (getBrowserToUse().equals("firefox")) {
            setupFirefoxDriver();
        } else {
            setupChromeDriver();
        }
    }

    @AfterMethod(alwaysRun=true)
    public void AfterMethod() {
        report.appendExport("\n *****\n * End of test\n *****");
        report.exportReport();
        if(null != driver) {
            driver.quit();
        }
    }

    // Getter and setter methods for parameters
    // WebDriver setup methods
}
```

### 2. GenericMethods Class

Create a `GenericMethods.java` file in the `src/toolbox` directory:

```java
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.testng.Assert;
import java.time.Duration;

public class GenericMethods {
    Report report;

    public GenericMethods(Report report) {
        this.report = report;
    }

    // Wait until the page contains the expected URL
    public GenericMethods waitForExpectedURL(WebDriver driver, String expectedURL) {
        new WebDriverWait(driver, Duration.ofSeconds(20))
            .until(ExpectedConditions.urlContains(expectedURL));

        if (driver.getCurrentUrl().contains(expectedURL)) {
            return this;
        } else {
            System.out.println("Did not get expected URL");
            return this;
        }
    }

    // Wait for page to load
    public void waitForPageLoad(WebDriver driver) {
        report.appendExport("INFO: Waiting for page to load.");
        new WebDriverWait(driver, Duration.ofSeconds(30))
            .until(webDriver -> ((org.openqa.selenium.JavascriptExecutor) webDriver)
                .executeScript("return document.readyState").equals("complete"));
    }

    // Wait for element to be visible
    public void waitForElementLocator(WebDriver driver, By element) {
        report.appendExport("INFO: Waiting for element " + element + " to appear on page.");
        new WebDriverWait(driver, Duration.ofSeconds(30))
            .until(ExpectedConditions.visibilityOfElementLocated(element));
    }
}

### 3. Report Class

Create a `Report.java` file in the `src/toolbox` directory:

```java
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class Report {
    String exportSteps = "Start of Report\nSteps:\n";
    String export = "";
    Boolean exportReport = false;
    String location;
    long startTime;

    public Report() {
        this.startTime = System.currentTimeMillis();
    }

    public void setLocation(String location) {
        this.exportReport = true;
        this.location = location;
    }

    public void appendExport(String newData) {
        long time = System.currentTimeMillis() - startTime;
        if(newData.contains("STEP:"))
            this.exportSteps = this.exportSteps + "\n Time: " + time + " | " + newData;
        this.export = this.export + "\n Time: " + time + " | " + newData;
    }

    public String getLocation() {
        return this.location;
    }

    public String getExport() {
        return this.export;
    }

    public String getExportSteps() {
        return this.exportSteps;
    }

    public void exportReport() {
        if(this.exportReport) {
            try {
                File file = new File(getLocation());
                file.getParentFile().mkdirs();
                FileWriter writer = new FileWriter(file);
                writer.write(getExportSteps() + "\n\nFull export:\n" + getExport());
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
```

### 4. Data Class

Create a `Data.java` file in the `src/toolbox` directory:

```java
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;

import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;

public class Data {
    // Parse JSON file and return JSONArray
    @SuppressWarnings("unchecked")
    public JSONArray returnJSONArray(String path, String[] values) {
        String actualPath = System.getProperty("user.dir") + "/" + path;
        JSONParser jsonParser = new JSONParser();
        JSONArray jsonArray = new JSONArray();
        try (FileReader reader = new FileReader(actualPath)) {
            Object obj = jsonParser.parse(reader);
            JSONObject jObj = (JSONObject) obj;
            for(int i=0; i<values.length; i++){
                jsonArray.add(jObj.get(values[i]));
            }
            return jsonArray;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return jsonArray;
        } catch (IOException e) {
            e.printStackTrace();
            return jsonArray;
        } catch (ParseException e) {
            e.printStackTrace();
            return jsonArray;
        }
    }

    // Generate random string
    public String getRandomString(int length, String type) {
        String characters = "";
        if(type.equals("lowercase")) {
            characters = "abcdefghijklmnopqrstuvxyz";
        } else if(type.equals("uppercase")) {
            characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        } else if(type.equals("numeric")) {
            characters = "0123456789";
        } else if(type.equals("any")) {
            characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvxyz";
        }

        StringBuilder randomString = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = (int)(characters.length() * Math.random());
            randomString.append(characters.charAt(index));
        }
        return randomString.toString();
    }
}
```

## Page Object Model

Create page object classes to represent the pages of your application. Each page class should extend the BasePage class.

### 1. BasePage Class

Create a `BasePage.java` file in the `src/base` directory:

```java
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

public class BasePage {
    protected WebDriver driver;
    public WebDriverWait wait;

    public BasePage(WebDriver driver) {
        this.driver = driver;
        driver.manage().timeouts().implicitlyWait(10, TimeUnit.SECONDS);
    }

    public void validatePrivileges() {
        // Override in specific page classes if needed
    }
}
```

### 2. Sample Page Class

Create page classes for each page in your application. For example, a login page:

```java
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

public class LoginPage extends BasePage {
    // Page elements
    private By emailField = By.id("email");
    private By passwordField = By.id("password");
    private By loginButton = By.xpath("//button[contains(text(), 'Login')]");

    public LoginPage(WebDriver driver) {
        super(driver);
    }

    public void login(String email, String password) {
        driver.findElement(emailField).sendKeys(email);
        driver.findElement(passwordField).sendKeys(password);
        driver.findElement(loginButton).click();
    }
}
```

## Test Organization

Organize your tests into logical groups based on functionality or test type.

### 1. Sample Test Class

Create a test class that extends BaseTest:

```java
import org.testng.annotations.Test;

public class LoginTest extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    LoginPage loginPage;

    @Test(groups={"smoke"})
    public void loginTest() {
        // Initialize required classes
        genericMethods = new GenericMethods(getReport());
        loginPage = new LoginPage(driver);

        // Navigate to login page
        driver.get(getEnvironment() + "/login");

        // Perform login
        loginPage.login("<EMAIL>", "password123");

        // Wait for redirect to dashboard
        genericMethods.waitForExpectedURL(driver, "/dashboard");
    }
}
```

## Test Suite Configuration

Create TestNG XML suite files to configure and run your tests.

### 1. Sample TestNG XML Suite File

Create a file `src/suite/feature/login/loginTest.xml`:

```xml
<!--Test suite for Login functionality-->
<suite name="Login Test Suite" verbose="1">
  <parameter name="iteration" value="feature"/>
  <parameter name="browserToUse" value="firefox"/>
  <parameter name="headless" value="true"/>
  <parameter name="environment" value="http://localhost:8000"/>
  <parameter name="testSuiteName" value="login/loginTest"/>
  <test name="Login Test - Test Data 1">
    <parameter name="testData" value="TD1"/>
    <parameter name="dataToUse" value="random"/>
    <groups>
      <run>
        <include name="smoke"/>
      </run>
    </groups>
    <classes>
      <class name="LoginTest"/>
    </classes>
  </test>
</suite>
```

### 2. Configuration Files

Create the following configuration files:

#### local.conf.json

```json
{
  "server": "hub-cloud.browserstack.com",
  "user": "",
  "key": "",

  "capabilities": {
    "name": "Local Test",
    "browserstack.local": true
  },

  "environments": {
    "chrome": {
      "browser": "chrome"
    },
    "firefox": {
      "browser": "firefox"
    }
  }
}
```

## Running Tests

You can run tests using Gradle commands or directly through your IDE.

### 1. Running Tests with Gradle

To run all tests:

```bash
./gradlew clean test
```

To run smoke tests:

```bash
./gradlew clean smokeTests
```

To run regression tests:

```bash
./gradlew clean regressionTests
```

To run a specific test suite:

```bash
./gradlew clean test --tests "LoginTest"
```

### 2. Running Tests with TestNG XML Suites

To run a specific TestNG XML suite:

```bash
./gradlew clean test -Dsuite=src/suite/feature/login/loginTest.xml
```

## Best Practices

1. **Maintain Clean Code**
   - Follow Java coding standards
   - Use meaningful variable and method names
   - Keep methods small and focused on a single task

2. **Page Object Model**
   - Each page should have its own class
   - Page classes should extend BasePage
   - Keep page elements and methods related to that page in the page class

3. **Test Data Management**
   - Store test data in JSON files
   - Use the Data class to read and manipulate test data
   - Generate random data when appropriate

4. **Reporting**
   - Use the Report class to log test steps and results
   - Export reports to files for later analysis
   - Include timestamps in logs

5. **Test Organization**
   - Group tests by functionality
   - Use TestNG groups to categorize tests
   - Create test suites for different test scenarios

6. **Error Handling**
   - Use try-catch blocks for error-prone operations
   - Log errors with detailed information
   - Take screenshots on test failures

7. **Parallel Execution**
   - Configure TestNG XML suites for parallel execution
   - Use thread-safe methods and variables
   - Avoid shared resources between tests

8. **Maintenance**
   - Regularly update dependencies
   - Refactor code to improve maintainability
   - Document changes and improvements

By following this guide, you should be able to set up a robust TestNG automation framework from scratch. The framework provides a structured approach to creating and running automated tests, making it easier to maintain and extend your test suite as your application evolves.

## Example: PMS Staff Login Test

This section provides a step-by-step guide to create and execute a basic test for logging into the PMS as a staff member.

### 1. Create Test Data

Create a JSON file for staff login credentials in `src/testdata/feature-final-data/adhoc/staff-login/TD1Login.json`:

```json
{
  "password": "Password123?",
  "role": "Administrator",
  "phone": "",
  "name": "Staff User",
  "user": "staff-admin",
  "uuid": "1618fb24-6cc4-11e9-bb0c-0242ac180003",
  "email": "<EMAIL>"
}
```

### 2. Create the Test Class

Create a `StaffLoginTest.java` file in the `src/tests/adhoc` directory:

```java
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.testng.annotations.Test;
import io.github.bonigarcia.wdm.WebDriverManager;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class StaffLoginTest extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    StaffLoginPage staffLoginPage;
    HotelsPage hotelsPage;
    Data data;

    String testDataLoginPath = "";

    /**
     * Basic test - Staff Login
     *
     * Navigate to the staff login page
     * Enter valid staff credentials
     * Submit the login form
     * Verify successful login by checking for "Logged in" message
     */
    @Test(groups={"staffLogin"})
    public void staffLogin() throws IOException {
        // Set test case name
        setTestCaseName("staffLogin");

        // Set path to login data
        setTestDataPath();
        testDataLoginPath = getTestDataPath() + "Login.json";

        // Start reporting
        setFailedReportLocation();
        getReport().appendExport(
                "\n *****\n" +
                " * Start of test:" +
                " * Staff Login Test" +
                " *****");

        // Initialize required objects
        genericMethods = new GenericMethods(getReport());
        data = new Data();

        try {
            // Parse login details from test data
            String[] loginValues = new String[]{"email", "password", "user"};
            JSONArray loginDetails = data.returnJSONArray(testDataLoginPath, loginValues);
            String email = loginDetails.get(0).toString();
            String password = loginDetails.get(1).toString();

            // Navigate to staff login page
            getReport().appendExport("STEP: Navigate to the Staff Login page.");
            driver.get(getEnvironment() + "/staff/login");

            // Take screenshot for debugging if needed
            takeDebugScreenshot("staff_login_page");

            // Initialize page objects
            staffLoginPage = new StaffLoginPage(getDriver(), getReport(), getEnvironment());
            staffLoginPage.validatePage();

            // Perform login
            getReport().appendExport("STEP: Perform login with credentials.");
            staffLoginPage.typeEmail(email);
            staffLoginPage.typePassword(password);
            hotelsPage = staffLoginPage.submitLogin();

            // Verify successful login
            getReport().appendExport("STEP: Verify successful login.");
            genericMethods.waitForExpectedURL(driver, "/hotels");
            getReport().appendExport("INFO: " + hotelsPage.returnSuccessMessage());
            genericMethods.checkText(hotelsPage.returnSuccessMessage(), "Logged in");

            // Take screenshot of successful login
            takeDebugScreenshot("successful_login");

            setPassedReportLocation();
        } catch (Exception e) {
            getReport().appendExport("TEST FAILED: " + e.getMessage());
            takeDebugScreenshot("login_failure");
            throw e;
        }
    }

    /**
     * Helper method to take screenshots for debugging
     */
    private void takeDebugScreenshot(String name) throws IOException {
        File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
        Path directory = Paths.get("reports/screenshots");
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }
        Path destination = Paths.get("reports/screenshots/" + name + ".png");
        Files.copy(screenshot.toPath(), destination);
        getReport().appendExport("INFO: Screenshot saved to: " + destination.toAbsolutePath());
    }
}
```

### 3. Create a TestNG XML Suite File

Create a file `src/suite/feature/adhoc/staff-login.xml`:

```xml
<!--Test suite for Staff Login Test -->
<suite name="Staff Login Test" verbose="1">
  <parameter name="iteration" value="feature"/>
  <parameter name="browserToUse" value="chrome"/>
  <parameter name="headless" value="true"/>
  <parameter name="environment" value="http://localhost:58000"/>
  <parameter name="testSuiteName" value="adhoc/staff-login"/>
  <test name="Staff Login Test - Test Data 1">
    <parameter name="testData" value="TD1"/>
    <parameter name="dataToUse" value="random"/>
    <groups>
      <run>
        <include name="staffLogin"/>
      </run>
    </groups>
    <classes>
      <class name="StaffLoginTest"/>
    </classes>
  </test>
</suite>
```

### 4. Create a Simple Debugging Test

For troubleshooting purposes, it's often helpful to create a simpler test that doesn't rely on the full framework. Here's an example:

```java
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import io.github.bonigarcia.wdm.WebDriverManager;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Simple script to take a screenshot of the login page
 */
public class ScreenshotLoginPage {
    WebDriver driver;

    @BeforeMethod
    public void setup() {
        // Initialize WebDriver with WebDriverManager
        WebDriverManager.chromedriver().setup();
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless=new");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        driver = new ChromeDriver(options);
    }

    @Test
    public void takeScreenshot() {
        try {
            // Navigate to staff login page
            driver.get("http://localhost:58000/staff/login");

            // Take screenshot
            File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);

            // Create directory if it doesn't exist
            Path directory = Paths.get("screenshots");
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }

            // Save screenshot
            Path destination = Paths.get("screenshots/login_page.png");
            Files.copy(screenshot.toPath(), destination);

            System.out.println("Screenshot saved to: " + destination.toAbsolutePath());

            // Print page source for debugging
            System.out.println("Page source:");
            System.out.println(driver.getPageSource());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @AfterMethod
    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }
}
```

### 5. Run the Test

You can run the test using Gradle with the following command:

```bash
# Run the full test with the TestNG XML suite
./gradlew clean test -Dsuite=src/suite/feature/adhoc/staff-login.xml

# Run a specific test class directly
./gradlew test --tests "StaffLoginTest"

# Run the simple debugging test
./gradlew test --tests "ScreenshotLoginPage"
```

Alternatively, you can run it directly from your IDE by right-clicking on the XML file or test class and selecting "Run".

### 6. Understanding the Test Flow

1. **Test Setup**: The test extends `BaseTest`, which handles WebDriver initialization and other setup tasks.

2. **Test Data**: The test reads login credentials from a JSON file, making it easy to change credentials without modifying the test code.

3. **Page Navigation**: The test navigates to the staff login page using the WebDriver.

4. **Page Object Model**: The test uses the `StaffLoginPage` class to interact with the login form, following the Page Object Model pattern.

5. **Assertions**: The test verifies successful login by checking for the "Logged in" message and the correct URL.

6. **Reporting**: The test uses the `Report` class to log test steps and results.

7. **Debugging**: The test includes methods for taking screenshots at key points, which is helpful for troubleshooting.

### 7. Troubleshooting

If the test fails, check the following:

- **Credentials**: Ensure the email and password in the test data file are valid.
- **Environment**: Make sure the environment parameter in the XML file points to the correct URL (e.g., http://localhost:58000 instead of the default port 8000).
- **Browser**: If using a specific browser, ensure it's installed and properly configured.
- **WebDriver**: Use WebDriverManager to handle driver setup automatically.
- **Network**: Verify network connectivity to the application server.
- **Application State**: Ensure the application is running and accessible.
- **Element Selectors**: Verify that the element selectors in the page objects match the actual elements on the page.
- **Screenshots**: Examine the debug screenshots to see what the page looks like at different stages of the test.
- **Page Source**: Check the page source to understand the structure of the page and identify the correct selectors.

### 8. Common Issues and Solutions

1. **Connection Refused**: If you see "ERR_CONNECTION_REFUSED", check that the server is running and that you're using the correct port (e.g., 58000 instead of 8000).

2. **Element Not Found**: If elements can't be found, take a screenshot and examine the page structure. You may need to update the selectors in the page objects.

3. **Headless Mode Issues**: Some applications behave differently in headless mode. Try running with a visible browser if you encounter issues.

4. **WebDriver Setup**: Use WebDriverManager to handle driver setup automatically:
   ```java
   WebDriverManager.chromedriver().setup();
   ChromeOptions options = new ChromeOptions();
   options.addArguments("--headless=new");
   driver = new ChromeDriver(options);
   ```

5. **Timeouts**: Adjust wait times if the application is slow to respond:
   ```java
   driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));
   new WebDriverWait(driver, Duration.ofSeconds(30)).until(ExpectedConditions.visibilityOfElementLocated(element));
   ```

The test report will be generated in the location specified by `setPassedReportLocation()` or `setFailedReportLocation()`, depending on whether the test passes or fails.

## Framework Improvements and Best Practices

### Recent Framework Improvements

We've made several improvements to the TestNG framework to make it more robust and easier to use:

1. **WebDriver Management**:
   - Added WebDriverManager for automatic driver setup
   - Updated Chrome and Firefox driver configurations for better stability
   - Added proper implicit wait times using Duration.ofSeconds()

2. **Error Handling and Debugging**:
   - Added try-catch blocks to all test methods
   - Added screenshot capabilities for debugging
   - Improved error reporting with detailed messages

3. **Test Configuration**:
   - Updated XML suite files to use the correct environment URL (58000 instead of 8000)
   - Changed default browser from Firefox to Chrome for better compatibility
   - Added proper test data file handling

4. **Test Execution**:
   - Added support for running tests with specific XML files
   - Added better reporting with screenshots at key points
   - Added support for running tests in parallel

### Debugging Tests

When tests fail, you can use the following debugging techniques:

1. **Screenshots**: The framework now takes screenshots at key points in the test, which can help identify where the test failed.
   ```java
   private void takeDebugScreenshot(String name) throws IOException {
       File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
       Path directory = Paths.get("reports/screenshots");
       if (!Files.exists(directory)) {
           Files.createDirectories(directory);
       }
       // Use timestamp to avoid file conflicts
       Path destination = Paths.get("reports/screenshots/" + name + "_" + System.currentTimeMillis() + ".png");
       Files.copy(screenshot.toPath(), destination);
       getReport().appendExport("INFO: Screenshot saved to: " + destination.toAbsolutePath());
   }
   ```

2. **Error Messages**: The framework now provides detailed error messages that can help identify the cause of the failure.
   ```java
   try {
       // Test code here
   } catch (Exception e) {
       getReport().appendExport("ERROR: Test failed with exception: " + e.getMessage());
       try {
           takeDebugScreenshot("test_failure");
       } catch (IOException ioe) {
           getReport().appendExport("ERROR: Failed to take failure screenshot: " + ioe.getMessage());
       }
       throw e;
   }
   ```

3. **Reports**: The framework generates detailed reports that show the steps executed and any errors encountered.
   ```java
   getReport().appendExport("STEP: Navigate to staff login page.");
   getReport().appendExport("INFO: Page title: " + driver.getTitle());
   getReport().appendExport("INFO: Current URL: " + driver.getCurrentUrl());
   ```

4. **Logs**: The framework logs all actions and errors to the console and report files.

### Known Issues and Solutions

During our testing, we identified several issues and their solutions:

1. **File Path Conflicts**: When taking screenshots, using a fixed filename can cause conflicts if the test is run multiple times. Use a timestamp in the filename to avoid this issue:
   ```java
   Path destination = Paths.get("reports/screenshots/" + name + "_" + System.currentTimeMillis() + ".png");
   ```

2. **Parameter Handling**: Tests may fail with `Parameter is required but has not been marked @Optional or defined` errors. Ensure all required parameters are provided in the XML file or mark them as @Optional with default values:
   ```java
   @Parameters({"browserToUse", "headless", "environment"})
   @BeforeTest(alwaysRun=true)
   public void BeforeTest(@Optional("chrome") String browserToUse,
                          @Optional("true") String headless,
                          @Optional("http://localhost:58000") String environment) {
       // Method implementation
   }
   ```

3. **Test Data Path**: Ensure the test data files are in the correct location. The framework expects test data files to be in a specific directory structure:
   ```
   src/testdata/feature/adhoc/1-adhoc/5-staffAdminLoginSuccess/staffAdminLoginSuccess/TD1Login.json
   ```

4. **Running Specific Tests**: To run a specific test, use the `--tests` parameter with Gradle:
   ```bash
   ./gradlew test --tests "SimpleStaffLoginTest"
   ```
   Or use TestNG directly:
   ```bash
   java -cp "build/classes/java/main:build/classes/java/test:$(find ~/.gradle/caches -name '*.jar' | tr '\n' ':')" org.testng.TestNG src/suite/feature/adhoc/1-adhoc/5-staffAdminLoginSuccess.xml
   ```

### Best Practices

Here are some best practices to follow when working with the TestNG framework:

1. **Use Page Object Model**: Organize your code using the Page Object Model pattern to improve maintainability.
2. **Keep Tests Independent**: Each test should be independent and not rely on the state of other tests.
3. **Use Meaningful Test Names**: Give your tests meaningful names that describe what they are testing.
4. **Use Assertions**: Use assertions to verify that the test is behaving as expected.
5. **Use Test Groups**: Organize your tests into groups to make it easier to run specific sets of tests.
6. **Use Test Parameters**: Use parameters to make your tests more flexible and reusable.
7. **Use Test Data Files**: Store test data in external files to make it easier to update.
8. **Use Reporting**: Use the reporting functionality to track test results.
9. **Use Version Control**: Store your test code in a version control system like Git.
10. **Document Changes**: Document changes to the framework and tests.
11. **Add Screenshots for Debugging**: Take screenshots at key points in the test to help with debugging.
12. **Handle Exceptions Properly**: Use try-catch blocks to handle exceptions and provide meaningful error messages.
13. **Use Proper Wait Mechanisms**: Use explicit and implicit waits to handle synchronization issues.
14. **Keep Test Data Separate**: Store test data in separate files to make it easier to update.
15. **Use Environment Variables**: Use environment variables for sensitive information like passwords.

By following this guide, you should be able to set up a robust TestNG automation framework from scratch. The framework provides a structured approach to creating and running automated tests, making it easier to maintain and extend your test suite as your application evolves.

## Example: Booking Engine Test

This section provides a step-by-step guide to create and execute a test for the booking engine flow, similar to the Cypress test `B1 D0 staffAvailabilityDepositFull.spec.cy.js`.

### 1. Test Structure Overview

The booking engine test follows this flow:
1. Login as staff
2. Navigate to the booking engine
3. Select dates and room
4. Skip extras
5. Fill in guest details
6. Make payment
7. Verify booking details
8. Verify booking appears in various parts of the application

### 2. Create Test Data

Create JSON files for guest details and payment card information:

**src/testdata/booking/guestDetails.json**:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "07123456789",
  "address": {
    "line1": "123 Main Street",
    "line2": "",
    "city": "London",
    "postcode": "SW1A 1AA",
    "country": "United Kingdom"
  }
}
```

**src/testdata/booking/cardDetails.json**:
```json
{
  "cardholderName": "John Doe",
  "cardNumber": "****************",
  "expiryMonth": "12",
  "expiryYear": "25",
  "csc": "123"
}
```

### 3. Create Page Objects

Create page object classes for each page in the booking flow. Here's an example of the `YourStayPage` class:

```java
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;

/**
 * Page object for the Your Stay page in the booking engine
 */
public class YourStayPage extends BasePage {
    // Page elements
    public String pageUrl = "/booking";

    // Selectors
    private By selectDatesButton = By.cssSelector(".select-dates-button");
    private By currentDateCell = By.cssSelector(".flatpickr-day.today");
    private By nextDayDateCell = By.cssSelector(".flatpickr-day.today + .flatpickr-day");
    private By searchButton = By.cssSelector(".search-button");
    private By occupancySearchButton = By.cssSelector(".occupancy-search-button");
    private By twinRoomBox = By.cssSelector(".twin-room-box");
    private By addRatePlanButton = By.cssSelector(".add-rate-plan-button");
    private By continueButton = By.cssSelector(".continue-button");
    private By spinner = By.cssSelector(".spinner");

    /**
     * Constructor
     */
    public YourStayPage(WebDriver driver, Report report, String environment) {
        super(driver, report, environment);
    }

    /**
     * Open the Your Stay page for a specific hotel
     */
    public YourStayPage open(String hotelSlug) {
        driver.get(environment + "/booking/" + hotelSlug);
        report.appendExport("INFO: Opened Your Stay page for hotel: " + hotelSlug);
        return this;
    }

    /**
     * Click the Select Dates button
     */
    public YourStayPage clickSelectDates() {
        report.appendExport("INFO: Clicking Select Dates button");
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(selectDatesButton));
        button.click();
        return this;
    }

    // Additional methods for interacting with the page...
}
```

Similar page objects should be created for:
- ExtrasPage
- GuestDetailsPage
- PayPage
- BookingHubPage
- BookingsPage
- CalendarPage

### 4. Create the Test Class

Create a test class that extends BaseTest and uses the page objects to perform the booking flow:

```java
import org.json.simple.JSONObject;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.testng.annotations.Test;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.Parameters;
import org.testng.annotations.Optional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Calendar;

/**
 * Test class for creating a booking as staff with full deposit
 */
public class StaffAvailabilityDepositFullTest extends BaseTest {
    // Required classes
    GenericMethods genericMethods;
    Data data;
    Report report;

    // Page objects
    StaffLoginPage staffLoginPage;
    YourStayPage yourStayPage;
    ExtrasPage extrasPage;
    GuestDetailsPage guestDetailsPage;
    PayPage payPage;
    BookingHubPage bookingHubPage;
    BookingsPage bookingsPage;
    CalendarPage calendarPage;

    // Test data
    String hotelSlug = "cardiff"; // Using cardiff as it has Barclays payment gateway
    String staffEmail = "<EMAIL>";
    String staffPassword = "Password123?";
    String guestDetailsPath;
    String cardDetailsPath;

    // Date variables
    String currentDate;
    String nextDayDate;

    @Parameters({"browserToUse", "headless", "environment"})
    @BeforeMethod(alwaysRun=true)
    public void setup(@Optional("chrome") String browserToUse,
                     @Optional("true") String headless,
                     @Optional("http://localhost:58000") String environment) {
        // Setup code...
    }

    @Test(groups={"bookingEngine"})
    public void createBookingAsStaffWithFullDeposit() throws IOException {
        try {
            // Login as staff
            report.appendExport("STEP: Login as staff");
            driver.get(getEnvironment() + "/staff/login");

            staffLoginPage = new StaffLoginPage(driver, report, getEnvironment());
            staffLoginPage.validatePage();
            staffLoginPage.typeEmail(staffEmail);
            staffLoginPage.typePassword(staffPassword);
            staffLoginPage.submitLogin();

            takeDebugScreenshot("after_login");

            // Access booking engine as staff
            report.appendExport("STEP: Access booking engine as staff");
            yourStayPage = new YourStayPage(driver, report, getEnvironment());
            yourStayPage.open(hotelSlug);

            // Continue with booking flow...

            report.appendExport("TEST PASSED: Successfully created booking as staff with full deposit");
        } catch (Exception e) {
            report.appendExport("TEST FAILED: " + e.getMessage());
            takeDebugScreenshot("test_failure");
            throw e;
        }
    }

    // Helper methods...
}
```

### 5. Create a TestNG XML Suite File

Create a file `src/suite/feature/booking/staff-availability-deposit-full.xml`:

```xml
<!--Test suite for Staff Availability Deposit Full Test -->
<suite name="Staff Availability Deposit Full Test" verbose="1">
  <parameter name="browserToUse" value="chrome"/>
  <parameter name="headless" value="true"/>
  <parameter name="environment" value="http://localhost:58000"/>
  <parameter name="testSuiteName" value="booking/staff-availability-deposit-full"/>
  <test name="Staff Availability Deposit Full Test">
    <groups>
      <run>
        <include name="bookingEngine"/>
      </run>
    </groups>
    <classes>
      <class name="StaffAvailabilityDepositFullTest"/>
    </classes>
  </test>
</suite>
```

### 6. Run the Test

You can run the test using Gradle with the following command:

```bash
./gradlew test -Dsuite=src/suite/feature/booking/staff-availability-deposit-full.xml
```

Alternatively, you can run it directly with TestNG:

```bash
java -cp "build/classes/java/main:build/classes/java/test:$(find ~/.gradle/caches -name '*.jar' | tr '\n' ':')" org.testng.TestNG src/suite/feature/booking/staff-availability-deposit-full.xml
```

### 7. Simplified Test for Debugging

For debugging purposes, you can create a simplified version of the test that focuses on a specific part of the flow:

```java
import org.openqa.selenium.By;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.Test;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.Parameters;
import org.testng.annotations.Optional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;

/**
 * Simple test class for demonstrating a booking flow
 */
public class SimpleBookingTest extends BaseTest {
    // Required classes
    Report report;
    WebDriverWait wait;

    // Test data
    String hotelSlug = "cardiff";
    String staffEmail = "<EMAIL>";
    String staffPassword = "Password123?";

    @Test(groups={"bookingEngine"})
    public void simpleBookingTest() throws IOException {
        try {
            // Login as staff
            report.appendExport("STEP: Login as staff");
            driver.get(getEnvironment() + "/staff/login");

            // Fill in login form
            WebElement emailField = wait.until(ExpectedConditions.visibilityOfElementLocated(By.id("email")));
            WebElement passwordField = wait.until(ExpectedConditions.visibilityOfElementLocated(By.id("password")));
            WebElement loginButton = wait.until(ExpectedConditions.elementToBeClickable(By.cssSelector("button[type='submit']")));

            emailField.sendKeys(staffEmail);
            passwordField.sendKeys(staffPassword);
            loginButton.click();

            takeDebugScreenshot("after_login");

            // Wait for dashboard to load
            wait.until(ExpectedConditions.urlContains("/hotels"));

            report.appendExport("INFO: Successfully logged in as staff");

            // Navigate to booking engine
            report.appendExport("STEP: Navigate to booking engine");
            driver.get(getEnvironment() + "/booking/" + hotelSlug);

            takeDebugScreenshot("booking_engine");

            report.appendExport("TEST PASSED: Successfully navigated to booking engine");
        } catch (Exception e) {
            report.appendExport("TEST FAILED: " + e.getMessage());
            takeDebugScreenshot("test_failure");
            throw e;
        }
    }

    // Helper methods...
}
```

### 8. Comparison with Cypress Test

The TestNG implementation follows the same flow as the Cypress test `B1 D0 staffAvailabilityDepositFull.spec.cy.js`:

1. **Cypress**:
   ```javascript
   cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
   ```
   **TestNG**:
   ```java
   staffLoginPage.typeEmail(staffEmail);
   staffLoginPage.typePassword(staffPassword);
   staffLoginPage.submitLogin();
   ```

2. **Cypress**:
   ```javascript
   yourStayPage.open(hotelSlug)
   yourStayPage.clickSelectDates()
   yourStayPage.selectCurrentDate()
   yourStayPage.selectNextDayDate()
   .clickSearchButton()
   ```
   **TestNG**:
   ```java
   yourStayPage = new YourStayPage(driver, report, getEnvironment());
   yourStayPage.open(hotelSlug);
   yourStayPage.clickSelectDates();
   yourStayPage.selectCurrentDate();
   yourStayPage.selectNextDayDate();
   yourStayPage.clickSearchButton();
   ```

3. **Cypress**:
   ```javascript
   paymentIframe.fillJudoPayDetails(card)
   ```
   **TestNG**:
   ```java
   payPage.fillPaymentDetails(cardDetailsPath);
   ```

4. **Cypress**:
   ```javascript
   bookingHubPage.assertPaymentStatus('paid')
   bookingHubPage.clickExpandBookingButton()
   ```
   **TestNG**:
   ```java
   bookingHubPage.assertPaymentStatus("paid");
   bookingHubPage.clickExpandBookingButton();
   ```

The main differences are:
- TestNG uses Java instead of JavaScript
- TestNG requires more setup code for WebDriver initialization
- TestNG uses explicit waits for elements to be visible or clickable
- TestNG takes screenshots for debugging purposes
- TestNG has more verbose error handling with try-catch blocks

### 9. Troubleshooting

If you encounter issues with the test, check the following:

- **WebDriver Setup**: Ensure WebDriverManager is properly configured.
- **Element Selectors**: Verify that the selectors in the page objects match the actual elements on the page.
- **Iframe Handling**: For payment forms, ensure you're properly switching to the correct iframe.
- **Wait Times**: Adjust wait times if the application is slow to respond.
- **Screenshots**: Examine the debug screenshots to see what the page looks like at different stages of the test.

## Framework Maintenance and Cleanup

The TestNG framework has been cleaned up to remove old broken tests and unnecessary files. This section describes the cleanup process and the current state of the framework.

### Cleanup Process

The following steps were taken to clean up the framework:

1. **Removed old broken tests and unnecessary files**:
   - Removed tests in the "old" directory
   - Removed tests marked as "wip" (work in progress)
   - Removed other unnecessary test directories (bugs, page-verifications, pms, prerequisites, rates, release-3-14-0)
   - Removed unnecessary output files (test-output, .gradle, build directories)

2. **Kept only the necessary tests**:
   - Kept SimpleStaffLoginTest.java in the adhoc directory
   - Added SimpleBookingTest.java in the booking directory

3. **Added page objects for the booking test**:
   - BasePage.java
   - YourStayPage.java
   - ExtrasPage.java
   - GuestDetailsPage.java
   - PayPage.java
   - BookingHubPage.java
   - BookingsPage.java
   - CalendarPage.java

4. **Added test data files**:
   - guestDetails.json
   - cardDetails.json

5. **Added utility classes**:
   - GenericMethods.java with waitForElementWithText method

6. **Added TestNG XML suite file**:
   - simple-booking-test.xml

### Current Framework Structure

The current structure of the framework is as follows:

```
testNG/
├── src/
│   ├── base/
│   │   ├── BasePage.java
│   │   └── BaseTest.java
│   ├── pages/
│   │   ├── booking/
│   │   │   ├── BasePage.java
│   │   │   ├── BookingHubPage.java
│   │   │   ├── BookingsPage.java
│   │   │   ├── CalendarPage.java
│   │   │   ├── ExtrasPage.java
│   │   │   ├── GuestDetailsPage.java
│   │   │   ├── PayPage.java
│   │   │   └── YourStayPage.java
│   │   └── root/
│   │       └── StaffLoginPage.java
│   ├── suite/
│   │   └── feature/
│   │       ├── adhoc/
│   │       │   └── simple-staff-login.xml
│   │       └── booking/
│   │           └── simple-booking-test.xml
│   ├── testdata/
│   │   └── booking/
│   │       ├── cardDetails.json
│   │       └── guestDetails.json
│   ├── tests/
│   │   ├── adhoc/
│   │   │   └── SimpleStaffLoginTest.java
│   │   └── booking/
│   │       └── SimpleBookingTest.java
│   └── toolbox/
│       ├── Data.java
│       ├── GenericMethods.java
│       └── Report.java
└── reports/
    └── screenshots/
```

### Running Tests

To run the tests, you can use the following command:

```bash
./gradlew test -Dsuite=src/suite/feature/booking/simple-booking-test.xml
```

This will run the SimpleBookingTest, which logs in as staff and navigates to the booking engine.

### Maintaining the Framework

When adding new tests to the framework, follow these guidelines:

1. **Create page objects** for each page in the application
2. **Create test data files** for test data
3. **Create test classes** that extend BaseTest
4. **Create TestNG XML suite files** for running the tests
5. **Document the tests** in this guide

### Future Cleanup Tasks

If you need to perform further cleanup of the framework, consider the following tasks:

1. **Remove unused page objects** that are not used by any tests
2. **Remove unused test data files** that are not used by any tests
3. **Remove unused utility classes** that are not used by any tests
4. **Update the build.gradle file** to remove unused dependencies

## Recommendations for Future Improvements

Based on our experience with the TestNG framework, we recommend the following improvements:

1. **Default Parameter Values**: Update the BaseTest class to provide default values for all required parameters, making tests more resilient when run outside of XML suites:
   ```java
   @Parameters({"browserToUse", "headless", "environment", "testData", "dataToUse", "testSuiteName"})
   @BeforeTest(alwaysRun=true)
   public void BeforeTest(
       @Optional("chrome") String browserToUse,
       @Optional("true") String headless,
       @Optional("http://localhost:58000") String environment,
       @Optional("TD1") String testData,
       @Optional("random") String dataToUse,
       @Optional("adhoc/staff-login") String testSuiteName) {
       // Method implementation
   }
   ```

2. **Simplified Test Structure**: Consider creating a more streamlined test structure that doesn't rely heavily on XML files for configuration. This would make it easier to run individual tests:
   ```java
   public class SimpleLoginTest {
       @Test
       public void testLogin() {
           // Test implementation that doesn't require XML configuration
       }
   }
   ```

3. **Centralized Screenshot Handling**: Create a utility class for taking screenshots to avoid duplicating code across test classes:
   ```java
   public class ScreenshotUtil {
       public static void takeScreenshot(WebDriver driver, String name, Report report) throws IOException {
           // Screenshot implementation
       }
   }
   ```

4. **Improved Reporting**: Enhance the reporting mechanism to include more details about test execution, such as environment information, test parameters, and execution time:
   ```java
   public class EnhancedReport extends Report {
       public void logEnvironmentInfo(String browser, String environment) {
           appendExport("INFO: Running tests on " + browser + " against " + environment);
       }
   }
   ```

5. **Parallel Test Execution**: Configure the framework to run tests in parallel to reduce execution time:
   ```xml
   <suite name="Parallel Test Suite" parallel="tests" thread-count="3">
       <!-- Test configurations -->
   </suite>
   ```

6. **CI/CD Integration**: Add configuration for running tests in a CI/CD pipeline, such as GitHub Actions or Jenkins:
   ```yaml
   # Example GitHub Actions workflow
   name: TestNG Tests
   on: [push, pull_request]
   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v2
         - name: Set up JDK
           uses: actions/setup-java@v2
           with:
             java-version: '11'
         - name: Run tests
           run: ./gradlew test
   ```

7. **Test Data Management**: Implement a more flexible test data management system that can handle different environments and test scenarios:
   ```java
   public class TestDataManager {
       public static JSONObject getTestData(String testName, String environment) {
           // Load and return test data based on test name and environment
       }
   }
   ```

8. **API Testing Integration**: Extend the framework to support API testing using RestAssured or similar libraries:
   ```java
   public class APITest extends BaseTest {
       @Test
       public void testAPI() {
           given()
               .baseUri(getEnvironment())
               .when()
               .get("/api/endpoint")
               .then()
               .statusCode(200);
       }
   }
   ```

By implementing these recommendations, the TestNG framework will become more robust, flexible, and easier to use for all types of testing scenarios.