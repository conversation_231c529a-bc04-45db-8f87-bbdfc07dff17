# Gradle Troubleshooting Guide

This guide provides solutions for common Gradle issues encountered in the TestNG automation framework.

## Table of Contents

1. [Common Gradle Issues](#common-gradle-issues)
   - ["No connection to gradle server" Error](#no-connection-to-gradle-server-error)
   - [Gradle Build Failures](#gradle-build-failures)
   - [Out of Memory Errors](#out-of-memory-errors)
   - [Slow Gradle Builds](#slow-gradle-builds)
   - [Dependency Conflicts](#dependency-conflicts)
2. [Gradle Configuration](#gradle-configuration)
   - [gradle.properties](#gradleproperties)
   - [Gradle Wrapper](#gradle-wrapper)
3. [Debugging Gradle](#debugging-gradle)
4. [Updating Gradle](#updating-gradle)
5. [IDE Integration](#ide-integration)

## Common Gradle Issues

### Gradle Deprecation Warnings

**Symptoms:**
- Warning message: "Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0."
- Message appears when running Gradle tasks in IntelliJ IDEA or from the command line
- Build completes successfully despite the warning

**Causes:**
- Using deprecated Gradle features or APIs
- Using plugins that use deprecated Gradle features
- Using older Gradle versions with newer plugins or dependencies

**Solutions:**

1. **View Detailed Deprecation Warnings**
   ```bash
   # Run with detailed warnings
   ./gradlew build --warning-mode all
   ```

2. **Update Gradle Version**
   ```bash
   # Update Gradle wrapper to a newer version
   ./gradlew wrapper --gradle-version=8.4 --distribution-type=bin
   ```

3. **Update Plugins and Dependencies**
   - Check for newer versions of plugins in your build.gradle file
   - Update dependencies to versions compatible with your Gradle version

4. **Ignore Warnings Temporarily**
   - If the build works correctly, you can continue using it while planning for future updates
   - Add a note in your documentation about the planned upgrade path

5. **Consult Gradle Upgrade Guide**
   - Review the [Gradle Upgrade Guide](https://docs.gradle.org/current/userguide/upgrading_version_8.html) for specific migration steps
   - Look for deprecated features mentioned in the detailed warnings

### "No connection to gradle server" Error

**Symptoms:**
- Error message: "No connection to gradle server. Try restarting the server."
- IDE shows a notification with a "Restart" button
- Gradle tasks fail to run from the IDE

**Causes:**
- The Gradle daemon has timed out due to inactivity
- Memory issues with the Gradle daemon
- Conflicting processes
- IDE configuration issues
- Gradle version mismatch

**Solutions:**

1. **Restart the Gradle Server**
   - Click the "Restart" button in the error notification
   - This is often the quickest and simplest solution

2. **Kill Existing Gradle Processes**
   ```bash
   # On Linux/Mac
   pkill -f gradle

   # On Windows (in Command Prompt as Administrator)
   taskkill /F /IM gradle.exe
   ```

3. **Invalidate Caches and Restart the IDE**
   - In your IDE, go to File > Invalidate Caches / Restart
   - Select "Invalidate and Restart"
   - Wait for the IDE to restart and reload your project

4. **Check Gradle Settings in IDE**
   - Go to File > Settings (or Preferences) > Build, Execution, Deployment > Build Tools > Gradle
   - Ensure "Build and run using" and "Run tests using" are both set to "Gradle"
   - Make sure the Gradle JVM is set to a compatible version

5. **Update Gradle Wrapper**
   ```bash
   # Navigate to your project directory
   cd /var/www/test-automation

   # Update Gradle wrapper
   ./gradlew wrapper --gradle-version=7.6 --distribution-type=bin
   ```

### Gradle Build Failures

**Symptoms:**
- Build fails with errors
- Tests fail to compile or run
- Dependency resolution errors

**Solutions:**

1. **Clean the Project**
   ```bash
   ./gradlew clean
   ```

2. **Refresh Dependencies**
   ```bash
   ./gradlew --refresh-dependencies
   ```

3. **Check for Dependency Conflicts**
   ```bash
   ./gradlew dependencies
   ```

4. **Add Resolution Strategy**
   Add this to your build.gradle:
   ```gradle
   configurations.all {
       resolutionStrategy {
           force 'com.google.guava:guava:32.1.2-jre'
           // Add other forced versions as needed
       }
   }
   ```

### Out of Memory Errors

**Symptoms:**
- "Java heap space" or "GC overhead limit exceeded" errors
- Gradle process terminates unexpectedly
- Build fails on large projects

**Solutions:**

1. **Increase Memory in gradle.properties**
   ```properties
   org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError
   ```

2. **Run with Explicit Memory Settings**
   ```bash
   ./gradlew test -Dorg.gradle.jvmargs="-Xmx2048m"
   ```

3. **Enable Gradle Daemon**
   ```properties
   org.gradle.daemon=true
   ```

### Slow Gradle Builds

**Symptoms:**
- Builds take a long time to complete
- Tests run slowly
- IDE becomes unresponsive during builds

**Solutions:**

1. **Enable Gradle Daemon**
   ```properties
   org.gradle.daemon=true
   ```

2. **Enable Parallel Execution**
   ```properties
   org.gradle.parallel=true
   ```

3. **Enable Build Cache**
   ```properties
   org.gradle.caching=true
   ```

4. **Use Incremental Builds**
   ```properties
   org.gradle.configureondemand=true
   ```

5. **Run with Daemon Flag**
   ```bash
   ./gradlew --daemon test
   ```

### Dependency Conflicts

**Symptoms:**
- "Duplicate class" errors
- "Cannot find class" errors
- Version conflicts between dependencies
- "Cannot have two attributes with the same name but different types" error
- JVM environment attribute conflicts

**Solutions:**

1. **View Dependency Tree**
   ```bash
   ./gradlew dependencies
   ```

2. **Force Specific Versions**
   ```gradle
   configurations.all {
       resolutionStrategy {
           force 'com.google.guava:guava:32.1.2-jre'
           force 'org.seleniumhq.selenium:selenium-java:4.15.0'
           // Add other forced versions as needed
       }
   }
   ```

3. **Exclude Transitive Dependencies**
   ```gradle
   dependencies {
       implementation("org.seleniumhq.selenium:selenium-java:4.15.0") {
           exclude group: 'com.google.guava', module: 'guava'
       }
   }
   ```

4. **Fix JVM Environment Attribute Conflicts**

   If you encounter the error "Cannot have two attributes with the same name but different types", especially related to JVM environment attributes, try simplifying the attributes section in your build.gradle:

   **Original problematic code:**
   ```gradle
   configurations.all {
       resolutionStrategy {
           force 'com.google.guava:guava:32.1.2-jre'
           // Specify JVM environment attribute to help Gradle choose the correct variant
           attributes {
               attribute(org.gradle.api.attributes.java.TargetJvmEnvironment.TARGET_JVM_ENVIRONMENT_ATTRIBUTE, org.gradle.api.attributes.java.TargetJvmEnvironment.STANDARD_JVM)
           }
       }
   }
   ```

   **Fixed code:**
   ```gradle
   configurations.all {
       resolutionStrategy {
           force 'com.google.guava:guava:32.1.2-jre'
           // Remove the attributes section as it's causing conflicts
           // We'll use a different approach to handle dependency resolution
       }
   }
   ```

   This simplification removes the explicit JVM environment attribute specification, allowing Gradle to use its default attribute handling, which often resolves the conflict.

## Gradle Configuration

### gradle.properties

Create a `gradle.properties` file in the root of your project with the following content:

```properties
# Gradle memory settings
org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# Gradle daemon settings
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# Kotlin settings
kotlin.incremental=true
```

### Gradle Wrapper

Update the Gradle wrapper properties in `gradle/wrapper/gradle-wrapper.properties`:

```properties
distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\://services.gradle.org/distributions/gradle-7.6-bin.zip
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
```

## Debugging Gradle

To get more detailed logs for troubleshooting Gradle issues:

```bash
# Run with debug logging
./gradlew test --debug

# Run with info logging
./gradlew test --info

# Run with stacktrace
./gradlew test --stacktrace
```

To see which tasks are being executed:

```bash
./gradlew test --console=verbose
```

## Updating Gradle

If you're experiencing issues with an older Gradle version, update the wrapper:

```bash
# Update to Gradle 7.6
./gradlew wrapper --gradle-version=7.6 --distribution-type=bin
```

To check the current Gradle version:

```bash
./gradlew --version
```

## IDE Integration

### IntelliJ IDEA

1. **Gradle Settings**
   - Go to File > Settings > Build, Execution, Deployment > Build Tools > Gradle
   - Set "Build and run using" to "Gradle"
   - Set "Run tests using" to "Gradle"
   - Set "Gradle JVM" to a compatible version (Java 11 or higher)

2. **Refresh Gradle Project**
   - Right-click on the project in the Project view
   - Select "Refresh Gradle Project"

3. **Import Gradle Project**
   - If opening the project for the first time:
   - File > Open
   - Select the build.gradle file
   - Select "Open as Project"

4. **Run Gradle Tasks from IDE**
   - Open the Gradle tool window (View > Tool Windows > Gradle)
   - Navigate to the task you want to run
   - Double-click on the task to run it

### Eclipse

1. **Install Buildship Gradle Integration**
   - Help > Eclipse Marketplace
   - Search for "Buildship"
   - Install "Buildship Gradle Integration"

2. **Import Gradle Project**
   - File > Import
   - Gradle > Existing Gradle Project
   - Select the project directory
   - Click "Finish"

3. **Refresh Gradle Project**
   - Right-click on the project in the Project Explorer
   - Gradle > Refresh Gradle Project

4. **Run Gradle Tasks from IDE**
   - Open the Gradle Tasks view (Window > Show View > Other > Gradle > Gradle Tasks)
   - Navigate to the task you want to run
   - Double-click on the task to run it

## Conclusion

By following this guide, you should be able to resolve common Gradle issues in the TestNG automation framework. If you encounter any issues not covered in this guide, please refer to the [Gradle documentation](https://docs.gradle.org/) or seek help from the development team.
