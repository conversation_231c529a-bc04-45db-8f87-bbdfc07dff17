# Dynamic Reseeding Guide

This document provides a comprehensive guide to the dynamic reseeding process used in the Cypress test automation framework. It explains what reseeding is, why it's important, how it works, and best practices for using it effectively.

## Table of Contents

1. [What is Reseeding?](#what-is-reseeding)
2. [Why Dynamic Reseeding?](#why-dynamic-reseeding)
3. [How Dynamic Reseeding Works](#how-dynamic-reseeding-works)
4. [The Dynamic Reseed Command](#the-dynamic-reseed-command)
5. [Using Dynamic Reseeding in Tests](#using-dynamic-reseeding-in-tests)
6. [Reseeding Implementation Details](#reseeding-implementation-details)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## What is Reseeding?

Reseeding is the process of resetting the test environment to a known state before running a test. This includes:

- Creating or resetting hotel data
- Setting up room types and rate plans
- Creating test users with specific permissions
- Configuring payment gateways
- Setting up any other data required for tests

The reseeding process ensures that each test starts with a clean, predictable environment, which is essential for test reliability and independence.

## Why Dynamic Reseeding?

The dynamic reseeding approach was implemented to solve several challenges:

1. **Changing URLs**: When using ngrok for local development or testing, the URL changes frequently. Hardcoding these URLs in tests leads to failures when the URL changes.

2. **Environment Flexibility**: Tests need to run against different environments (local, development, UAT, production) without code changes.

3. **Test Independence**: Each test should have its own isolated data to prevent interference between tests.

4. **Reliability**: Tests should be resilient to changes in the environment and infrastructure.

Before implementing dynamic reseeding, tests would fail with errors like:

```
cy.request() failed on:

https://fc2e-149-22-177-47.ngrok-free.app/automation/tests/reseedHotel/cypress-seeded-hotel-6
```

This happened because the tests were using a hardcoded ngrok URL that had changed.

## How Dynamic Reseeding Works

The dynamic reseeding solution works by:

1. Using the current `baseUrl` from the Cypress configuration
2. Combining it with the reseeding endpoint path
3. Making a request to reset the test environment
4. Handling any errors gracefully

This ensures that the reseeding URL is always correct, even when the ngrok URL or environment changes.

## The Dynamic Reseed Command

The dynamic reseeding functionality is implemented as a custom Cypress command:

```javascript
// cypress/support/commands/dynamicReseed.js

/**
 * A dynamic reseeding command that uses the current baseUrl
 * This ensures that the reseeding URL is always correct, even when the ngrok URL changes
 */

/**
 * Reseed a hotel using the current baseUrl
 * @param {string} hotelSlug - The slug of the hotel to reseed
 * @param {Object} options - Additional options for the request
 * @returns {Cypress.Chainable} - The Cypress chainable
 */
Cypress.Commands.add('dynamicReseed', (hotelSlug, options = {}) => {
  // Get the current baseUrl from Cypress config
  const baseUrl = Cypress.config('baseUrl');
  
  // Combine with the default options
  const requestOptions = {
    url: `automation/tests/reseedHotel/${hotelSlug}`,
    failOnStatusCode: false,
    timeout: 180000,
    ...options
  };
  
  // Log the reseeding attempt
  cy.log(`Reseeding hotel ${hotelSlug} using baseUrl: ${baseUrl}`);
  
  // Make the request
  return cy.request(requestOptions);
});
```

This command is imported in the `cypress/support/e2e.js` file:

```javascript
// cypress/support/e2e.js

// COMMANDS
import './commands/cancelBookings'
import './commands/getIFrame'
import './commands/overwrites'
import './commands/dynamicReseed'  // Import the dynamic reseed command
import 'cypress-iframe'
import './commands/jira'
```

## Using Dynamic Reseeding in Tests

To use dynamic reseeding in a test, simply call the `dynamicReseed` command with the hotel slug:

```javascript
// Before running the test, reseed the hotel
before('setup test environment', () => {
  cy.dynamicReseed(hotelSlug);
  cy.clearCookies();
  cy.clearLocalStorage();
});
```

For tests that don't use the `before` hook, you can call the command directly:

```javascript
it('should perform a specific action', () => {
  cy.dynamicReseed(hotelSlug);
  // Test steps...
});
```

## Reseeding Implementation Details

### Backend Implementation

The reseeding endpoint (`/automation/tests/reseedHotel/{hotelSlug}`) is implemented in the Laravel backend. When called, it:

1. Identifies the hotel based on the provided slug
2. Resets the hotel data to a known state
3. Creates or updates test users, rooms, rates, and other entities
4. Sets up specific test scenarios needed for the Cypress tests

### Hotel Slugs

The hotel slugs used for reseeding are defined in the `accounts.js` fixture file:

```javascript
// cypress/fixtures/accounts.js

export default {
  'cypressHotel1': {
    'title': ',',
    'slug': 'cypress-seeded-hotel-1',
    'name': 'Hotel 1',
    // Other properties...
  },
  'cypressHotel2': {
    'title': ',',
    'slug': 'cypress-seeded-hotel-2',
    'name': 'Hotel 2',
    // Other properties...
  },
  // Other hotels...
}
```

Each hotel is configured with specific test data to support different test scenarios.

### Environment Configuration

The baseUrl used by the dynamic reseeding command is defined in the environment-specific configuration files:

```json
// cypress/config/local.json
{
    "baseUrl": "https://b0f9-88-97-222-230.ngrok-free.app",
    "video": false,
    "env": {
        "staff_name": "Chris Maggs",
        "staff_email": "<EMAIL>",
        "staff_password": "Password123?",
        "environment": "Local",
        "zonalAPIUsername": "1ee2ac1e-bc10-4f3c-8274-8b6a4800d605",
        "zonalAPIPassword": "EPlU5dydASSgOIn0"
    }
}
```

## Best Practices

### 1. Always Use Dynamic Reseeding

Always use the `dynamicReseed` command instead of direct `cy.request` calls to the reseeding endpoint:

```javascript
// Good
cy.dynamicReseed(hotelSlug);

// Avoid
cy.request(`automation/tests/reseedHotel/${hotelSlug}`);
```

### 2. Reseed at the Beginning of Tests

Reseed the hotel at the beginning of your test or test suite to ensure a clean environment:

```javascript
before('setup', () => {
  cy.dynamicReseed(hotelSlug);
  cy.clearCookies();
  cy.clearLocalStorage();
});
```

### 3. Use the Correct Hotel Slug

Use the appropriate hotel slug for your test scenario:

```javascript
// Import the accounts fixture
import accounts from "../../../fixtures/accounts";

// Use the appropriate hotel slug
const hotelSlug = accounts.cypressHotel4.slug;
```

### 4. Handle Long Reseeding Times

Reseeding can take time, especially for complex hotel configurations. Be patient and consider increasing the timeout if needed:

```javascript
cy.dynamicReseed(hotelSlug, { timeout: 300000 }); // 5 minutes
```

### 5. Check for Reseeding Success

If your test depends on specific data being created during reseeding, verify that the data exists before proceeding:

```javascript
cy.dynamicReseed(hotelSlug).then(() => {
  // Verify that the required data exists
  cy.visit(`/hotels/${hotelSlug}/rooms`);
  cy.contains('Standard Room').should('exist');
});
```

## Troubleshooting

### Common Issues

1. **Reseeding Timeout**: If reseeding takes too long, increase the timeout:

   ```javascript
   cy.dynamicReseed(hotelSlug, { timeout: 300000 }); // 5 minutes
   ```

2. **404 Not Found**: If the reseeding endpoint returns a 404, check that:
   - The hotel slug is correct
   - The baseUrl is correct
   - The reseeding endpoint exists on the server

3. **500 Server Error**: If the reseeding endpoint returns a 500, check the server logs for details.

4. **Incorrect Data**: If the reseeded data is not what you expect, check:
   - The hotel slug is correct
   - The reseeding endpoint is working correctly
   - The test is not modifying the data after reseeding

### Debugging

To debug reseeding issues, you can:

1. **Check the Cypress Logs**: The `dynamicReseed` command logs the reseeding attempt, which can help identify issues.

2. **Inspect the Request**: Use the Cypress Network tab to inspect the reseeding request and response.

3. **Check the Server Logs**: Check the server logs for any errors during reseeding.

4. **Test the Endpoint Directly**: Test the reseeding endpoint directly using a tool like Postman or curl.

## Conclusion

Dynamic reseeding is a critical component of the test automation framework, ensuring that tests run reliably across different environments. By using the `dynamicReseed` command and following the best practices outlined in this guide, you can create more robust and reliable tests that are resilient to changes in the environment.
