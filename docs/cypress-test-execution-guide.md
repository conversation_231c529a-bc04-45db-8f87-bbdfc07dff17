# Cypress Test Execution Guide

This guide provides comprehensive instructions for running Cypress tests in various configurations, including running individual tests in headless mode.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Running Tests](#running-tests)
   - [Running All Tests](#running-all-tests)
   - [Running Individual Tests](#running-individual-tests)
   - [Running Tests in Headless Mode](#running-tests-in-headless-mode)
   - [Running Tests in Different Environments](#running-tests-in-different-environments)
3. [Test Filtering](#test-filtering)
4. [Debugging Tests](#debugging-tests)
5. [Common Issues and Solutions](#common-issues-and-solutions)

## Prerequisites

Before running Cypress tests, ensure you have:

1. Node.js installed (v14 or later recommended)
2. The project dependencies installed:
   ```bash
   npm install
   ```
3. Proper environment configuration in `cypress.config.js` or environment-specific JSON files

## Running Tests

### Running All Tests

To run all tests in the Cypress Test Runner (interactive mode):

```bash
npx cypress open
```

To run all tests in headless mode:

```bash
npx cypress run
```

### Running Individual Tests

#### Running a Specific Test File in Interactive Mode

To run a specific test file in the Cypress Test Runner:

```bash
npx cypress open --spec "cypress/e2e/path/to/test.spec.cy.js"
```

For example:

```bash
npx cypress open --spec "cypress/e2e/booking-engine/B1 D0 guestNoLoginDepositPartial/B1 D1 rateRestrictionsAny/B1:D2 guestRateRestrictionsMinStay.spec.cy.js"
```

#### Running a Specific Test File in Headless Mode

To run a specific test file in headless mode:

```bash
npx cypress run --spec "cypress/e2e/path/to/test.spec.cy.js"
```

For example:

```bash
npx cypress run --spec "cypress/e2e/booking-engine/B1 D0 guestNoLoginDepositPartial/B1 D1 rateRestrictionsAny/B1:D2 guestRateRestrictionsMinStay.spec.cy.js"
```

> **Note**: If your test file path contains special characters like colons (:), you may need to escape them with a backslash:
> 
> ```bash
> npx cypress run --spec "cypress/e2e/booking-engine/B1 D0 guestNoLoginDepositPartial/B1 D1 rateRestrictionsAny/B1\:D2 guestRateRestrictionsMinStay.spec.cy.js"
> ```

#### Running Multiple Specific Test Files

To run multiple specific test files:

```bash
npx cypress run --spec "cypress/e2e/path/to/test1.spec.cy.js,cypress/e2e/path/to/test2.spec.cy.js"
```

#### Running Tests by Pattern

To run tests matching a pattern:

```bash
npx cypress run --spec "cypress/e2e/**/*login*.spec.cy.js"
```

### Running Tests in Headless Mode

Headless mode runs tests without opening a browser window, which is useful for CI/CD pipelines or batch test execution.

#### Running All Tests in Headless Mode

```bash
npx cypress run
```

#### Running a Specific Test in Headless Mode

```bash
npx cypress run --spec "cypress/e2e/path/to/test.spec.cy.js"
```

#### Running Tests in Headless Mode with a Specific Browser

```bash
npx cypress run --browser chrome --spec "cypress/e2e/path/to/test.spec.cy.js"
```

Available browser options:
- `chrome`
- `firefox`
- `edge`
- `electron` (default)

### Running Tests in Different Environments

To run tests in a specific environment (e.g., local, development, UAT, production):

```bash
npx cypress run --env environmentName=uat --spec "cypress/e2e/path/to/test.spec.cy.js"
```

Common environment options:
- `local`
- `dev`
- `uat`
- `prod`

For example, to run a specific test in the UAT environment in headless mode:

```bash
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/B1 D0 guestNoLoginDepositPartial/B1 D1 rateRestrictionsAny/B1:D2 guestRateRestrictionsMinStay.spec.cy.js"
```

## Test Filtering

Tests can be filtered using the `TestFilters` utility:

```javascript
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {
    describe('My Test Suite', () => {
        // Test implementation
    });
});
```

To run tests with specific filters:

```bash
npx cypress run --env grepTags=P2Sanity
```

## Debugging Tests

### Running Tests with Browser UI Visible

To run tests with the browser UI visible (non-headless):

```bash
npx cypress run --headed --spec "cypress/e2e/path/to/test.spec.cy.js"
```

### Slowing Down Test Execution

To slow down test execution for debugging:

```bash
npx cypress run --config defaultCommandTimeout=10000,responseTimeout=30000 --spec "cypress/e2e/path/to/test.spec.cy.js"
```

### Pausing Test Execution

You can add `cy.pause()` in your test code to pause execution at specific points:

```javascript
it('should complete a booking', () => {
    cy.visit('/booking');
    cy.get('#date-picker').click();
    cy.pause(); // Test will pause here
    cy.get('.date-cell').first().click();
});
```

### Generating Videos and Screenshots

To generate videos of test runs:

```bash
npx cypress run --spec "cypress/e2e/path/to/test.spec.cy.js" --config video=true
```

To generate screenshots on test failure:

```bash
npx cypress run --spec "cypress/e2e/path/to/test.spec.cy.js" --config screenshotOnRunFailure=true
```

## Common Issues and Solutions

### Handling Timeouts

If tests are timing out, increase the timeout values:

```bash
npx cypress run --config defaultCommandTimeout=10000,responseTimeout=30000 --spec "cypress/e2e/path/to/test.spec.cy.js"
```

### Handling Flaky Tests

For flaky tests, you can configure automatic retries:

```bash
npx cypress run --config retries=2 --spec "cypress/e2e/path/to/test.spec.cy.js"
```

### Handling Special Characters in File Paths

If your test file path contains special characters (like colons), escape them with a backslash:

```bash
npx cypress run --spec "cypress/e2e/path/with/special\:characters.spec.cy.js"
```

### Handling Browser Crashes

If the browser crashes during tests, try running with a different browser:

```bash
npx cypress run --browser firefox --spec "cypress/e2e/path/to/test.spec.cy.js"
```

## Examples

### Example 1: Run a Specific Test in UAT Environment in Headless Mode

```bash
npx cypress run --env environmentName=uat --spec "cypress/e2e/booking-engine/B1 D0 guestNoLoginDepositPartial/B1 D1 rateRestrictionsAny/B1\:D2 guestRateRestrictionsMinStay.spec.cy.js"
```

### Example 2: Run All Booking Engine Tests in Chrome

```bash
npx cypress run --browser chrome --spec "cypress/e2e/booking-engine/**/*.spec.cy.js"
```

### Example 3: Run High-Priority Tests in Development Environment

```bash
npx cypress run --env environmentName=dev,grepTags=P1
```

### Example 4: Debug a Specific Test with UI Visible

```bash
npx cypress run --headed --config defaultCommandTimeout=10000 --spec "cypress/e2e/path/to/test.spec.cy.js"
```
